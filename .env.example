# ==============================================
# EDUSYS API - CONFIGURAÇÕES DE AMBIENTE
# ==============================================

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=seu_usuario
DATABASE_PASSWORD=sua_senha
DATABASE_NAME=edusys

# ==============================================
# JWT CONFIGURATION
# ==============================================
JWT_SECRET=seu_jwt_secret_super_seguro_aqui_com_pelo_menos_32_caracteres
JWT_EXPIRES_IN=24h

# ==============================================
# STRIPE CONFIGURATION
# ==============================================
# Chaves do Stripe (obtenha em https://dashboard.stripe.com/apikeys)
STRIPE_SECRET_KEY=sk_test_51...
STRIPE_PUBLISHABLE_KEY=pk_test_51...

# Webhook secret (obtenha em https://dashboard.stripe.com/webhooks)
STRIPE_WEBHOOK_SECRET=whsec_...

# Configurações de pagamento
STRIPE_CURRENCY=BRL
STRIPE_SUCCESS_URL=http://localhost:3000/payment/success
STRIPE_CANCEL_URL=http://localhost:3000/payment/cancel

# ==============================================
# EMAIL CONFIGURATION (OPCIONAL)
# ==============================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=sua_senha_de_app

# Configurações de email
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=EduSys

# ==============================================
# APPLICATION CONFIGURATION
# ==============================================
NODE_ENV=development
PORT=3000
API_PREFIX=api/v1

# URL base da aplicação
APP_URL=http://localhost:3000
FRONTEND_URL=http://localhost:3001

# ==============================================
# SECURITY CONFIGURATION
# ==============================================
# Chave para criptografia de dados sensíveis
ENCRYPTION_KEY=sua_chave_de_criptografia_de_32_caracteres

# Rate limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# ==============================================
# FILE UPLOAD CONFIGURATION
# ==============================================
MAX_FILE_SIZE=5242880
UPLOAD_DEST=./uploads

# ==============================================
# LOGGING CONFIGURATION
# ==============================================
LOG_LEVEL=info
LOG_FILE=logs/app.log

# ==============================================
# REDIS CONFIGURATION (OPCIONAL)
# ==============================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ==============================================
# MONITORING CONFIGURATION (OPCIONAL)
# ==============================================
SENTRY_DSN=
NEW_RELIC_LICENSE_KEY=

# ==============================================
# DEVELOPMENT CONFIGURATION
# ==============================================
# Swagger
SWAGGER_ENABLED=true
SWAGGER_PATH=api/docs

# Debug
DEBUG_MODE=true
VERBOSE_LOGGING=true
