# 🗄️ **Migrations do Módulo Educacional - EduSys**

## 📋 **Visão Geral**

Foram criadas 3 migrations fundamentais para o módulo educacional seguindo exatamente os padrões estabelecidos no projeto:

1. **CreateCoursesTable** - Tabela de cursos
2. **CreateClassesTable** - Tabela de turmas  
3. **CreateEnrollmentsTable** - Tabela de matrículas

---

## 🏗️ **Estrutura das Migrations**

### **1. CreateCoursesTable (1752065318281)**

#### **Schema:** `educational.courses`

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | UUID | Chave primária |
| `institution_id` | UUID | FK para institutions |
| `name` | VARCHAR(255) | Nome do curso |
| `description` | TEXT | Descrição detalhada |
| `duration_months` | INTEGER | Duração em meses |
| `price` | DECIMAL(10,2) | Preço do curso |
| `max_students` | INTEGER | Máximo de alunos |
| `status` | VARCHAR(20) | active/inactive/suspended |
| `start_date` | TIMESTAMP | Data de início |
| `end_date` | TIMESTAMP | Data de término |
| `created_by` | UUID | FK para users |
| `updated_by` | UUID | FK para users |
| `created_at` | TIMESTAMP | Data de criação |
| `updated_at` | TIMESTAMP | Data de atualização |

#### **Foreign Keys:**
- `institution_id` → `core.institutions(id)` ON DELETE CASCADE
- `created_by` → `core.users(id)` ON DELETE RESTRICT
- `updated_by` → `core.users(id)` ON DELETE RESTRICT

#### **Indexes:**
- `idx_courses_institution` - Busca por instituição
- `idx_courses_status` - Filtro por status
- `idx_courses_dates` - Busca por período

---

### **2. CreateClassesTable (1752065326222)**

#### **Schema:** `educational.classes`

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | UUID | Chave primária |
| `course_id` | UUID | FK para courses |
| `name` | VARCHAR(255) | Nome da turma |
| `description` | TEXT | Descrição da turma |
| `max_students` | INTEGER | Máximo de alunos |
| `current_students` | INTEGER | Alunos atuais (default: 0) |
| `status` | VARCHAR(20) | open/closed/in_progress/finished |
| `start_date` | TIMESTAMP | Data de início |
| `end_date` | TIMESTAMP | Data de término |
| `schedule` | JSONB | Horários e dias da semana |
| `created_by` | UUID | FK para users |
| `updated_by` | UUID | FK para users |
| `created_at` | TIMESTAMP | Data de criação |
| `updated_at` | TIMESTAMP | Data de atualização |

#### **Foreign Keys:**
- `course_id` → `educational.courses(id)` ON DELETE CASCADE
- `created_by` → `core.users(id)` ON DELETE RESTRICT
- `updated_by` → `core.users(id)` ON DELETE RESTRICT

#### **Indexes:**
- `idx_classes_course` - Busca por curso
- `idx_classes_status` - Filtro por status
- `idx_classes_dates` - Busca por período
- `idx_classes_students` - Controle de vagas

---

### **3. CreateEnrollmentsTable (1752065333854)**

#### **Schema:** `educational.enrollments`

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | UUID | Chave primária |
| `student_id` | UUID | FK para users (aluno) |
| `class_id` | UUID | FK para classes |
| `institution_id` | UUID | FK para institutions |
| `status` | VARCHAR(20) | pending/approved/rejected/active/suspended/completed |
| `enrollment_date` | TIMESTAMP | Data da inscrição |
| `approval_date` | TIMESTAMP | Data da aprovação |
| `approved_by` | UUID | FK para users (quem aprovou) |
| `rejection_reason` | TEXT | Motivo da rejeição |
| `notes` | TEXT | Observações |
| `created_by` | UUID | FK para users |
| `updated_by` | UUID | FK para users |
| `created_at` | TIMESTAMP | Data de criação |
| `updated_at` | TIMESTAMP | Data de atualização |

#### **Foreign Keys:**
- `student_id` → `core.users(id)` ON DELETE CASCADE
- `class_id` → `educational.classes(id)` ON DELETE CASCADE
- `institution_id` → `core.institutions(id)` ON DELETE CASCADE
- `approved_by` → `core.users(id)` ON DELETE RESTRICT
- `created_by` → `core.users(id)` ON DELETE RESTRICT
- `updated_by` → `core.users(id)` ON DELETE RESTRICT

#### **Indexes:**
- `idx_enrollments_student` - Busca por aluno
- `idx_enrollments_class` - Busca por turma
- `idx_enrollments_institution` - Busca por instituição
- `idx_enrollments_status` - Filtro por status
- `idx_enrollments_dates` - Busca por período
- `idx_enrollments_unique_student_class` - Evita matrículas duplicadas

#### **Constraint Único:**
```sql
CREATE UNIQUE INDEX idx_enrollments_unique_student_class 
ON educational.enrollments(student_id, class_id) 
WHERE status NOT IN ('rejected', 'cancelled');
```

---

## 🔄 **Relacionamentos**

```
Institution (1) ──→ (N) Course
Course (1) ──→ (N) Class  
Class (1) ──→ (N) Enrollment
User (1) ──→ (N) Enrollment (como student)
User (1) ──→ (N) Enrollment (como approved_by)
```

---

## 🎯 **Status Definidos**

### **Course Status:**
- `active` - Curso ativo e disponível
- `inactive` - Curso inativo
- `suspended` - Curso suspenso temporariamente

### **Class Status:**
- `open` - Turma aberta para matrículas
- `closed` - Turma fechada para matrículas
- `in_progress` - Turma em andamento
- `finished` - Turma finalizada

### **Enrollment Status:**
- `pending` - Aguardando aprovação
- `approved` - Aprovada pela secretaria
- `rejected` - Rejeitada pela secretaria
- `active` - Matrícula ativa (após pagamento)
- `suspended` - Matrícula suspensa
- `completed` - Curso concluído

---

## 🚀 **Como Executar as Migrations**

### **1. Executar todas as migrations:**
```bash
yarn run:migration
```

### **2. Reverter última migration:**
```bash
yarn revert:migration
```

### **3. Verificar status:**
```bash
yarn typeorm migration:show
```

---

## 📊 **Exemplo de Dados**

### **Course:**
```json
{
  "id": "uuid-curso",
  "institution_id": "uuid-instituicao",
  "name": "Desenvolvimento Web Full Stack",
  "description": "Curso completo de desenvolvimento web",
  "duration_months": 12,
  "price": 2500.00,
  "max_students": 30,
  "status": "active",
  "start_date": "2024-08-01T00:00:00Z",
  "end_date": "2025-07-31T23:59:59Z"
}
```

### **Class:**
```json
{
  "id": "uuid-turma",
  "course_id": "uuid-curso",
  "name": "Turma 2024.2 - Noturno",
  "max_students": 25,
  "current_students": 0,
  "status": "open",
  "schedule": {
    "days": ["monday", "wednesday", "friday"],
    "start_time": "19:00",
    "end_time": "22:00"
  }
}
```

### **Enrollment:**
```json
{
  "id": "uuid-matricula",
  "student_id": "uuid-aluno",
  "class_id": "uuid-turma",
  "institution_id": "uuid-instituicao",
  "status": "pending",
  "enrollment_date": "2024-07-09T10:30:00Z"
}
```

---

## ✅ **Validações Implementadas**

1. **Integridade Referencial** - Todas as FKs configuradas
2. **Prevenção de Duplicatas** - Constraint único em enrollments
3. **Indexes Otimizados** - Para consultas frequentes
4. **Cascade Apropriado** - DELETE CASCADE onde necessário
5. **Auditoria Completa** - Campos created_by/updated_by

---

**🎉 Migrations do módulo educacional criadas com sucesso seguindo todos os padrões do projeto!**
