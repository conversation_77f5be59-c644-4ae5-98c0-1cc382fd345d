{"info": {"_postman_id": "edusys-api-collection", "name": "EduSys API - Sistema de Gestão Educacional", "description": "Collection completa para testar a API do EduSys - Sistema de Gestão Educacional\n\n**Funcionalidades:**\n- Autenticação JWT\n- Gestão de Usuários e Permissões\n- Sistema Educacional (Cursos, Turmas, Matrículas)\n- Controle de Frequência e Notas\n- Módulo Financeiro\n- Sistema de Assinaturas\n\n**Como usar:**\n1. Configure as variáveis de ambiente\n2. Execute o login para obter o token\n3. O token será automaticamente usado nas outras requisições", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Script global para todas as requisições", "const accessToken = pm.environment.get('access_token');", "", "// Log do token atual (apenas primeiros caracteres por segurança)", "if (accessToken) {", "    console.log('Token atual:', accessToken.substring(0, 20) + '...');", "} else {", "    console.log('Nenhum token encontrado. Faça login primeiro.');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Script global para todas as respostas", "", "// Log da resposta", "console.log('Status:', pm.response.code);", "console.log('Tempo de resposta:', pm.response.responseTime + 'ms');", "", "// Verificar se token expirou", "if (pm.response.code === 401) {", "    console.log('Token expirado ou inválido. Faça login novamente.');", "    pm.environment.unset('access_token');", "    pm.environment.unset('token_expiry');", "}", "", "// Testes básicos para todas as requisições", "pm.test('Status code is not 500', function () {", "    pm.expect(pm.response.code).to.not.equal(500);", "});", "", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});"]}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "institution_id", "value": "", "type": "string"}], "item": [{"name": "🔐 Autenticação", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.accessToken);", "    pm.environment.set('refresh_token', response.refreshToken);", "    pm.environment.set('user_id', response.user.id);", "    pm.environment.set('institution_id', response.user.institution_id);", "    ", "    // Calcular expiração do token (15 minutos)", "    const expiry = Date.now() + (15 * 60 * 1000);", "    pm.environment.set('token_expiry', expiry);", "    ", "    console.log('Login realizado com sucesso!');", "    console.log('Token salvo:', response.accessToken.substring(0, 20) + '...');", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}, "description": "Fazer login e obter tokens de acesso"}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}, "description": "Fazer logout e invalidar tokens"}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.accessToken);", "    ", "    const expiry = Date.now() + (15 * 60 * 1000);", "    pm.environment.set('token_expiry', expiry);", "    ", "    console.log('Token renovado com sucesso!');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}, "description": "<PERSON><PERSON> token de acesso usando refresh token"}, "response": []}, {"name": "<PERSON><PERSON><PERSON> (Primeiro <PERSON>)", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"first-access-token\",\n  \"password\": \"newPassword123\",\n  \"confirmPassword\": \"newPassword123\"\n}"}, "url": {"raw": "{{base_url}}/auth/set-password", "host": ["{{base_url}}"], "path": ["auth", "set-password"]}, "description": "Definir senha no primeiro acesso"}, "response": []}], "description": "Endpoints de autenticação e autorização"}, {"name": "👥 Gestão de Usuários", "item": [{"name": "Listar Usuários", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}, "description": "Listar todos os usuários (apenas ADMIN)"}, "response": []}, {"name": "Buscar Usuário por ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["user", "{{user_id}}"]}, "description": "Buscar usuário específico por ID"}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"(11) 99999-9999\",\n  \"document\": \"123.456.789-00\",\n  \"birth_date\": \"1990-01-15\",\n  \"institution_id\": \"{{institution_id}}\"\n}"}, "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}, "description": "Criar novo usuário (apenas ADMIN)"}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"phone\": \"(11) 88888-8888\"\n}"}, "url": {"raw": "{{base_url}}/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["user", "{{user_id}}"]}, "description": "Atualizar dados do usuário"}, "response": []}, {"name": "<PERSON>eta<PERSON>", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["user", "{{user_id}}"]}, "description": "Deletar usuário (apenas ADMIN)"}, "response": []}], "description": "Gestão de usuários do sistema"}, {"name": "🎓 Sistema Educacional", "item": [{"name": "📚 Cursos", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/course", "host": ["{{base_url}}"], "path": ["course"]}, "description": "Listar todos os cursos da instituição"}, "response": []}, {"name": "Buscar Curso por ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/course/:id", "host": ["{{base_url}}"], "path": ["course", ":id"], "variable": [{"key": "id", "value": "course-uuid"}]}, "description": "Buscar curso específico por ID"}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Desenvolvimento Web Full Stack\",\n  \"description\": \"Curso completo de desenvolvimento web com React e Node.js\",\n  \"duration_months\": 12,\n  \"price\": 2999.99,\n  \"max_students\": 30,\n  \"start_date\": \"2024-02-01T00:00:00.000Z\",\n  \"end_date\": \"2025-01-31T23:59:59.000Z\"\n}"}, "url": {"raw": "{{base_url}}/course", "host": ["{{base_url}}"], "path": ["course"]}, "description": "Criar novo curso"}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Desenvolvimento Web Full Stack - Atualizado\",\n  \"price\": 3299.99\n}"}, "url": {"raw": "{{base_url}}/course/:id", "host": ["{{base_url}}"], "path": ["course", ":id"], "variable": [{"key": "id", "value": "course-uuid"}]}, "description": "Atualizar dados do curso"}, "response": []}, {"name": "Deletar C<PERSON>o", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/course/:id", "host": ["{{base_url}}"], "path": ["course", ":id"], "variable": [{"key": "id", "value": "course-uuid"}]}, "description": "Deletar curso"}, "response": []}], "description": "Gestão de cursos"}, {"name": "🏫 Turmas", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/class", "host": ["{{base_url}}"], "path": ["class"]}, "description": "<PERSON>ar todas as turmas"}, "response": []}, {"name": "Buscar Turmas por Curso", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/class/course/:courseId", "host": ["{{base_url}}"], "path": ["class", "course", ":courseId"], "variable": [{"key": "courseId", "value": "course-uuid"}]}, "description": "Buscar turmas de um curso específico"}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>rma A - Manhã\",\n  \"course_id\": \"course-uuid\",\n  \"start_date\": \"2024-02-01T00:00:00.000Z\",\n  \"end_date\": \"2025-01-31T23:59:59.000Z\",\n  \"schedule\": {\n    \"monday\": \"08:00-12:00\",\n    \"wednesday\": \"08:00-12:00\",\n    \"friday\": \"08:00-12:00\"\n  },\n  \"max_students\": 25\n}"}, "url": {"raw": "{{base_url}}/class", "host": ["{{base_url}}"], "path": ["class"]}, "description": "Criar nova turma"}, "response": []}], "description": "Gestão de turmas"}, {"name": "📝 Matrículas", "item": [{"name": "Listar <PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/enrollment", "host": ["{{base_url}}"], "path": ["enrollment"]}, "description": "<PERSON>ar to<PERSON> as matr<PERSON><PERSON><PERSON>"}, "response": []}, {"name": "Buscar Matrícula por ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/enrollment/:id", "host": ["{{base_url}}"], "path": ["enrollment", ":id"], "variable": [{"key": "id", "value": "enrollment-uuid"}]}, "description": "Buscar matrícula específica"}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"student_name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"(11) 99999-8888\",\n  \"document\": \"987.654.321-00\",\n  \"birth_date\": \"1995-05-20\",\n  \"course_id\": \"course-uuid\",\n  \"class_id\": \"class-uuid\",\n  \"emergency_contact\": {\n    \"name\": \"<PERSON>\",\n    \"phone\": \"(11) 88888-7777\",\n    \"relationship\": \"<PERSON><PERSON>\"\n  }\n}"}, "url": {"raw": "{{base_url}}/enrollment", "host": ["{{base_url}}"], "path": ["enrollment"]}, "description": "Criar nova matrícula"}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/enrollment/:id/approve", "host": ["{{base_url}}"], "path": ["enrollment", ":id", "approve"], "variable": [{"key": "id", "value": "enrollment-uuid"}]}, "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> pendente"}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Documentação incompleta\"\n}"}, "url": {"raw": "{{base_url}}/enrollment/:id/reject", "host": ["{{base_url}}"], "path": ["enrollment", ":id", "reject"], "variable": [{"key": "id", "value": "enrollment-uuid"}]}, "description": "Rejeitar matrícula com motivo"}, "response": []}], "description": "Gestão de matrículas"}, {"name": "📊 Frequência", "item": [{"name": "Listar Frequências", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/attendance", "host": ["{{base_url}}"], "path": ["attendance"]}, "description": "Listar registros de frequência"}, "response": []}, {"name": "Registrar <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"class_id\": \"class-uuid\",\n  \"date\": \"2024-01-15\",\n  \"attendances\": [\n    {\n      \"enrollment_id\": \"enrollment-uuid-1\",\n      \"status\": \"present\"\n    },\n    {\n      \"enrollment_id\": \"enrollment-uuid-2\",\n      \"status\": \"absent\"\n    },\n    {\n      \"enrollment_id\": \"enrollment-uuid-3\",\n      \"status\": \"late\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/attendance", "host": ["{{base_url}}"], "path": ["attendance"]}, "description": "Registrar frequ<PERSON><PERSON> da turma"}, "response": []}, {"name": "Buscar Frequência por Turma", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/attendance/class/:classId?start_date=2024-01-01&end_date=2024-01-31", "host": ["{{base_url}}"], "path": ["attendance", "class", ":classId"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-01-31"}], "variable": [{"key": "classId", "value": "class-uuid"}]}, "description": "Buscar frequência de uma turma por período"}, "response": []}], "description": "Controle de frequência"}, {"name": "📈 Notas", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/grade", "host": ["{{base_url}}"], "path": ["grade"]}, "description": "Listar todas as notas"}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"enrollment_id\": \"enrollment-uuid\",\n  \"assessment_type\": \"exam\",\n  \"assessment_name\": \"Prova 1 - JavaScript\",\n  \"grade\": 8.5,\n  \"max_grade\": 10.0,\n  \"weight\": 0.4,\n  \"assessment_date\": \"2024-01-20\",\n  \"notes\": \"Boa prova, demonstrou conhecimento sólido\"\n}"}, "url": {"raw": "{{base_url}}/grade", "host": ["{{base_url}}"], "path": ["grade"]}, "description": "Lançar nova nota"}, "response": []}, {"name": "Buscar Notas por Matrícula", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/grade/enrollment/:enrollmentId", "host": ["{{base_url}}"], "path": ["grade", "enrollment", ":enrollmentId"], "variable": [{"key": "enrollmentId", "value": "enrollment-uuid"}]}, "description": "<PERSON><PERSON> todas as notas de uma matrícula"}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grade\": 9.0,\n  \"notes\": \"Nota revisada após recurso\"\n}"}, "url": {"raw": "{{base_url}}/grade/:id", "host": ["{{base_url}}"], "path": ["grade", ":id"], "variable": [{"key": "id", "value": "grade-uuid"}]}, "description": "Atualizar nota existente"}, "response": []}], "description": "Gestão de notas e avaliações"}], "description": "Sistema educacional completo"}, {"name": "💰 <PERSON><PERSON><PERSON><PERSON>", "item": [{"name": "💸 Transações Financeiras", "item": [{"name": "Listar Transações", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/financial/transactions?type=income&start_date=2024-01-01&end_date=2024-01-31", "host": ["{{base_url}}"], "path": ["financial", "transactions"], "query": [{"key": "type", "value": "income", "description": "income ou expense"}, {"key": "category", "value": "monthly_fee", "disabled": true}, {"key": "status", "value": "completed", "disabled": true}, {"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-01-31"}]}, "description": "Listar transações financeiras com filtros"}, "response": []}, {"name": "Criar Transação", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"income\",\n  \"category\": \"monthly_fee\",\n  \"amount\": 299.99,\n  \"description\": \"Mensalidade Janeiro 2024 - <PERSON>\",\n  \"reference_id\": \"enrollment-uuid\",\n  \"reference_type\": \"enrollment\",\n  \"transaction_date\": \"2024-01-15T10:30:00.000Z\",\n  \"due_date\": \"2024-01-31T23:59:59.000Z\",\n  \"currency\": \"BRL\",\n  \"metadata\": {\n    \"student_name\": \"<PERSON> Santos\",\n    \"course_name\": \"Desenvolvimento Web\"\n  }\n}"}, "url": {"raw": "{{base_url}}/financial/transactions", "host": ["{{base_url}}"], "path": ["financial", "transactions"]}, "description": "Criar nova transação financeira"}, "response": []}, {"name": "Atualizar Transação", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"completed\",\n  \"paid_date\": \"2024-01-15T14:30:00.000Z\",\n  \"payment_method_id\": \"payment-method-uuid\"\n}"}, "url": {"raw": "{{base_url}}/financial/transactions/:id", "host": ["{{base_url}}"], "path": ["financial", "transactions", ":id"], "variable": [{"key": "id", "value": "transaction-uuid"}]}, "description": "Atualizar transação existente"}, "response": []}], "description": "Gestão de transações financeiras"}, {"name": "🧾 Faturas", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"enrollment_id\": \"enrollment-uuid\",\n  \"amount\": 299.99,\n  \"due_date\": \"2024-01-31T23:59:59.000Z\",\n  \"notes\": \"Mensalidade Janeiro 2024\",\n  \"currency\": \"BRL\",\n  \"metadata\": {\n    \"course_name\": \"Desenvolvimento Web\",\n    \"student_name\": \"<PERSON>\"\n  }\n}"}, "url": {"raw": "{{base_url}}/financial/invoices", "host": ["{{base_url}}"], "path": ["financial", "invoices"]}, "description": "Criar nova fatura"}, "response": []}, {"name": "Processar Pagamento da Fatura", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"payment_method_id\": \"payment-method-uuid\",\n  \"payment_date\": \"2024-01-15T14:30:00.000Z\",\n  \"stripe_payment_intent_id\": \"pi_1234567890\",\n  \"notes\": \"Pagamento via cartão de crédito\"\n}"}, "url": {"raw": "{{base_url}}/financial/invoices/:id/payment", "host": ["{{base_url}}"], "path": ["financial", "invoices", ":id", "payment"], "variable": [{"key": "id", "value": "invoice-uuid"}]}, "description": "Processar pagamento de uma fatura"}, "response": []}], "description": "Gestão de faturas"}, {"name": "📊 <PERSON>sumo <PERSON>iro", "item": [{"name": "Obter <PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/financial/summary?start_date=2024-01-01&end_date=2024-01-31", "host": ["{{base_url}}"], "path": ["financial", "summary"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-01-31"}]}, "description": "Obter resumo financeiro com análises"}, "response": []}], "description": "Relatórios e análises financeiras"}], "description": "Sistema financeiro completo"}, {"name": "🔧 Administração", "item": [{"name": "🏢 Instituições", "item": [{"name": "Listar Instituições", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/institution", "host": ["{{base_url}}"], "path": ["institution"]}, "description": "Listar todas as institui<PERSON><PERSON><PERSON> (Super Admin)"}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Escola de Tecnologia ABC\",\n  \"document\": \"12.345.678/0001-90\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"(11) 3333-4444\",\n  \"website\": \"https://escolaabc.com\",\n  \"address\": {\n    \"street\": \"Rua das Flores, 123\",\n    \"city\": \"São Paulo\",\n    \"state\": \"SP\",\n    \"zip_code\": \"01234-567\",\n    \"country\": \"Brasil\"\n  }\n}"}, "url": {"raw": "{{base_url}}/institution", "host": ["{{base_url}}"], "path": ["institution"]}, "description": "Criar nova instituição"}, "response": []}], "description": "Gestão de instituições"}, {"name": "👤 Roles e Permissões", "item": [{"name": "Listar Roles", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/role", "host": ["{{base_url}}"], "path": ["role"]}, "description": "Listar to<PERSON> as roles"}, "response": []}, {"name": "Listar <PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/permission", "host": ["{{base_url}}"], "path": ["permission"]}, "description": "Listar todas as permiss<PERSON>es"}, "response": []}, {"name": "Atribuir Role ao Usuário", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"user-uuid\",\n  \"role_id\": \"role-uuid\"\n}"}, "url": {"raw": "{{base_url}}/user-role", "host": ["{{base_url}}"], "path": ["user-role"]}, "description": "Atribuir role a um usuário"}, "response": []}], "description": "Gestão de roles e permissões"}], "description": "Funcionalidades administrativas"}, {"name": "💳 Pagamentos Stripe", "item": [{"name": "<PERSON><PERSON>r <PERSON>gamento", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 29999,\n  \"currency\": \"brl\",\n  \"description\": \"Mensalidade Janeiro 2024\",\n  \"success_url\": \"https://app.edusys.com/payment/success\",\n  \"cancel_url\": \"https://app.edusys.com/payment/cancel\",\n  \"metadata\": {\n    \"enrollment_id\": \"enrollment-uuid\",\n    \"invoice_id\": \"invoice-uuid\"\n  }\n}"}, "url": {"raw": "{{base_url}}/payment/create-session", "host": ["{{base_url}}"], "path": ["payment", "create-session"]}, "description": "Criar sess<PERSON> de pagamento no Stripe"}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"plan_id\": \"plan-uuid\",\n  \"payment_method_id\": \"pm_1234567890\",\n  \"trial_days\": 7,\n  \"metadata\": {\n    \"institution_name\": \"Escola ABC\"\n  }\n}"}, "url": {"raw": "{{base_url}}/payment/create-subscription", "host": ["{{base_url}}"], "path": ["payment", "create-subscription"]}, "description": "Criar assinatura recorrente"}, "response": []}, {"name": "Webhook Stripe", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "stripe-signature", "value": "webhook-signature"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"evt_1234567890\",\n  \"object\": \"event\",\n  \"type\": \"payment_intent.succeeded\",\n  \"data\": {\n    \"object\": {\n      \"id\": \"pi_1234567890\",\n      \"amount\": 29999,\n      \"currency\": \"brl\",\n      \"status\": \"succeeded\"\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/payment/webhook", "host": ["{{base_url}}"], "path": ["payment", "webhook"]}, "description": "Webhook para eventos do Stripe"}, "response": []}], "description": "Integração com Stripe para pagamentos"}, {"name": "🧪 Testes e Utilitários", "item": [{"name": "Health Check", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Verificar se a API está funcionando"}, "response": []}, {"name": "Swagger Documentation", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api", "host": ["{{base_url}}"], "path": ["api"]}, "description": "Acessar documentação Swagger da API"}, "response": []}], "description": "Endpoints para testes e verificações"}]}