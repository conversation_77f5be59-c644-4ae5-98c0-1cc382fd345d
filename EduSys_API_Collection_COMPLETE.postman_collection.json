{"info": {"_postman_id": "edusys-api-complete-collection", "name": "EduSys API - COLLECTION COMPLETA", "description": "Collection COMPLETA com TODOS os endpoints da API do EduSys\n\n**TODOS OS CONTROLLERS INCLUÍDOS:**\n- 🔐 AuthController (4 endpoints)\n- 👥 UserController (5 endpoints)\n- 📚 CourseController (6 endpoints)\n- 🏫 ClassController (7 endpoints)\n- 📝 EnrollmentController (9 endpoints)\n- 📊 AttendanceController (8 endpoints)\n- 📈 GradeController (7 endpoints)\n- 💰 FinancialController (6 endpoints)\n- 🏢 InstitutionController (5 endpoints)\n- 📍 AddressController (5 endpoints)\n- 🔑 PermissionController (5 endpoints)\n- 👤 RoleController (5 endpoints)\n- 🔗 RolePermissionController (5 endpoints)\n- 👥 UserRoleController (5 endpoints)\n- 📋 PlanController (6 endpoints)\n- 💳 SubscriptionController (5 endpoints)\n- 💸 PaymentController (3 endpoints)\n\n**TOTAL: 17 Controllers | 100+ Endpoints**", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "institution_id", "value": "", "type": "string"}, {"key": "course_id", "value": "", "type": "string"}, {"key": "class_id", "value": "", "type": "string"}, {"key": "enrollment_id", "value": "", "type": "string"}, {"key": "role_id", "value": "", "type": "string"}, {"key": "permission_id", "value": "", "type": "string"}, {"key": "plan_id", "value": "", "type": "string"}, {"key": "subscription_id", "value": "", "type": "string"}, {"key": "address_id", "value": "", "type": "string"}, {"key": "grade_id", "value": "", "type": "string"}, {"key": "attendance_id", "value": "", "type": "string"}, {"key": "transaction_id", "value": "", "type": "string"}, {"key": "invoice_id", "value": "", "type": "string"}], "item": [{"name": "🔐 AuthController", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.accessToken);", "    pm.environment.set('refresh_token', response.refreshToken);", "    pm.environment.set('user_id', response.user.id);", "    pm.environment.set('institution_id', response.user.institution_id);", "    console.log('✅ Login realizado com sucesso!');", "    console.log('Token salvo:', response.accessToken.substring(0, 20) + '...');", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}, "description": "POST /auth/login - Fazer login e obter tokens JWT"}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}, "description": "POST /auth/logout - Fazer logout e invalidar tokens"}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.accessToken);", "    console.log('✅ Token renovado com sucesso!');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}, "description": "POST /auth/refresh - Renovar token de acesso"}, "response": []}, {"name": "Set Password", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"first-access-token\",\n  \"password\": \"newPassword123\",\n  \"confirmPassword\": \"newPassword123\"\n}"}, "url": {"raw": "{{base_url}}/auth/set-password", "host": ["{{base_url}}"], "path": ["auth", "set-password"]}, "description": "POST /auth/set-password - De<PERSON>ir senha no primeiro acesso"}, "response": []}], "description": "AuthController - 4 endpoints de autenticação"}, {"name": "👥 UserController", "item": [{"name": "Create User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('user_id', response.id);", "    console.log('✅ Us<PERSON>ário criado:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"(11) 99999-9999\",\n  \"document\": \"123.456.789-00\",\n  \"birth_date\": \"1990-01-15\",\n  \"institution_id\": \"{{institution_id}}\"\n}"}, "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}, "description": "POST /user - Criar novo usuário (ADMIN only)"}, "response": []}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"phone\": \"(11) 88888-8888\",\n  \"token\": \"update-token\"\n}"}, "url": {"raw": "{{base_url}}/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["user", "{{user_id}}"]}, "description": "PUT /user/:id - <PERSON><PERSON><PERSON><PERSON> usu<PERSON> (ADMIN only)"}, "response": []}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"delete-token\"\n}"}, "url": {"raw": "{{base_url}}/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["user", "{{user_id}}"]}, "description": "DELETE /user/:id - <PERSON><PERSON>r usuá<PERSON> (ADMIN only)"}, "response": []}, {"name": "Get User by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["user", "{{user_id}}"]}, "description": "GET /user/:id - Buscar usuário por ID (ADMIN only)"}, "response": []}, {"name": "Get All Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}, "description": "GET /user - Listar todos os usuários (ADMIN only)"}, "response": []}], "description": "UserController - 5 endpoints de gestão de usuários"}, {"name": "📚 CourseController", "item": [{"name": "Create Course", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('course_id', response.id);", "    console.log('✅ Curso criado:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Desenvolvimento Web Full Stack\",\n  \"description\": \"Curso completo de desenvolvimento web com React e Node.js\",\n  \"duration_months\": 12,\n  \"price\": 2999.99,\n  \"max_students\": 30,\n  \"start_date\": \"2024-02-01T00:00:00.000Z\",\n  \"end_date\": \"2025-01-31T23:59:59.000Z\"\n}"}, "url": {"raw": "{{base_url}}/course", "host": ["{{base_url}}"], "path": ["course"]}, "description": "POST /course - Criar novo curso"}, "response": []}, {"name": "Update Course", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Desenvolvimento Web Full Stack - Atualizado\",\n  \"price\": 3299.99,\n  \"max_students\": 35\n}"}, "url": {"raw": "{{base_url}}/course/{{course_id}}", "host": ["{{base_url}}"], "path": ["course", "{{course_id}}"]}, "description": "PUT /course/:id - <PERSON><PERSON><PERSON>r curso"}, "response": []}, {"name": "Delete Course", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/course/{{course_id}}", "host": ["{{base_url}}"], "path": ["course", "{{course_id}}"]}, "description": "DELETE /course/:id - Deletar curso"}, "response": []}, {"name": "Get Course by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/course/{{course_id}}", "host": ["{{base_url}}"], "path": ["course", "{{course_id}}"]}, "description": "GET /course/:id - Buscar curso por ID"}, "response": []}, {"name": "Get All Courses", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/course", "host": ["{{base_url}}"], "path": ["course"]}, "description": "GET /course - Listar todos os cursos"}, "response": []}, {"name": "Get Courses by Institution", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/course/institution/{{institution_id}}", "host": ["{{base_url}}"], "path": ["course", "institution", "{{institution_id}}"]}, "description": "GET /course/institution/:institutionId - Buscar cursos por instituição"}, "response": []}], "description": "CourseController - 6 endpoints de gestão de cursos"}, {"name": "🏫 ClassController", "item": [{"name": "Create Class", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('class_id', response.id);", "    console.log('✅ Turma criada:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>rma A - Manhã\",\n  \"course_id\": \"{{course_id}}\",\n  \"start_date\": \"2024-02-01T00:00:00.000Z\",\n  \"end_date\": \"2025-01-31T23:59:59.000Z\",\n  \"schedule\": {\n    \"monday\": \"08:00-12:00\",\n    \"wednesday\": \"08:00-12:00\",\n    \"friday\": \"08:00-12:00\"\n  },\n  \"max_students\": 25\n}"}, "url": {"raw": "{{base_url}}/class", "host": ["{{base_url}}"], "path": ["class"]}, "description": "POST /class - Criar nova turma"}, "response": []}, {"name": "Update Class", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON> (Atualizada)\",\n  \"max_students\": 30,\n  \"schedule\": {\n    \"monday\": \"08:00-12:00\",\n    \"tuesday\": \"08:00-12:00\",\n    \"wednesday\": \"08:00-12:00\",\n    \"friday\": \"08:00-12:00\"\n  }\n}"}, "url": {"raw": "{{base_url}}/class/{{class_id}}", "host": ["{{base_url}}"], "path": ["class", "{{class_id}}"]}, "description": "PUT /class/:id - Atualizar turma"}, "response": []}, {"name": "Delete Class", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/class/{{class_id}}", "host": ["{{base_url}}"], "path": ["class", "{{class_id}}"]}, "description": "DELETE /class/:id - Deletar turma"}, "response": []}, {"name": "Get Class by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/class/{{class_id}}", "host": ["{{base_url}}"], "path": ["class", "{{class_id}}"]}, "description": "GET /class/:id - Buscar turma por ID"}, "response": []}, {"name": "Get All Classes", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/class", "host": ["{{base_url}}"], "path": ["class"]}, "description": "GET /class - Listar todas as turmas"}, "response": []}, {"name": "Get Classes by Course", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/class/course/{{course_id}}", "host": ["{{base_url}}"], "path": ["class", "course", "{{course_id}}"]}, "description": "GET /class/course/:courseId - Buscar turmas por curso"}, "response": []}, {"name": "Get Available Classes", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/class/available", "host": ["{{base_url}}"], "path": ["class", "available"]}, "description": "GET /class/available - Buscar turmas disponíveis para matrícula"}, "response": []}], "description": "ClassController - 7 endpoints de gestão de turmas"}, {"name": "📝 EnrollmentController", "item": [{"name": "Create Enrollment", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('enrollment_id', response.id);", "    console.log('✅ Matrícula criada:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"student_name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"(11) 99999-8888\",\n  \"document\": \"987.654.321-00\",\n  \"birth_date\": \"1995-05-20\",\n  \"course_id\": \"{{course_id}}\",\n  \"class_id\": \"{{class_id}}\",\n  \"emergency_contact\": {\n    \"name\": \"<PERSON>\",\n    \"phone\": \"(11) 88888-7777\",\n    \"relationship\": \"<PERSON><PERSON>\"\n  }\n}"}, "url": {"raw": "{{base_url}}/enrollment", "host": ["{{base_url}}"], "path": ["enrollment"]}, "description": "POST /enrollment - Criar nova matr<PERSON><PERSON>"}, "response": []}, {"name": "Delete Enrollment", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/enrollment/{{enrollment_id}}", "host": ["{{base_url}}"], "path": ["enrollment", "{{enrollment_id}}"]}, "description": "DELETE /enrollment/:id - Deletar matrícula"}, "response": []}, {"name": "Get Enrollment by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/enrollment/{{enrollment_id}}", "host": ["{{base_url}}"], "path": ["enrollment", "{{enrollment_id}}"]}, "description": "GET /enrollment/:id - Buscar matrícula por ID"}, "response": []}, {"name": "Get All Enrollments", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/enrollment", "host": ["{{base_url}}"], "path": ["enrollment"]}, "description": "GET /enrollment - <PERSON><PERSON> todas as matrículas"}, "response": []}, {"name": "Get Enrollments by Student", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/enrollment/student/{{user_id}}", "host": ["{{base_url}}"], "path": ["enrollment", "student", "{{user_id}}"]}, "description": "GET /enrollment/student/:studentId - Buscar matrículas por estudante"}, "response": []}, {"name": "Get Enrollments by Class", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/enrollment/class/{{class_id}}", "host": ["{{base_url}}"], "path": ["enrollment", "class", "{{class_id}}"]}, "description": "GET /enrollment/class/:classId - Buscar matrículas por turma"}, "response": []}, {"name": "Get Pending Enrollments", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/enrollment/pending", "host": ["{{base_url}}"], "path": ["enrollment", "pending"]}, "description": "GET /enrollment/pending - Buscar matrículas pendentes de aprovação"}, "response": []}, {"name": "Approve Enrollment", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"notes\": \"Documentação aprovada\",\n  \"generate_invoice\": true\n}"}, "url": {"raw": "{{base_url}}/enrollment/{{enrollment_id}}/approve", "host": ["{{base_url}}"], "path": ["enrollment", "{{enrollment_id}}", "approve"]}, "description": "PUT /enrollment/:id/approve - <PERSON><PERSON><PERSON> mat<PERSON>"}, "response": []}, {"name": "Reject Enrollment", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Documentação incompleta\",\n  \"notes\": \"Favor reenviar documentos atualizados\"\n}"}, "url": {"raw": "{{base_url}}/enrollment/{{enrollment_id}}/reject", "host": ["{{base_url}}"], "path": ["enrollment", "{{enrollment_id}}", "reject"]}, "description": "PUT /enrollment/:id/reject - <PERSON><PERSON><PERSON><PERSON> matr<PERSON><PERSON>"}, "response": []}], "description": "EnrollmentController - 9 endpoints de gestão de matrículas"}, {"name": "📊 AttendanceController", "item": [{"name": "Create Attendance", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('attendance_id', response.id);", "    console.log('✅ Frequência registrada:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"class_id\": \"{{class_id}}\",\n  \"date\": \"2024-01-15\",\n  \"attendances\": [\n    {\n      \"enrollment_id\": \"{{enrollment_id}}\",\n      \"status\": \"present\"\n    },\n    {\n      \"enrollment_id\": \"enrollment-uuid-2\",\n      \"status\": \"absent\"\n    },\n    {\n      \"enrollment_id\": \"enrollment-uuid-3\",\n      \"status\": \"late\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/attendance", "host": ["{{base_url}}"], "path": ["attendance"]}, "description": "POST /attendance - Registrar fre<PERSON><PERSON><PERSON><PERSON> da turma"}, "response": []}, {"name": "Update Attendance", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"present\",\n  \"notes\": \"Chegou atrasado mas participou da aula\"\n}"}, "url": {"raw": "{{base_url}}/attendance/{{attendance_id}}", "host": ["{{base_url}}"], "path": ["attendance", "{{attendance_id}}"]}, "description": "PUT /attendance/:id - Atualizar registro de frequência"}, "response": []}, {"name": "Delete Attendance", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/attendance/{{attendance_id}}", "host": ["{{base_url}}"], "path": ["attendance", "{{attendance_id}}"]}, "description": "DELETE /attendance/:id - Deletar registro de frequência"}, "response": []}, {"name": "Get Attendance by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/attendance/{{attendance_id}}", "host": ["{{base_url}}"], "path": ["attendance", "{{attendance_id}}"]}, "description": "GET /attendance/:id - Buscar frequência por ID"}, "response": []}, {"name": "Get All Attendances", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/attendance", "host": ["{{base_url}}"], "path": ["attendance"]}, "description": "GET /attendance - Listar todos os registros de frequência"}, "response": []}, {"name": "Get Attendance by Class", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/attendance/class/{{class_id}}?start_date=2024-01-01&end_date=2024-01-31", "host": ["{{base_url}}"], "path": ["attendance", "class", "{{class_id}}"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-01-31"}]}, "description": "GET /attendance/class/:classId - Buscar frequência por turma e período"}, "response": []}, {"name": "Get Attendance by Student", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/attendance/student/{{enrollment_id}}?start_date=2024-01-01&end_date=2024-01-31", "host": ["{{base_url}}"], "path": ["attendance", "student", "{{enrollment_id}}"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-01-31"}]}, "description": "GET /attendance/student/:enrollmentId - Buscar frequência por estudante"}, "response": []}, {"name": "Get Attendance Report", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/attendance/report/{{class_id}}?start_date=2024-01-01&end_date=2024-01-31", "host": ["{{base_url}}"], "path": ["attendance", "report", "{{class_id}}"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-01-31"}]}, "description": "GET /attendance/report/:classId - Gerar relatório de frequência"}, "response": []}], "description": "AttendanceController - 8 endpoints de controle de frequência"}, {"name": "📈 GradeController", "item": [{"name": "Create Grade", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('grade_id', response.id);", "    console.log('✅ Nota lançada:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"enrollment_id\": \"{{enrollment_id}}\",\n  \"assessment_type\": \"exam\",\n  \"assessment_name\": \"Prova 1 - JavaScript\",\n  \"grade\": 8.5,\n  \"max_grade\": 10.0,\n  \"weight\": 0.4,\n  \"assessment_date\": \"2024-01-20\",\n  \"notes\": \"Boa prova, demonstrou conhecimento sólido\"\n}"}, "url": {"raw": "{{base_url}}/grade", "host": ["{{base_url}}"], "path": ["grade"]}, "description": "POST /grade - Lançar nova nota"}, "response": []}, {"name": "Update Grade", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grade\": 9.0,\n  \"notes\": \"Nota revisada após recurso\",\n  \"assessment_name\": \"Prova 1 - JavaScript (Revisada)\"\n}"}, "url": {"raw": "{{base_url}}/grade/{{grade_id}}", "host": ["{{base_url}}"], "path": ["grade", "{{grade_id}}"]}, "description": "PUT /grade/:id - <PERSON><PERSON><PERSON><PERSON> nota"}, "response": []}, {"name": "Delete Grade", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/grade/{{grade_id}}", "host": ["{{base_url}}"], "path": ["grade", "{{grade_id}}"]}, "description": "DELETE /grade/:id - Deletar nota"}, "response": []}, {"name": "Get Grade by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/grade/{{grade_id}}", "host": ["{{base_url}}"], "path": ["grade", "{{grade_id}}"]}, "description": "GET /grade/:id - Buscar nota por ID"}, "response": []}, {"name": "Get All Grades", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/grade", "host": ["{{base_url}}"], "path": ["grade"]}, "description": "GET /grade - Listar todas as notas"}, "response": []}, {"name": "Get Grades by Enrollment", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/grade/enrollment/{{enrollment_id}}", "host": ["{{base_url}}"], "path": ["grade", "enrollment", "{{enrollment_id}}"]}, "description": "GET /grade/enrollment/:enrollmentId - Buscar notas por matrícula"}, "response": []}, {"name": "Get Grades by Class", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/grade/class/{{class_id}}", "host": ["{{base_url}}"], "path": ["grade", "class", "{{class_id}}"]}, "description": "GET /grade/class/:classId - Buscar notas por turma"}, "response": []}], "description": "GradeController - 7 endpoints de gestão de notas"}, {"name": "💰 FinancialController", "item": [{"name": "Create Financial Transaction", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('transaction_id', response.id);", "    console.log('✅ Transação criada:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"income\",\n  \"category\": \"monthly_fee\",\n  \"amount\": 299.99,\n  \"description\": \"Mensalidade Janeiro 2024 - <PERSON>\",\n  \"reference_id\": \"{{enrollment_id}}\",\n  \"reference_type\": \"enrollment\",\n  \"transaction_date\": \"2024-01-15T10:30:00.000Z\",\n  \"due_date\": \"2024-01-31T23:59:59.000Z\",\n  \"currency\": \"BRL\",\n  \"metadata\": {\n    \"student_name\": \"<PERSON>\",\n    \"course_name\": \"Desenvolvimento Web\"\n  }\n}"}, "url": {"raw": "{{base_url}}/financial/transactions", "host": ["{{base_url}}"], "path": ["financial", "transactions"]}, "description": "POST /financial/transactions - Criar transação financeira"}, "response": []}, {"name": "Get Financial Transactions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/financial/transactions?type=income&category=monthly_fee&status=pending&start_date=2024-01-01&end_date=2024-01-31", "host": ["{{base_url}}"], "path": ["financial", "transactions"], "query": [{"key": "type", "value": "income", "description": "income ou expense"}, {"key": "category", "value": "monthly_fee"}, {"key": "status", "value": "pending"}, {"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-01-31"}, {"key": "reference_id", "value": "{{enrollment_id}}", "disabled": true}, {"key": "reference_type", "value": "enrollment", "disabled": true}]}, "description": "GET /financial/transactions - Listar transações com filtros"}, "response": []}, {"name": "Update Financial Transaction", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"completed\",\n  \"paid_date\": \"2024-01-15T14:30:00.000Z\",\n  \"payment_method_id\": \"payment-method-uuid\",\n  \"notes\": \"Pagamento via PIX\"\n}"}, "url": {"raw": "{{base_url}}/financial/transactions/{{transaction_id}}", "host": ["{{base_url}}"], "path": ["financial", "transactions", "{{transaction_id}}"]}, "description": "PUT /financial/transactions/:id - Atualizar transação"}, "response": []}, {"name": "Get Financial Summary", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/financial/summary?start_date=2024-01-01&end_date=2024-01-31", "host": ["{{base_url}}"], "path": ["financial", "summary"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-01-31"}]}, "description": "GET /financial/summary - Obter resumo financeiro"}, "response": []}, {"name": "Create Invoice", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('invoice_id', response.id);", "    console.log('✅ Fatura criada:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"enrollment_id\": \"{{enrollment_id}}\",\n  \"amount\": 299.99,\n  \"due_date\": \"2024-01-31T23:59:59.000Z\",\n  \"notes\": \"Mensalidade Janeiro 2024\",\n  \"currency\": \"BRL\",\n  \"metadata\": {\n    \"course_name\": \"Desenvolvimento Web\",\n    \"student_name\": \"<PERSON>\"\n  }\n}"}, "url": {"raw": "{{base_url}}/financial/invoices", "host": ["{{base_url}}"], "path": ["financial", "invoices"]}, "description": "POST /financial/invoices - <PERSON><PERSON><PERSON> fatura"}, "response": []}, {"name": "Process Invoice Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"payment_method_id\": \"payment-method-uuid\",\n  \"payment_date\": \"2024-01-15T14:30:00.000Z\",\n  \"stripe_payment_intent_id\": \"pi_1234567890\",\n  \"notes\": \"Pagamento via cartão de crédito\"\n}"}, "url": {"raw": "{{base_url}}/financial/invoices/{{invoice_id}}/payment", "host": ["{{base_url}}"], "path": ["financial", "invoices", "{{invoice_id}}", "payment"]}, "description": "POST /financial/invoices/:id/payment - Processar pagamento da fatura"}, "response": []}], "description": "FinancialController - 6 endpoints do módulo financeiro"}, {"name": "🏢 InstitutionController", "item": [{"name": "Create Institution", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Escola de Tecnologia ABC\",\n  \"document\": \"12.345.678/0001-90\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"(11) 3333-4444\",\n  \"website\": \"https://escolaabc.com\",\n  \"address\": {\n    \"street\": \"Rua das Flores, 123\",\n    \"city\": \"São Paulo\",\n    \"state\": \"SP\",\n    \"zip_code\": \"01234-567\",\n    \"country\": \"Brasil\"\n  }\n}"}, "url": {"raw": "{{base_url}}/institution", "host": ["{{base_url}}"], "path": ["institution"]}, "description": "POST /institution - Criar instituição (Super Admin)"}, "response": []}, {"name": "Update Institution", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Escola de Tecnologia ABC - Atualizada\",\n  \"phone\": \"(11) 3333-5555\",\n  \"website\": \"https://escolaabc.edu.br\"\n}"}, "url": {"raw": "{{base_url}}/institution/{{institution_id}}", "host": ["{{base_url}}"], "path": ["institution", "{{institution_id}}"]}, "description": "PUT /institution/:id - Atualizar instituição"}, "response": []}, {"name": "Delete Institution", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/institution/{{institution_id}}", "host": ["{{base_url}}"], "path": ["institution", "{{institution_id}}"]}, "description": "DELETE /institution/:id - Deletar instituição (Super Admin)"}, "response": []}, {"name": "Get Institution by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/institution/{{institution_id}}", "host": ["{{base_url}}"], "path": ["institution", "{{institution_id}}"]}, "description": "GET /institution/:id - Buscar instituição por ID"}, "response": []}, {"name": "Get All Institutions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/institution", "host": ["{{base_url}}"], "path": ["institution"]}, "description": "GET /institution - Listar instituições (Super Admin)"}, "response": []}], "description": "InstitutionController - 5 endpoints de gestão de instituições"}, {"name": "📍 AddressController", "item": [{"name": "Create Address", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('address_id', response.id);", "    console.log('✅ Endereço criado:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"street\": \"Rua das Palmeiras, 456\",\n  \"number\": \"123\",\n  \"complement\": \"Apto 45\",\n  \"neighborhood\": \"Centro\",\n  \"city\": \"São Paulo\",\n  \"state\": \"SP\",\n  \"zip_code\": \"01234-567\",\n  \"country\": \"Brasil\",\n  \"user_id\": \"{{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/address", "host": ["{{base_url}}"], "path": ["address"]}, "description": "POST /address - <PERSON><PERSON><PERSON>"}, "response": []}, {"name": "Update Address", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"street\": \"Rua das Palmeiras, 789\",\n  \"number\": \"456\",\n  \"complement\": \"Casa\"\n}"}, "url": {"raw": "{{base_url}}/address/{{address_id}}", "host": ["{{base_url}}"], "path": ["address", "{{address_id}}"]}, "description": "PUT /address/:id - Atualizar endereço"}, "response": []}, {"name": "Delete Address", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/address/{{address_id}}", "host": ["{{base_url}}"], "path": ["address", "{{address_id}}"]}, "description": "DELETE /address/:id - Deletar endereço"}, "response": []}, {"name": "Get Address by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/address/{{address_id}}", "host": ["{{base_url}}"], "path": ["address", "{{address_id}}"]}, "description": "GET /address/:id - Buscar endereço por ID"}, "response": []}, {"name": "Get All Addresses", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/address", "host": ["{{base_url}}"], "path": ["address"]}, "description": "GET /address - Listar endereços"}, "response": []}], "description": "AddressController - 5 endpoints de gestão de endereços"}, {"name": "🔑 PermissionController", "item": [{"name": "Create Permission", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('permission_id', response.id);", "    console.log('✅ Permissão criada:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"CREATE_COURSE\",\n  \"description\": \"Permissão para criar cursos\",\n  \"resource\": \"course\",\n  \"action\": \"create\"\n}"}, "url": {"raw": "{{base_url}}/permission", "host": ["{{base_url}}"], "path": ["permission"]}, "description": "POST /permission - <PERSON><PERSON><PERSON> (Super Admin)"}, "response": []}, {"name": "Update Permission", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"Permissão para criar e gerenciar cursos\"\n}"}, "url": {"raw": "{{base_url}}/permission/{{permission_id}}", "host": ["{{base_url}}"], "path": ["permission", "{{permission_id}}"]}, "description": "PUT /permission/:id - <PERSON><PERSON><PERSON><PERSON> per<PERSON>"}, "response": []}, {"name": "Delete Permission", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/permission/{{permission_id}}", "host": ["{{base_url}}"], "path": ["permission", "{{permission_id}}"]}, "description": "DELETE /permission/:id - <PERSON><PERSON><PERSON> (Super Admin)"}, "response": []}, {"name": "Get Permission by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/permission/{{permission_id}}", "host": ["{{base_url}}"], "path": ["permission", "{{permission_id}}"]}, "description": "GET /permission/:id - Buscar permissão por ID"}, "response": []}, {"name": "Get All Permissions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/permission", "host": ["{{base_url}}"], "path": ["permission"]}, "description": "GET /permission - <PERSON><PERSON> todas as permissões"}, "response": []}], "description": "PermissionController - 5 endpoints de gestão de permissões"}, {"name": "👤 RoleController", "item": [{"name": "Create Role", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('role_id', response.id);", "    console.log('✅ Role criada:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"PROFESSOR\",\n  \"description\": \"Professor da institui<PERSON>\",\n  \"institution_id\": \"{{institution_id}}\"\n}"}, "url": {"raw": "{{base_url}}/role", "host": ["{{base_url}}"], "path": ["role"]}, "description": "POST /role - Criar role"}, "response": []}, {"name": "Update Role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"Professor re<PERSON><PERSON><PERSON><PERSON> por turmas e avaliações\"\n}"}, "url": {"raw": "{{base_url}}/role/{{role_id}}", "host": ["{{base_url}}"], "path": ["role", "{{role_id}}"]}, "description": "PUT /role/:id - Atual<PERSON>r role"}, "response": []}, {"name": "Delete Role", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/role/{{role_id}}", "host": ["{{base_url}}"], "path": ["role", "{{role_id}}"]}, "description": "DELETE /role/:id - <PERSON><PERSON><PERSON> role"}, "response": []}, {"name": "Get Role by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/role/{{role_id}}", "host": ["{{base_url}}"], "path": ["role", "{{role_id}}"]}, "description": "GET /role/:id - Buscar role por ID"}, "response": []}, {"name": "Get All Roles", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/role", "host": ["{{base_url}}"], "path": ["role"]}, "description": "GET /role - <PERSON><PERSON> as roles"}, "response": []}], "description": "RoleController - 5 endpoints de gestão de roles"}, {"name": "🔗 RolePermissionController", "item": [{"name": "Assign Permission to Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"role_id\": \"{{role_id}}\",\n  \"permission_id\": \"{{permission_id}}\"\n}"}, "url": {"raw": "{{base_url}}/role-permission", "host": ["{{base_url}}"], "path": ["role-permission"]}, "description": "POST /role-permission - Atribuir permissão à role"}, "response": []}, {"name": "Remove Permission from Role", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"role_id\": \"{{role_id}}\",\n  \"permission_id\": \"{{permission_id}}\"\n}"}, "url": {"raw": "{{base_url}}/role-permission", "host": ["{{base_url}}"], "path": ["role-permission"]}, "description": "DELETE /role-permission - <PERSON>mo<PERSON> per<PERSON><PERSON> da role"}, "response": []}, {"name": "Get Role Permissions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/role-permission/role/{{role_id}}", "host": ["{{base_url}}"], "path": ["role-permission", "role", "{{role_id}}"]}, "description": "GET /role-permission/role/:roleId - Buscar permissões da role"}, "response": []}, {"name": "Get Permission Roles", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/role-permission/permission/{{permission_id}}", "host": ["{{base_url}}"], "path": ["role-permission", "permission", "{{permission_id}}"]}, "description": "GET /role-permission/permission/:permissionId - Buscar roles com a permissão"}, "response": []}, {"name": "Get All Role Permissions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/role-permission", "host": ["{{base_url}}"], "path": ["role-permission"]}, "description": "GET /role-permission - <PERSON><PERSON><PERSON> as atribuiç<PERSON><PERSON> role-permiss<PERSON>"}, "response": []}], "description": "RolePermissionController - 5 endpoints de gestão role-permissão"}, {"name": "👥 UserRoleController", "item": [{"name": "Assign Role to User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"{{user_id}}\",\n  \"role_id\": \"{{role_id}}\"\n}"}, "url": {"raw": "{{base_url}}/user-role", "host": ["{{base_url}}"], "path": ["user-role"]}, "description": "POST /user-role - Atribuir role ao usuário"}, "response": []}, {"name": "Remove Role from User", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"{{user_id}}\",\n  \"role_id\": \"{{role_id}}\"\n}"}, "url": {"raw": "{{base_url}}/user-role", "host": ["{{base_url}}"], "path": ["user-role"]}, "description": "DELETE /user-role - Remover role do usuário"}, "response": []}, {"name": "Get User Roles", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/user-role/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["user-role", "user", "{{user_id}}"]}, "description": "GET /user-role/user/:userId - Buscar roles do usuário"}, "response": []}, {"name": "Get Role Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/user-role/role/{{role_id}}", "host": ["{{base_url}}"], "path": ["user-role", "role", "{{role_id}}"]}, "description": "GET /user-role/role/:roleId - Buscar usuários com a role"}, "response": []}, {"name": "Get All User Roles", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/user-role", "host": ["{{base_url}}"], "path": ["user-role"]}, "description": "GET /user-role - <PERSON><PERSON> as atri<PERSON><PERSON><PERSON><PERSON><PERSON> usuário-role"}, "response": []}], "description": "UserRoleController - 5 endpoints de gestão usuário-role"}, {"name": "📋 PlanController", "item": [{"name": "Create Plan", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('plan_id', response.id);", "    console.log('✅ Plano criado:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Plano Premium\",\n  \"description\": \"Plano completo com todas as funcionalidades\",\n  \"price\": 199.99,\n  \"billing_cycle\": \"monthly\",\n  \"features\": {\n    \"max_students\": 500,\n    \"max_courses\": 50,\n    \"financial_module\": true,\n    \"reports\": true,\n    \"api_access\": true\n  },\n  \"stripe_price_id\": \"price_1234567890\"\n}"}, "url": {"raw": "{{base_url}}/plan", "host": ["{{base_url}}"], "path": ["plan"]}, "description": "POST /plan - <PERSON><PERSON><PERSON> plano (Super Admin)"}, "response": []}, {"name": "Update Plan", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Plano Premium Plus\",\n  \"price\": 249.99,\n  \"features\": {\n    \"max_students\": 1000,\n    \"max_courses\": 100,\n    \"financial_module\": true,\n    \"reports\": true,\n    \"api_access\": true,\n    \"priority_support\": true\n  }\n}"}, "url": {"raw": "{{base_url}}/plan/{{plan_id}}", "host": ["{{base_url}}"], "path": ["plan", "{{plan_id}}"]}, "description": "PUT /plan/:id - Atualizar plano"}, "response": []}, {"name": "Delete Plan", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/plan/{{plan_id}}", "host": ["{{base_url}}"], "path": ["plan", "{{plan_id}}"]}, "description": "DELETE /plan/:id - Deletar plano (Super Admin)"}, "response": []}, {"name": "Get Plan by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/plan/{{plan_id}}", "host": ["{{base_url}}"], "path": ["plan", "{{plan_id}}"]}, "description": "GET /plan/:id - Buscar plano por ID"}, "response": []}, {"name": "Get All Plans", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/plan", "host": ["{{base_url}}"], "path": ["plan"]}, "description": "GET /plan - Listar todos os planos"}, "response": []}, {"name": "Get Active Plans", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/plan/active", "host": ["{{base_url}}"], "path": ["plan", "active"]}, "description": "GET /plan/active - Buscar planos ativos"}, "response": []}], "description": "PlanController - 6 endpoints de gestão de planos"}, {"name": "💳 SubscriptionController", "item": [{"name": "Create Subscription", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('subscription_id', response.id);", "    console.log('✅ Assinatura criada:', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"institution_id\": \"{{institution_id}}\",\n  \"plan_id\": \"{{plan_id}}\",\n  \"start_date\": \"2024-01-01T00:00:00.000Z\",\n  \"trial_days\": 7,\n  \"stripe_subscription_id\": \"sub_1234567890\"\n}"}, "url": {"raw": "{{base_url}}/subscription", "host": ["{{base_url}}"], "path": ["subscription"]}, "description": "POST /subscription - Criar assinatura"}, "response": []}, {"name": "Update Subscription", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"plan_id\": \"{{plan_id}}\",\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/subscription/{{subscription_id}}", "host": ["{{base_url}}"], "path": ["subscription", "{{subscription_id}}"]}, "description": "PUT /subscription/:id - Atualizar assinatura"}, "response": []}, {"name": "Cancel Subscription", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Cancelamento solicitado pelo cliente\",\n  \"immediate\": false\n}"}, "url": {"raw": "{{base_url}}/subscription/{{subscription_id}}", "host": ["{{base_url}}"], "path": ["subscription", "{{subscription_id}}"]}, "description": "DELETE /subscription/:id - Cancelar assinatura"}, "response": []}, {"name": "Get Subscription by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/subscription/{{subscription_id}}", "host": ["{{base_url}}"], "path": ["subscription", "{{subscription_id}}"]}, "description": "GET /subscription/:id - Buscar assinatura por ID"}, "response": []}, {"name": "Get Institution Subscription", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/subscription/institution/{{institution_id}}", "host": ["{{base_url}}"], "path": ["subscription", "institution", "{{institution_id}}"]}, "description": "GET /subscription/institution/:institutionId - Buscar assinatura da instituição"}, "response": []}], "description": "SubscriptionController - 5 endpoints de gestão de assinaturas"}, {"name": "💸 PaymentController", "item": [{"name": "Create Payment Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 29999,\n  \"currency\": \"brl\",\n  \"description\": \"Mensalidade Janeiro 2024\",\n  \"success_url\": \"https://app.edusys.com/payment/success\",\n  \"cancel_url\": \"https://app.edusys.com/payment/cancel\",\n  \"metadata\": {\n    \"enrollment_id\": \"{{enrollment_id}}\",\n    \"invoice_id\": \"{{invoice_id}}\"\n  }\n}"}, "url": {"raw": "{{base_url}}/payment/create-session", "host": ["{{base_url}}"], "path": ["payment", "create-session"]}, "description": "POST /payment/create-session - <PERSON><PERSON>r se<PERSON><PERSON> de pagamento Stripe"}, "response": []}, {"name": "Create Subscription Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"plan_id\": \"{{plan_id}}\",\n  \"payment_method_id\": \"pm_1234567890\",\n  \"trial_days\": 7,\n  \"metadata\": {\n    \"institution_name\": \"Escola ABC\",\n    \"institution_id\": \"{{institution_id}}\"\n  }\n}"}, "url": {"raw": "{{base_url}}/payment/create-subscription", "host": ["{{base_url}}"], "path": ["payment", "create-subscription"]}, "description": "POST /payment/create-subscription - Criar assinatura recorrente"}, "response": []}, {"name": "Stripe Webhook", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "stripe-signature", "value": "webhook-signature-here"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"evt_1234567890\",\n  \"object\": \"event\",\n  \"type\": \"payment_intent.succeeded\",\n  \"data\": {\n    \"object\": {\n      \"id\": \"pi_1234567890\",\n      \"amount\": 29999,\n      \"currency\": \"brl\",\n      \"status\": \"succeeded\",\n      \"metadata\": {\n        \"enrollment_id\": \"{{enrollment_id}}\",\n        \"invoice_id\": \"{{invoice_id}}\"\n      }\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/payment/webhook", "host": ["{{base_url}}"], "path": ["payment", "webhook"]}, "description": "POST /payment/webhook - Webhook para eventos do Stripe"}, "response": []}], "description": "PaymentController - 3 endpoints de integração Stripe"}, {"name": "🧪 Utilitários e Testes", "item": [{"name": "Health Check", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "GET /health - Verificar se a API está funcionando"}, "response": []}, {"name": "Swagger Documentation", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api", "host": ["{{base_url}}"], "path": ["api"]}, "description": "GET /api - Acessar documentação Swagger da API"}, "response": []}, {"name": "API Info", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}, "description": "GET / - Informações básicas da API"}, "response": []}], "description": "Endpoints para testes e verificações"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Script global executado antes de cada requisição", "const accessToken = pm.environment.get('access_token');", "const tokenExpiry = pm.environment.get('token_expiry');", "", "// Log do token atual (apenas primeiros caracteres por segurança)", "if (accessToken) {", "    console.log('🔑 Token atual:', accessToken.substring(0, 20) + '...');", "    ", "    // Verificar se token está próximo do vencimento", "    if (tokenExpiry && Date.now() > (tokenExpiry - 60000)) {", "        console.log('⚠️ Token expirando em breve, considere fazer refresh');", "    }", "} else {", "    console.log('❌ Nenhum token encontrado. Faça login primeiro.');", "}", "", "// Log da requisição", "console.log('📤 Requisição:', pm.request.method, pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Script global executado após cada resposta", "", "// Log da resposta", "console.log('📥 Resposta:', pm.response.code, pm.response.status);", "console.log('⏱️ Tempo:', pm.response.responseTime + 'ms');", "", "// Verificar se token expirou", "if (pm.response.code === 401) {", "    console.log('🔒 Token expirado ou inválido. Faça login novamente.');", "    pm.environment.unset('access_token');", "    pm.environment.unset('token_expiry');", "}", "", "// Testes básicos para todas as requisições", "pm.test('✅ Status code não é 500 (erro interno)', function () {", "    pm.expect(pm.response.code).to.not.equal(500);", "});", "", "pm.test('⚡ Tempo de resposta menor que 10 segundos', function () {", "    pm.expect(pm.response.responseTime).to.be.below(10000);", "});", "", "// Log de sucesso/erro", "if (pm.response.code >= 200 && pm.response.code < 300) {", "    console.log('✅ Requisição bem-sucedida');", "} else if (pm.response.code >= 400) {", "    console.log('❌ Erro na requisição:', pm.response.code, pm.response.status);", "    try {", "        const errorBody = pm.response.json();", "        console.log('📋 Detalhes do erro:', errorBody);", "    } catch (e) {", "        console.log('📋 Resposta de erro:', pm.response.text());", "    }", "}"]}}]}