# 🔑 **Mapeamento de Permissões Granulares por Controller - EduSys**

## 📋 **Permissões por Controller**

### **👤 UserController** - `/user`
```typescript
// Permissões administrativas - apenas ADMIN
@Post() - 'user.create'           // Criar usuários
@Put(':id') - 'user.update'       // Editar usuários  
@Delete(':id') - 'user.delete'    // Deletar usuários
@Get() - 'user.view'              // Ver todos os usuários
@Get(':id') - 'user.view'         // Ver usuário específico
```

### **🎭 RoleController** - `/role`
```typescript
// Permissões administrativas - apenas ADMIN
@Post() - 'role.create'           // Criar roles
@Put(':id') - 'role.update'       // Editar roles
@Delete(':id') - 'role.delete'    // Deletar roles
@Get() - 'role.view'              // Ver todas as roles
@Get(':id') - 'role.view'         // Ver role específica
```

### **🔐 PermissionController** - `/permission`
```typescript
// Permissões administrativas - apenas ADMIN
@Post() - 'permission.create'     // Criar permissões
@Put(':id') - 'permission.update' // Editar permissões
@Delete(':id') - 'permission.delete' // Deletar permissões
@Get() - 'permission.view'        // Ver todas as permissões
@Get(':id') - 'permission.view'   // Ver permissão específica
```

### **📍 AddressController** - `/address`
```typescript
// Permissões básicas - ADMIN e usuários autenticados
@Post() - 'address.create'        // Criar endereços
@Put(':id') - 'address.update'    // Editar endereços
@Delete(':id') - 'address.delete' // Deletar endereços
@Get() - 'address.view'           // Ver todos os endereços
@Get(':id') - 'address.view'      // Ver endereço específico
```

### **🏢 InstitutionController** - `/institution`
```typescript
// Permissões administrativas - apenas ADMIN
@Post() - 'institution.create'    // Criar instituições
@Put(':id') - 'institution.update' // Editar instituições
@Delete(':id') - 'institution.delete' // Deletar instituições
@Get() - 'institution.view'       // Ver todas as instituições
@Get(':id') - 'institution.view'  // Ver instituição específica
```

### **💳 PlanController** - `/plan`
```typescript
// Permissões de visualização para todos, criação apenas ADMIN
@Get() - 'plan.view'              // Ver todos os planos (público)
@Get(':id') - 'plan.view'         // Ver plano específico (público)
@Post() - 'plan.create'           // Criar planos - ADMIN
@Put(':id') - 'plan.update'       // Editar planos - ADMIN
@Delete(':id') - 'plan.delete'    // Deletar planos - ADMIN
```

### **📋 SubscriptionController** - `/subscription`
```typescript
// Permissões administrativas e contextuais
@Get() - 'subscription.view'      // Ver todas as assinaturas - ADMIN
@Get(':id') - 'subscription.view.own' // Ver própria assinatura
@Post() - 'subscription.create'   // Criar assinaturas - ADMIN
@Put(':id') - 'subscription.update' // Editar assinaturas - ADMIN
@Delete(':id') - 'subscription.cancel' // Cancelar assinaturas - ADMIN
```

### **🔗 RolePermissionController** - `/role-permission`
```typescript
// Permissões administrativas - apenas ADMIN
@Post() - 'role_permission.create'    // Associar role-permissão
@Put(':id') - 'role_permission.update' // Editar associação
@Delete(':id') - 'role_permission.delete' // Remover associação
@Get() - 'role_permission.view'       // Ver todas as associações
@Get(':id') - 'role_permission.view'  // Ver associação específica
```

### **👥 UserRoleController** - `/user-role`
```typescript
// Permissões administrativas - apenas ADMIN
@Post() - 'user_role.create'      // Associar usuário-role
@Put(':id') - 'user_role.update'  // Editar associação
@Delete(':id') - 'user_role.delete' // Remover associação
@Get() - 'user_role.view'         // Ver todas as associações
@Get(':id') - 'user_role.view'    // Ver associação específica
```

---

## 🎯 **Estratégia de Implementação**

### **Nível 1: Autenticação Básica (Implementado)**
```typescript
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
```

### **Nível 2: Permissões Granulares (A implementar)**
```typescript
@UseGuards(JwtAuthGuard, PermissionGuard)
@RequirePermissions('permission.key')
@ApiBearerAuth('JWT-auth')
```

### **Nível 3: Permissões Contextuais (A implementar)**
```typescript
@UseGuards(JwtAuthGuard, PermissionGuard)
@RequirePermissions('permission.view.own', 'permission.view')
@ApiBearerAuth('JWT-auth')
async method(@CurrentUser() user: CurrentUserData) {
  // Lógica baseada na role do usuário
}
```

---

## 📊 **Matriz de Permissões por Role**

| Permissão | Aluno | Professor | Responsável | Admin |
|-----------|-------|-----------|-------------|-------|
| `user.view` | ❌ | ❌ | ❌ | ✅ |
| `user.create` | ❌ | ❌ | ❌ | ✅ |
| `role.view` | ❌ | ❌ | ❌ | ✅ |
| `permission.view` | ❌ | ❌ | ❌ | ✅ |
| `address.view` | ✅ | ✅ | ✅ | ✅ |
| `address.create` | ✅ | ✅ | ✅ | ✅ |
| `institution.view` | ❌ | ❌ | ❌ | ✅ |
| `plan.view` | ✅ | ✅ | ✅ | ✅ |
| `plan.create` | ❌ | ❌ | ❌ | ✅ |
| `subscription.view.own` | ✅ | ❌ | ✅ | ✅ |
| `subscription.view` | ❌ | ❌ | ❌ | ✅ |

---

## 🔧 **Implementação Prática**

### **Exemplo 1: Controller Público (PlanController)**
```typescript
@Controller('plan')
export class PlanController {
  @Get()
  // Sem guards - endpoint público
  async getPlans() { }

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('plan.create')
  @ApiBearerAuth('JWT-auth')
  async createPlan() { }
}
```

### **Exemplo 2: Controller Administrativo (UserController)**
```typescript
@Controller('user')
export class UserController {
  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('user.view')
  @ApiBearerAuth('JWT-auth')
  async getUsers() { }

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('user.create')
  @ApiBearerAuth('JWT-auth')
  async createUser() { }
}
```

### **Exemplo 3: Controller Contextual (SubscriptionController)**
```typescript
@Controller('subscription')
export class SubscriptionController {
  @Get('my-subscriptions')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('subscription.view.own')
  @ApiBearerAuth('JWT-auth')
  async getMySubscriptions(@CurrentUser() user: CurrentUserData) {
    // Lógica baseada na role
  }

  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('subscription.view')
  @ApiBearerAuth('JWT-auth')
  async getAllSubscriptions() { }
}
```

---

## 🚀 **Próximos Passos**

### **1. Aplicar Guards Básicos (Em progresso)**
- ✅ UserController
- ✅ RoleController  
- ✅ PermissionController
- ⚠️ AddressController (parcial)
- ❌ InstitutionController
- ❌ PlanController
- ❌ SubscriptionController
- ❌ RolePermissionController
- ❌ UserRoleController

### **2. Implementar Permissões Granulares**
- Adicionar `@RequirePermissions()` em todos os endpoints
- Configurar permissões específicas por ação
- Implementar lógica contextual para `.own` permissions

### **3. Testar Sistema Completo**
- Criar usuários de teste para cada role
- Validar acesso aos endpoints
- Verificar logs de auditoria

**🎯 Objetivo: Sistema de permissões granular completo e funcional!**
