# 🧪 **CHECKLIST DE TESTES - EDUSYS API**

## 📋 **ÍNDICE RÁPIDO**
- [🔐 Autenticação](#-autenticação-4-endpoints)
- [👥 Usuários](#-usuários-5-endpoints)
- [📚 Cursos](#-cursos-6-endpoints)
- [🏫 Turmas](#-turmas-7-endpoints)
- [📝 Matrículas](#-matrículas-9-endpoints)
- [📊 Frequência](#-frequência-8-endpoints)
- [📈 Notas](#-notas-7-endpoints)
- [💰 Financeiro](#-financeiro-6-endpoints)
- [🏢 Instituições](#-instituições-5-endpoints)
- [📍 Endereços](#-endereços-5-endpoints)
- [🔑 Permissões](#-permissões-5-endpoints)
- [👤 Roles](#-roles-5-endpoints)
- [🔗 Role-Permissões](#-role-permissões-5-endpoints)
- [👥 User-Roles](#-user-roles-5-endpoints)
- [📋 Planos](#-planos-6-endpoints)
- [💳 Assinaturas](#-assinaturas-5-endpoints)
- [💸 Pagamentos](#-pagamentos-3-endpoints)

---

## 🔐 **AUTENTICAÇÃO (4 endpoints)**

### **POST /auth/login**
- [X] ✅ Login com credenciais válidas
- [X] ❌ Login com email inválido
- [X] ❌ Login com senha incorreta
- [X] ❌ Login com campos obrigatórios vazios
- [X] ✅ Verificar se retorna accessToken e refreshToken
- [X] ✅ Verificar se retorna dados do usuário
- [X] ✅ Verificar se retorna dados da instituição

### **POST /auth/logout**
- [X] ✅ Logout com token válido
- [X] ❌ Logout sem token
- [X] ❌ Logout com token inválido
- [ ] ✅ Verificar se invalida o token

### **POST /auth/refresh**
- [ ] ✅ Refresh com refreshToken válido
- [ ] ❌ Refresh sem refreshToken
- [ ] ❌ Refresh com refreshToken inválido
- [ ] ❌ Refresh com refreshToken expirado
- [ ] ✅ Verificar se retorna novo accessToken

### **POST /auth/set-password**
- [ ] ✅ Definir senha com token válido
- [ ] ❌ Definir senha sem token
- [ ] ❌ Definir senha com token inválido
- [ ] ❌ Senhas não coincidem
- [ ] ❌ Senha muito fraca
- [ ] ✅ Verificar se senha é criptografada

---

## 👥 **USUÁRIOS (5 endpoints)**

### **POST /user**
- [ ] ✅ Criar usuário com dados válidos (ADMIN)
- [ ] ❌ Criar usuário sem autenticação
- [ ] ❌ Criar usuário sem permissão
- [ ] ❌ Email já existe
- [ ] ❌ Documento já existe
- [ ] ❌ Campos obrigatórios vazios
- [ ] ❌ Email inválido
- [ ] ❌ Documento inválido
- [ ] ✅ Verificar se gera token de primeiro acesso

### **PUT /user/:id**
- [ ] ✅ Atualizar usuário com dados válidos (ADMIN)
- [ ] ❌ Atualizar sem autenticação
- [ ] ❌ Atualizar sem permissão
- [ ] ❌ ID inexistente
- [ ] ❌ Email já existe (outro usuário)
- [ ] ❌ Token inválido
- [ ] ✅ Verificar auditoria (updated_by, updated_at)

### **DELETE /user/:id**
- [ ] ✅ Deletar usuário existente (ADMIN)
- [ ] ❌ Deletar sem autenticação
- [ ] ❌ Deletar sem permissão
- [ ] ❌ ID inexistente
- [ ] ❌ Token inválido
- [ ] ✅ Verificar se foi removido do banco

### **GET /user/:id**
- [ ] ✅ Buscar usuário existente (ADMIN)
- [ ] ❌ Buscar sem autenticação
- [ ] ❌ Buscar sem permissão
- [ ] ❌ ID inexistente
- [ ] ❌ ID inválido (não UUID)
- [ ] ✅ Verificar estrutura da resposta

### **GET /user**
- [ ] ✅ Listar todos os usuários (ADMIN)
- [ ] ❌ Listar sem autenticação
- [ ] ❌ Listar sem permissão
- [ ] ✅ Verificar se retorna array
- [ ] ✅ Verificar estrutura dos itens

---

## 📚 **CURSOS (6 endpoints)**

### **POST /course**
- [ ] ✅ Criar curso com dados válidos
- [ ] ❌ Criar sem autenticação
- [ ] ❌ Criar sem permissão
- [ ] ❌ Campos obrigatórios vazios
- [ ] ❌ Preço negativo
- [ ] ❌ Duração inválida
- [ ] ❌ Data de início posterior à data fim
- [ ] ✅ Verificar auditoria (created_by)

### **PUT /course/:id**
- [ ] ✅ Atualizar curso existente
- [ ] ❌ Atualizar sem autenticação
- [ ] ❌ Atualizar sem permissão
- [ ] ❌ ID inexistente
- [ ] ❌ Preço negativo
- [ ] ✅ Verificar auditoria (updated_by, updated_at)

### **DELETE /course/:id**
- [ ] ✅ Deletar curso existente
- [ ] ❌ Deletar sem autenticação
- [ ] ❌ Deletar sem permissão
- [ ] ❌ ID inexistente
- [ ] ✅ Verificar se foi removido

### **GET /course/:id**
- [ ] ✅ Buscar curso existente
- [ ] ❌ Buscar sem autenticação
- [ ] ❌ Buscar sem permissão
- [ ] ❌ ID inexistente
- [ ] ✅ Verificar estrutura da resposta

### **GET /course**
- [ ] ✅ Listar todos os cursos
- [ ] ❌ Listar sem autenticação
- [ ] ❌ Listar sem permissão
- [ ] ✅ Verificar se retorna array

### **GET /course/institution/:institutionId**
- [ ] ✅ Buscar cursos por instituição
- [ ] ❌ Buscar sem autenticação
- [ ] ❌ Buscar sem permissão
- [ ] ❌ Institution ID inexistente
- [ ] ✅ Verificar filtro por instituição

---

## 🏫 **TURMAS (7 endpoints)**

### **POST /class**
- [ ] ✅ Criar turma com dados válidos
- [ ] ❌ Criar sem autenticação
- [ ] ❌ Criar sem permissão
- [ ] ❌ Course ID inexistente
- [ ] ❌ Campos obrigatórios vazios
- [ ] ❌ Max students negativo
- [ ] ❌ Data de início posterior à data fim

### **PUT /class/:id**
- [ ] ✅ Atualizar turma existente
- [ ] ❌ Atualizar sem autenticação
- [ ] ❌ Atualizar sem permissão
- [ ] ❌ ID inexistente
- [ ] ✅ Verificar auditoria

### **DELETE /class/:id**
- [ ] ✅ Deletar turma existente
- [ ] ❌ Deletar sem autenticação
- [ ] ❌ Deletar sem permissão
- [ ] ❌ ID inexistente

### **GET /class/:id**
- [ ] ✅ Buscar turma existente
- [ ] ❌ Buscar sem autenticação
- [ ] ❌ Buscar sem permissão
- [ ] ❌ ID inexistente

### **GET /class**
- [ ] ✅ Listar todas as turmas
- [ ] ❌ Listar sem autenticação
- [ ] ❌ Listar sem permissão

### **GET /class/course/:courseId**
- [ ] ✅ Buscar turmas por curso
- [ ] ❌ Buscar sem autenticação
- [ ] ❌ Course ID inexistente

### **GET /class/available**
- [ ] ✅ Buscar turmas disponíveis
- [ ] ❌ Buscar sem autenticação
- [ ] ✅ Verificar se retorna apenas turmas com vagas

---

## 📝 **MATRÍCULAS (9 endpoints)**

### **POST /enrollment**
- [ ] ✅ Criar matrícula com dados válidos
- [ ] ❌ Criar sem autenticação
- [ ] ❌ Campos obrigatórios vazios
- [ ] ❌ Email inválido
- [ ] ❌ Documento inválido
- [ ] ❌ Course ID inexistente
- [ ] ❌ Class ID inexistente
- [ ] ❌ Turma sem vagas

### **DELETE /enrollment/:id**
- [ ] ✅ Deletar matrícula existente
- [ ] ❌ Deletar sem autenticação
- [ ] ❌ Deletar sem permissão
- [ ] ❌ ID inexistente

### **GET /enrollment/:id**
- [ ] ✅ Buscar matrícula existente
- [ ] ❌ Buscar sem autenticação
- [ ] ❌ ID inexistente

### **GET /enrollment**
- [ ] ✅ Listar todas as matrículas
- [ ] ❌ Listar sem autenticação

### **GET /enrollment/student/:studentId**
- [ ] ✅ Buscar matrículas por estudante
- [ ] ❌ Student ID inexistente

### **GET /enrollment/class/:classId**
- [ ] ✅ Buscar matrículas por turma
- [ ] ❌ Class ID inexistente

### **GET /enrollment/pending**
- [ ] ✅ Buscar matrículas pendentes
- [ ] ✅ Verificar se retorna apenas status 'pending'

### **PUT /enrollment/:id/approve**
- [ ] ✅ Aprovar matrícula pendente
- [ ] ❌ Aprovar sem autenticação
- [ ] ❌ Aprovar sem permissão
- [ ] ❌ ID inexistente
- [ ] ❌ Matrícula já aprovada
- [ ] ✅ Verificar mudança de status

### **PUT /enrollment/:id/reject**
- [ ] ✅ Rejeitar matrícula pendente
- [ ] ❌ Rejeitar sem autenticação
- [ ] ❌ Rejeitar sem permissão
- [ ] ❌ ID inexistente
- [ ] ❌ Sem motivo da rejeição
- [ ] ✅ Verificar mudança de status

---

## 📊 **FREQUÊNCIA (8 endpoints)**

### **POST /attendance**
- [ ] ✅ Registrar frequência com dados válidos
- [ ] ❌ Registrar sem autenticação
- [ ] ❌ Class ID inexistente
- [ ] ❌ Enrollment ID inexistente
- [ ] ❌ Status inválido
- [ ] ❌ Data futura

### **PUT /attendance/:id**
- [ ] ✅ Atualizar frequência existente
- [ ] ❌ Atualizar sem autenticação
- [ ] ❌ ID inexistente
- [ ] ❌ Status inválido

### **DELETE /attendance/:id**
- [ ] ✅ Deletar frequência existente
- [ ] ❌ Deletar sem autenticação
- [ ] ❌ ID inexistente

### **GET /attendance/:id**
- [ ] ✅ Buscar frequência existente
- [ ] ❌ ID inexistente

### **GET /attendance**
- [ ] ✅ Listar todas as frequências
- [ ] ❌ Listar sem autenticação

### **GET /attendance/class/:classId**
- [ ] ✅ Buscar frequência por turma
- [ ] ❌ Class ID inexistente
- [ ] ✅ Testar filtros de data

### **GET /attendance/student/:enrollmentId**
- [ ] ✅ Buscar frequência por estudante
- [ ] ❌ Enrollment ID inexistente
- [ ] ✅ Testar filtros de data

### **GET /attendance/report/:classId**
- [ ] ✅ Gerar relatório de frequência
- [ ] ❌ Class ID inexistente
- [ ] ✅ Verificar cálculos de percentual

---

## 📈 **NOTAS (7 endpoints)**

### **POST /grade**
- [ ] ✅ Lançar nota com dados válidos
- [ ] ❌ Lançar sem autenticação
- [ ] ❌ Enrollment ID inexistente
- [ ] ❌ Nota maior que nota máxima
- [ ] ❌ Nota negativa
- [ ] ❌ Peso inválido

### **PUT /grade/:id**
- [ ] ✅ Atualizar nota existente
- [ ] ❌ Atualizar sem autenticação
- [ ] ❌ ID inexistente
- [ ] ❌ Nota inválida

### **DELETE /grade/:id**
- [ ] ✅ Deletar nota existente
- [ ] ❌ Deletar sem autenticação
- [ ] ❌ ID inexistente

### **GET /grade/:id**
- [ ] ✅ Buscar nota existente
- [ ] ❌ ID inexistente

### **GET /grade**
- [ ] ✅ Listar todas as notas
- [ ] ❌ Listar sem autenticação

### **GET /grade/enrollment/:enrollmentId**
- [ ] ✅ Buscar notas por matrícula
- [ ] ❌ Enrollment ID inexistente
- [ ] ✅ Verificar cálculo de média

### **GET /grade/class/:classId**
- [ ] ✅ Buscar notas por turma
- [ ] ❌ Class ID inexistente

---

## 💰 **FINANCEIRO (6 endpoints)**

### **POST /financial/transactions**
- [ ] ✅ Criar transação com dados válidos
- [ ] ❌ Criar sem autenticação
- [ ] ❌ Tipo inválido (não income/expense)
- [ ] ❌ Valor negativo
- [ ] ❌ Moeda inválida

### **GET /financial/transactions**
- [ ] ✅ Listar transações
- [ ] ❌ Listar sem autenticação
- [ ] ✅ Testar filtros (tipo, categoria, status)
- [ ] ✅ Testar filtros de data

### **PUT /financial/transactions/:id**
- [ ] ✅ Atualizar transação existente
- [ ] ❌ Atualizar sem autenticação
- [ ] ❌ ID inexistente

### **GET /financial/summary**
- [ ] ✅ Obter resumo financeiro
- [ ] ❌ Obter sem autenticação
- [ ] ✅ Testar filtros de período
- [ ] ✅ Verificar cálculos

### **POST /financial/invoices**
- [ ] ✅ Criar fatura com dados válidos
- [ ] ❌ Criar sem autenticação
- [ ] ❌ Enrollment ID inexistente
- [ ] ❌ Valor negativo

### **POST /financial/invoices/:id/payment**
- [ ] ✅ Processar pagamento de fatura
- [ ] ❌ Processar sem autenticação
- [ ] ❌ Invoice ID inexistente
- [ ] ❌ Fatura já paga

---

## 🏢 **INSTITUIÇÕES (5 endpoints)**

### **POST /institution**
- [ ] ✅ Criar instituição com dados válidos (ADMIN)
- [ ] ❌ Criar sem autenticação
- [ ] ❌ Criar sem permissão
- [ ] ❌ CNPJ já existe
- [ ] ❌ Email já existe
- [ ] ❌ CNPJ inválido

### **PUT /institution/:id**
- [ ] ✅ Atualizar instituição existente (ADMIN)
- [ ] ❌ Atualizar sem autenticação
- [ ] ❌ Atualizar sem permissão
- [ ] ❌ ID inexistente

### **DELETE /institution/:id**
- [ ] ✅ Deletar instituição existente (ADMIN)
- [ ] ❌ Deletar sem autenticação
- [ ] ❌ Deletar sem permissão
- [ ] ❌ ID inexistente

### **GET /institution/:id**
- [ ] ✅ Buscar instituição existente (ADMIN)
- [ ] ❌ Buscar sem autenticação
- [ ] ❌ Buscar sem permissão
- [ ] ❌ ID inexistente

### **GET /institution**
- [ ] ✅ Listar todas as instituições (ADMIN)
- [ ] ❌ Listar sem autenticação
- [ ] ❌ Listar sem permissão

---

## 📍 **ENDEREÇOS (5 endpoints)**

### **POST /address**
- [ ] ✅ Criar endereço com dados válidos
- [ ] ❌ Criar sem autenticação
- [ ] ❌ User ID inexistente
- [ ] ❌ CEP inválido
- [ ] ❌ Campos obrigatórios vazios

### **PUT /address/:id**
- [ ] ✅ Atualizar endereço existente
- [ ] ❌ Atualizar sem autenticação
- [ ] ❌ ID inexistente

### **DELETE /address/:id**
- [ ] ✅ Deletar endereço existente
- [ ] ❌ Deletar sem autenticação
- [ ] ❌ ID inexistente

### **GET /address/:id**
- [ ] ✅ Buscar endereço existente
- [ ] ❌ ID inexistente

### **GET /address**
- [ ] ✅ Listar todos os endereços
- [ ] ❌ Listar sem autenticação

---

## 🔑 **PERMISSÕES (5 endpoints)**

### **POST /permission**
- [ ] ✅ Criar permissão com dados válidos (ADMIN)
- [ ] ❌ Criar sem autenticação
- [ ] ❌ Criar sem permissão
- [ ] ❌ Nome já existe
- [ ] ❌ Campos obrigatórios vazios

### **PUT /permission/:id**
- [ ] ✅ Atualizar permissão existente (ADMIN)
- [ ] ❌ Atualizar sem autenticação
- [ ] ❌ Atualizar sem permissão
- [ ] ❌ ID inexistente

### **DELETE /permission/:id**
- [ ] ✅ Deletar permissão existente (ADMIN)
- [ ] ❌ Deletar sem autenticação
- [ ] ❌ Deletar sem permissão
- [ ] ❌ ID inexistente

### **GET /permission/:id**
- [ ] ✅ Buscar permissão existente
- [ ] ❌ ID inexistente

### **GET /permission**
- [ ] ✅ Listar todas as permissões
- [ ] ❌ Listar sem autenticação

---

## 👤 **ROLES (5 endpoints)**

### **POST /role**
- [ ] ✅ Criar role com dados válidos
- [ ] ❌ Criar sem autenticação
- [ ] ❌ Nome já existe na instituição
- [ ] ❌ Institution ID inexistente

### **PUT /role/:id**
- [ ] ✅ Atualizar role existente
- [ ] ❌ Atualizar sem autenticação
- [ ] ❌ ID inexistente

### **DELETE /role/:id**
- [ ] ✅ Deletar role existente
- [ ] ❌ Deletar sem autenticação
- [ ] ❌ ID inexistente

### **GET /role/:id**
- [ ] ✅ Buscar role existente
- [ ] ❌ ID inexistente

### **GET /role**
- [ ] ✅ Listar todas as roles
- [ ] ❌ Listar sem autenticação

---

## 🔗 **ROLE-PERMISSÕES (5 endpoints)**

### **POST /role-permission**
- [ ] ✅ Atribuir permissão à role
- [ ] ❌ Atribuir sem autenticação
- [ ] ❌ Role ID inexistente
- [ ] ❌ Permission ID inexistente
- [ ] ❌ Relação já existe

### **DELETE /role-permission**
- [ ] ✅ Remover permissão da role
- [ ] ❌ Remover sem autenticação
- [ ] ❌ Relação inexistente

### **GET /role-permission/role/:roleId**
- [ ] ✅ Buscar permissões da role
- [ ] ❌ Role ID inexistente

### **GET /role-permission/permission/:permissionId**
- [ ] ✅ Buscar roles com a permissão
- [ ] ❌ Permission ID inexistente

### **GET /role-permission**
- [ ] ✅ Listar todas as atribuições
- [ ] ❌ Listar sem autenticação

---

## 👥 **USER-ROLES (5 endpoints)**

### **POST /user-role**
- [ ] ✅ Atribuir role ao usuário
- [ ] ❌ Atribuir sem autenticação
- [ ] ❌ User ID inexistente
- [ ] ❌ Role ID inexistente
- [ ] ❌ Relação já existe

### **DELETE /user-role**
- [ ] ✅ Remover role do usuário
- [ ] ❌ Remover sem autenticação
- [ ] ❌ Relação inexistente

### **GET /user-role/user/:userId**
- [ ] ✅ Buscar roles do usuário
- [ ] ❌ User ID inexistente

### **GET /user-role/role/:roleId**
- [ ] ✅ Buscar usuários com a role
- [ ] ❌ Role ID inexistente

### **GET /user-role**
- [ ] ✅ Listar todas as atribuições
- [ ] ❌ Listar sem autenticação

---

## 📋 **PLANOS (6 endpoints)**

### **POST /plan**
- [ ] ✅ Criar plano com dados válidos (ADMIN)
- [ ] ❌ Criar sem autenticação
- [ ] ❌ Criar sem permissão
- [ ] ❌ Preço negativo
- [ ] ❌ Billing period inválido

### **PUT /plan/:id**
- [ ] ✅ Atualizar plano existente (ADMIN)
- [ ] ❌ Atualizar sem autenticação
- [ ] ❌ Atualizar sem permissão
- [ ] ❌ ID inexistente

### **DELETE /plan/:id**
- [ ] ✅ Deletar plano existente (ADMIN)
- [ ] ❌ Deletar sem autenticação
- [ ] ❌ Deletar sem permissão
- [ ] ❌ ID inexistente

### **GET /plan/:id**
- [ ] ✅ Buscar plano existente (PÚBLICO)
- [ ] ❌ ID inexistente

### **GET /plan**
- [ ] ✅ Listar todos os planos (PÚBLICO)
- [ ] ✅ Verificar se retorna apenas planos ativos

### **GET /plan/active**
- [ ] ✅ Buscar planos ativos (PÚBLICO)
- [ ] ✅ Verificar filtro is_active = true

---

## 💳 **ASSINATURAS (5 endpoints)**

### **POST /subscription**
- [ ] ✅ Criar assinatura com dados válidos (ADMIN)
- [ ] ❌ Criar sem autenticação
- [ ] ❌ Criar sem permissão
- [ ] ❌ Institution ID inexistente
- [ ] ❌ Plan ID inexistente
- [ ] ❌ Plano inativo
- [ ] ❌ Instituição já tem assinatura ativa
- [ ] ✅ Verificar se atualiza instituição
- [ ] ✅ Verificar cálculo de período

### **PUT /subscription/:id/cancel**
- [ ] ✅ Cancelar assinatura existente (ADMIN)
- [ ] ❌ Cancelar sem autenticação
- [ ] ❌ Cancelar sem permissão
- [ ] ❌ ID inexistente
- [ ] ❌ Assinatura já cancelada

### **GET /subscription/:id**
- [ ] ✅ Buscar assinatura existente
- [ ] ❌ Buscar sem autenticação
- [ ] ❌ ID inexistente

### **GET /subscription**
- [ ] ✅ Listar todas as assinaturas (ADMIN)
- [ ] ❌ Listar sem autenticação
- [ ] ❌ Listar sem permissão

### **GET /subscription/institution/:id**
- [ ] ✅ Buscar assinatura da instituição (ADMIN)
- [ ] ❌ Buscar sem autenticação
- [ ] ❌ Institution ID inexistente

---

## 💸 **PAGAMENTOS (3 endpoints)**

### **POST /payment/create-session**
- [ ] ✅ Criar sessão de pagamento
- [ ] ❌ Criar sem autenticação
- [ ] ❌ Valor inválido
- [ ] ❌ Moeda inválida

### **POST /payment/create-subscription**
- [ ] ✅ Criar assinatura Stripe
- [ ] ❌ Criar sem autenticação
- [ ] ❌ Plan ID inexistente
- [ ] ❌ Payment method inválido

### **POST /payment/webhook**
- [ ] ✅ Processar webhook Stripe (SEM AUTH)
- [ ] ❌ Signature inválida
- [ ] ❌ Evento inválido
- [ ] ✅ Verificar processamento de eventos

---

## 📊 **RESUMO DE TESTES**

### **📈 Estatísticas:**
- **Total de Endpoints:** 100+
- **Controllers:** 17
- **Cenários de Teste:** 400+

### **🎯 Tipos de Teste:**
- ✅ **Casos de Sucesso** (Happy Path)
- ❌ **Casos de Erro** (Error Handling)
- 🔒 **Testes de Autenticação**
- 🛡️ **Testes de Autorização**
- 📝 **Validação de Dados**
- 🔍 **Testes de Busca**
- 🧮 **Validação de Cálculos**

### **🔧 Como Usar:**
1. **Marque** os testes conforme executa
2. **Use** a collection do Postman
3. **Documente** bugs encontrados
4. **Verifique** logs da aplicação

**🎯 Use este checklist para garantir 100% de cobertura de testes!**
