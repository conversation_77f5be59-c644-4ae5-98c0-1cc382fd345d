# 🛡️ **Implementação Completa: PermissionGuard e PermissionService - EduSys**

## ✅ **Arquivos Implementados**

### **1. PermissionService**
- **Arquivo:** `src/infrastructure/services/permission/permission.service.ts`
- **<PERSON><PERSON><PERSON><PERSON>:** `src/infrastructure/services/permission/permission.module.ts`
- **Funcionalidades:** Buscar permissões, verificar roles, validar acessos

### **2. PermissionGuard**
- **Arquivo:** `src/infrastructure/common/guards/permission.guard.ts`
- **Módulo:** `src/infrastructure/common/guards/guards.module.ts`
- **Funcionalidades:** Validar permissões em endpoints

### **3. Decorators**
- **Arquivo:** `src/infrastructure/common/decorators/require-permissions.decorator.ts`
- **Decorators:** `@RequirePermissions()`, `@RequireRoles()`, `@RequireAuth()`

### **4. JWT Strategy Atualizada**
- **Arquivo:** `src/infrastructure/common/strategies/jwt.strategy.ts`
- **Atualização:** Inclui role do usuário no token
- **Interface:** `CurrentUserData` atualizada com campo `role`

---

## 🔧 **Como Usar nos Controllers**

### **Exemplo 1: Controller de Cursos**
```typescript
import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../common/guards/permission.guard';
import { RequirePermissions } from '../common/decorators/require-permissions.decorator';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { CurrentUserData } from '../common/decorators/current-user.decorator';

@Controller('course')
@ApiTags('Courses')
@ApiBearerAuth('JWT-auth')
export class CourseController {
  
  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.view')
  async getCourses(@CurrentUser() user: CurrentUserData) {
    // ✅ Todos os tipos podem ver cursos
    // Aluno, Professor, Responsável, Admin
    return this.courseService.findByInstitution(user.institution_id);
  }

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.create')
  async createCourse(@Body() data: CreateCourseDto, @CurrentUser() user: CurrentUserData) {
    // ✅ Apenas ADMIN pode criar cursos
    return this.courseService.create(data, user);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.update')
  async updateCourse(
    @Param('id') id: string,
    @Body() data: UpdateCourseDto,
    @CurrentUser() user: CurrentUserData
  ) {
    // ✅ Apenas ADMIN pode editar cursos
    return this.courseService.update(id, data, user);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.delete')
  async deleteCourse(@Param('id') id: string, @CurrentUser() user: CurrentUserData) {
    // ✅ Apenas ADMIN pode deletar cursos
    return this.courseService.delete(id, user);
  }
}
```

### **Exemplo 2: Controller de Matrículas**
```typescript
@Controller('enrollment')
@ApiTags('Enrollments')
@ApiBearerAuth('JWT-auth')
export class EnrollmentController {

  @Get('my-enrollments')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.view.own')
  async getMyEnrollments(@CurrentUser() user: CurrentUserData) {
    // ✅ ALUNO, RESPONSÁVEL e ADMIN podem ver suas próprias matrículas
    
    switch (user.role) {
      case 'student':
        return this.enrollmentService.findByStudentId(user.sub);
      
      case 'guardian':
        return this.enrollmentService.findByGuardianId(user.sub);
      
      case 'admin':
        return this.enrollmentService.findByInstitutionId(user.institution_id);
      
      default:
        return [];
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.view')
  async getAllEnrollments(@CurrentUser() user: CurrentUserData) {
    // ✅ Apenas PROFESSOR e ADMIN podem ver todas as matrículas
    return this.enrollmentService.findByInstitutionId(user.institution_id);
  }

  @Put(':id/approve')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.approve')
  async approveEnrollment(
    @Param('id') id: string,
    @CurrentUser() user: CurrentUserData
  ) {
    // ✅ Apenas ADMIN pode aprovar matrículas
    return this.enrollmentService.approve(id, user.sub);
  }

  @Put(':id/reject')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.reject')
  async rejectEnrollment(
    @Param('id') id: string,
    @Body() data: { reason: string },
    @CurrentUser() user: CurrentUserData
  ) {
    // ✅ Apenas ADMIN pode rejeitar matrículas
    return this.enrollmentService.reject(id, data.reason, user.sub);
  }
}
```

### **Exemplo 3: Controller de Presença**
```typescript
@Controller('attendance')
@ApiTags('Attendance')
@ApiBearerAuth('JWT-auth')
export class AttendanceController {

  @Get('my-attendance')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.view.own')
  async getMyAttendance(@CurrentUser() user: CurrentUserData) {
    // ✅ ALUNO, RESPONSÁVEL e ADMIN podem ver presença própria
    
    if (user.role === 'student') {
      return this.attendanceService.findByStudentId(user.sub);
    }
    
    if (user.role === 'guardian') {
      return this.attendanceService.findByGuardianId(user.sub);
    }
    
    if (user.role === 'admin') {
      return this.attendanceService.findByInstitutionId(user.institution_id);
    }
    
    return [];
  }

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.create')
  async recordAttendance(
    @Body() data: CreateAttendanceDto,
    @CurrentUser() user: CurrentUserData
  ) {
    // ✅ Apenas PROFESSOR e ADMIN podem registrar presença
    return this.attendanceService.create(data, user.sub);
  }

  @Post('bulk')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.create')
  async recordBulkAttendance(
    @Body() data: BulkAttendanceDto,
    @CurrentUser() user: CurrentUserData
  ) {
    // ✅ Apenas PROFESSOR e ADMIN podem registrar presença em lote
    return this.attendanceService.createBulk(data, user.sub);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.update')
  async updateAttendance(
    @Param('id') id: string,
    @Body() data: UpdateAttendanceDto,
    @CurrentUser() user: CurrentUserData
  ) {
    // ✅ Apenas PROFESSOR e ADMIN podem editar presença
    return this.attendanceService.update(id, data, user.sub);
  }
}
```

### **Exemplo 4: Múltiplas Permissões**
```typescript
@Controller('grade')
@ApiTags('Grades')
@ApiBearerAuth('JWT-auth')
export class GradeController {

  @Get('report/:enrollmentId')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view.own', 'grade.view')
  async getGradeReport(
    @Param('enrollmentId') enrollmentId: string,
    @CurrentUser() user: CurrentUserData
  ) {
    // ✅ Usuário precisa ter 'grade.view.own' OU 'grade.view'
    // Permite tanto visualização própria quanto geral
    return this.gradeService.generateReport(enrollmentId, user);
  }

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.create')
  async createGrade(
    @Body() data: CreateGradeDto,
    @CurrentUser() user: CurrentUserData
  ) {
    // ✅ Apenas PROFESSOR e ADMIN podem lançar notas
    return this.gradeService.create(data, user.sub);
  }
}
```

---

## 🚀 **Para Usar em Novos Controllers**

### **1. Importar Guards e Decorators:**
```typescript
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../common/guards/permission.guard';
import { RequirePermissions } from '../common/decorators/require-permissions.decorator';
import { CurrentUser } from '../common/decorators/current-user.decorator';
```

### **2. Aplicar Guards:**
```typescript
@UseGuards(JwtAuthGuard, PermissionGuard)
@RequirePermissions('permission.key')
```

### **3. Obter Dados do Usuário:**
```typescript
async method(@CurrentUser() user: CurrentUserData) {
  // user.sub = ID do usuário
  // user.role = Role do usuário (student, teacher, guardian, admin)
  // user.institution_id = ID da instituição
}
```

---

## 🎯 **Benefícios da Implementação**

### **✅ Segurança Robusta:**
- Controle granular por permissão
- Validação automática em todos os endpoints
- Logs detalhados de tentativas de acesso

### **✅ Flexibilidade:**
- Múltiplas permissões por endpoint
- Permissões contextuais (.own)
- Fácil adição de novas permissões

### **✅ Manutenibilidade:**
- Decorators simples de usar
- Código limpo e organizado
- Padrão consistente em todo o projeto

### **✅ Performance:**
- Cache de permissões por usuário
- Queries otimizadas
- Validação eficiente

**🎉 Sistema de permissões completo e pronto para uso em todo o sistema de matrículas!**
