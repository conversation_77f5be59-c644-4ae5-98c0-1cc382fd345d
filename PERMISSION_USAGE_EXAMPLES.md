# 🛡️ **Exemplos de Uso das Permissões - EduSys**

## 📋 **Como Usar as Permissõ<PERSON> Criadas**

Este documento mostra exemplos práticos de como implementar o controle de acesso usando as permissões criadas na migration de seed.

---

## 🔧 **1. PermissionGuard (Implementação)**

```typescript
// src/infrastructure/common/guards/permission.guard.ts
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PermissionService } from '../services/permission.service';

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly permissionService: PermissionService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.get<string[]>(
      'permissions',
      context.getHandler()
    );

    if (!requiredPermissions) {
      return true; // Sem permissões requeridas = acesso liberado
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return false; // Usuário não autenticado
    }

    // Verificar se o usuário tem pelo menos uma das permissões requeridas
    for (const permission of requiredPermissions) {
      const hasPermission = await this.permissionService.hasPermission(
        user.sub,
        permission
      );
      if (hasPermission) {
        return true;
      }
    }

    return false; // Usuário não tem nenhuma das permissões
  }
}
```

---

## 🔑 **2. Decorator de Permissões**

```typescript
// src/infrastructure/common/decorators/require-permissions.decorator.ts
import { SetMetadata } from '@nestjs/common';

export const RequirePermissions = (...permissions: string[]) =>
  SetMetadata('permissions', permissions);
```

---

## 🏫 **3. Exemplos nos Controllers do Sistema de Matrículas**

### **📚 Course Controller**
```typescript
@Controller('course')
@ApiTags('Courses')
export class CourseController {
  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.view')
  @ApiBearerAuth('JWT-auth')
  async getCourses(@CurrentUser() user: CurrentUserData) {
    // ✅ Todos os tipos podem ver cursos
    return this.courseService.findByInstitution(user.institution_id);
  }

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.create')
  @ApiBearerAuth('JWT-auth')
  async createCourse(@Body() data: CreateCourseDto) {
    // ✅ Apenas ADMIN pode criar cursos
    return this.courseService.create(data);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.update')
  @ApiBearerAuth('JWT-auth')
  async updateCourse(@Param('id') id: string, @Body() data: UpdateCourseDto) {
    // ✅ Apenas ADMIN pode editar cursos
    return this.courseService.update(id, data);
  }
}
```

### **📝 Enrollment Controller**
```typescript
@Controller('enrollment')
@ApiTags('Enrollments')
export class EnrollmentController {
  @Get('my-enrollments')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.view.own')
  @ApiBearerAuth('JWT-auth')
  async getMyEnrollments(@CurrentUser() user: CurrentUserData) {
    // ✅ ALUNO, RESPONSÁVEL e ADMIN podem ver suas próprias matrículas
    
    if (user.role === 'student') {
      // Aluno vê suas matrículas
      return this.enrollmentService.findByStudentId(user.sub);
    }
    
    if (user.role === 'guardian') {
      // Responsável vê matrículas dos filhos
      return this.enrollmentService.findByGuardianId(user.sub);
    }
    
    if (user.role === 'admin') {
      // Admin vê todas da instituição
      return this.enrollmentService.findByInstitutionId(user.institution_id);
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.view')
  @ApiBearerAuth('JWT-auth')
  async getAllEnrollments(@CurrentUser() user: CurrentUserData) {
    // ✅ Apenas PROFESSOR e ADMIN podem ver todas as matrículas
    return this.enrollmentService.findByInstitutionId(user.institution_id);
  }

  @Put(':id/approve')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.approve')
  @ApiBearerAuth('JWT-auth')
  async approveEnrollment(@Param('id') id: string) {
    // ✅ Apenas ADMIN pode aprovar matrículas
    return this.enrollmentService.approve(id);
  }

  @Put(':id/reject')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.reject')
  @ApiBearerAuth('JWT-auth')
  async rejectEnrollment(
    @Param('id') id: string,
    @Body() data: { reason: string }
  ) {
    // ✅ Apenas ADMIN pode rejeitar matrículas
    return this.enrollmentService.reject(id, data.reason);
  }
}
```

### **📊 Attendance Controller**
```typescript
@Controller('attendance')
@ApiTags('Attendance')
export class AttendanceController {
  @Get('my-attendance')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.view.own')
  @ApiBearerAuth('JWT-auth')
  async getMyAttendance(@CurrentUser() user: CurrentUserData) {
    // ✅ ALUNO, RESPONSÁVEL e ADMIN podem ver presença própria
    
    if (user.role === 'student') {
      return this.attendanceService.findByStudentId(user.sub);
    }
    
    if (user.role === 'guardian') {
      return this.attendanceService.findByGuardianId(user.sub);
    }
    
    if (user.role === 'admin') {
      return this.attendanceService.findByInstitutionId(user.institution_id);
    }
  }

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.create')
  @ApiBearerAuth('JWT-auth')
  async recordAttendance(@Body() data: CreateAttendanceDto) {
    // ✅ Apenas PROFESSOR e ADMIN podem registrar presença
    return this.attendanceService.create(data);
  }

  @Post('bulk')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.create')
  @ApiBearerAuth('JWT-auth')
  async recordBulkAttendance(@Body() data: BulkAttendanceDto) {
    // ✅ Apenas PROFESSOR e ADMIN podem registrar presença em lote
    return this.attendanceService.createBulk(data);
  }
}
```

### **📝 Grade Controller**
```typescript
@Controller('grade')
@ApiTags('Grades')
export class GradeController {
  @Get('my-grades')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view.own')
  @ApiBearerAuth('JWT-auth')
  async getMyGrades(@CurrentUser() user: CurrentUserData) {
    // ✅ ALUNO, RESPONSÁVEL e ADMIN podem ver notas próprias
    
    if (user.role === 'student') {
      return this.gradeService.findByStudentId(user.sub);
    }
    
    if (user.role === 'guardian') {
      return this.gradeService.findByGuardianId(user.sub);
    }
    
    if (user.role === 'admin') {
      return this.gradeService.findByInstitutionId(user.institution_id);
    }
  }

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.create')
  @ApiBearerAuth('JWT-auth')
  async createGrade(@Body() data: CreateGradeDto) {
    // ✅ Apenas PROFESSOR e ADMIN podem lançar notas
    return this.gradeService.create(data);
  }

  @Get('report/:enrollmentId')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view.own', 'grade.view')
  @ApiBearerAuth('JWT-auth')
  async getGradeReport(
    @Param('enrollmentId') enrollmentId: string,
    @CurrentUser() user: CurrentUserData
  ) {
    // ✅ Múltiplas permissões: própria OU geral
    // Validar se o usuário pode ver este boletim específico
    return this.gradeService.generateReport(enrollmentId, user);
  }
}
```

### **💰 Financial Controller**
```typescript
@Controller('financial')
@ApiTags('Financial')
export class FinancialController {
  @Get('dashboard')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('financial.view')
  @ApiBearerAuth('JWT-auth')
  async getFinancialDashboard(@CurrentUser() user: CurrentUserData) {
    // ✅ Apenas ADMIN pode acessar financeiro
    return this.financialService.getDashboard(user.institution_id);
  }

  @Get('transactions')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('transaction.view')
  @ApiBearerAuth('JWT-auth')
  async getTransactions(@CurrentUser() user: CurrentUserData) {
    // ✅ Apenas ADMIN pode ver transações
    return this.transactionService.findByInstitution(user.institution_id);
  }

  @Post('transactions')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('transaction.create')
  @ApiBearerAuth('JWT-auth')
  async createTransaction(@Body() data: CreateTransactionDto) {
    // ✅ Apenas ADMIN pode criar transações
    return this.transactionService.create(data);
  }
}
```

### **🧾 Invoice Controller**
```typescript
@Controller('invoice')
@ApiTags('Invoices')
export class InvoiceController {
  @Get('my-invoices')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('invoice.view.own')
  @ApiBearerAuth('JWT-auth')
  async getMyInvoices(@CurrentUser() user: CurrentUserData) {
    // ✅ ALUNO, RESPONSÁVEL e ADMIN podem ver suas faturas
    
    if (user.role === 'student') {
      return this.invoiceService.findByStudentId(user.sub);
    }
    
    if (user.role === 'guardian') {
      return this.invoiceService.findByGuardianId(user.sub);
    }
    
    if (user.role === 'admin') {
      return this.invoiceService.findByInstitutionId(user.institution_id);
    }
  }

  @Put(':id/pay')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('invoice.pay')
  @ApiBearerAuth('JWT-auth')
  async markAsPaid(
    @Param('id') id: string,
    @Body() data: PayInvoiceDto
  ) {
    // ✅ Apenas ADMIN pode marcar como paga
    return this.invoiceService.markAsPaid(id, data);
  }
}
```

---

## 🎯 **Resumo dos Padrões**

### **✅ Permissões .own (Contextuais):**
- Filtram dados baseado no tipo de usuário
- Aluno vê apenas seus dados
- Responsável vê dados dos filhos
- Admin vê todos da instituição

### **✅ Permissões Administrativas:**
- Apenas Admin tem acesso
- Controle total do sistema
- Operações críticas protegidas

### **✅ Permissões de Professor:**
- Foco em gestão educacional
- Sem acesso financeiro
- Limitado ao escopo pedagógico

### **✅ Múltiplas Permissões:**
- Use array para permissões alternativas
- Usuário precisa ter apenas uma delas
- Flexibilidade no controle de acesso

**🎉 Sistema de permissões granular implementado com sucesso!**
