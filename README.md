# 🎓 EduSys API - Sistema Educacional Multi-Tenant

Sistema completo de gestão educacional com arquitetura multi-tenant, autenticação robusta e integração com Stripe para pagamentos.

## 🚀 Características Principais

- **🏢 Multi-Tenant:** Isolamento completo de dados por instituição
- **🔐 Autenticação JWT:** Sistema robusto com 2FA opcional
- **👥 Sistema de Permissões:** Controle granular de acesso
- **📚 Módulos Educacionais:** Cursos, Classes, Matrículas, Presença e Notas
- **💳 Pagamentos Stripe:** Suporte a pagamentos únicos e recorrentes
- **📊 API RESTful:** Documentação completa com Swagger
- **🛡️ Segurança:** Guards, validações e auditoria

## 🏗️ Arquitetura

```
src/
├── domain/           # Entidades de domínio e abstrações
├── usecases/         # Casos de uso (regras de negócio)
└── infrastructure/   # Implementações técnicas
    ├── config/       # Configurações (DB, JWT, etc.)
    ├── controllers/  # Controllers REST
    ├── entities/     # Entidades TypeORM
    ├── repositories/ # Implementações de repositórios
    └── common/       # Guards, decorators, interceptors
```

## 🛠️ Tecnologias

- **Backend:** NestJS, TypeScript
- **Banco de Dados:** PostgreSQL com TypeORM
- **Autenticação:** JWT, Passport, Speakeasy (2FA)
- **Pagamentos:** Stripe
- **Documentação:** Swagger/OpenAPI
- **Validação:** Class-validator
- **Testes:** Jest

## 📋 Pré-requisitos

- Node.js 18+
- PostgreSQL 13+
- Yarn
- Conta Stripe (para pagamentos)

## ⚙️ Configuração

### 1. Variáveis de Ambiente

Crie um arquivo `.env` baseado no `.env.example`:

```bash
# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=seu_usuario
DATABASE_PASSWORD=sua_senha
DATABASE_NAME=edusys

# JWT
JWT_SECRET=seu_jwt_secret_super_seguro
JWT_EXPIRES_IN=24h

# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email (opcional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=sua_senha_app
```

### 2. Instalação

```bash
# Instalar dependências
yarn install

# Executar migrations
yarn run:migration

# Executar seeds (dados iniciais)
yarn seed

# Iniciar em desenvolvimento
yarn start:dev
```

## 📚 Documentação

Toda a documentação está organizada na pasta `docs/`:

- **[Guia de Autenticação](docs/AUTHENTICATION_GUIDE.md)** - Sistema de login e 2FA
- **[Sistema de Permissões](docs/PERMISSION_SYSTEM_COMPLETE_IMPLEMENTATION.md)** - Controle de acesso
- **[Módulos Educacionais](docs/EDUCATIONAL_MIGRATIONS_DOCUMENTATION.md)** - Cursos, classes, etc.
- **[API Endpoints](docs/ENROLLMENT_API_ENDPOINTS.md)** - Documentação da API
- **[Swagger Guide](docs/SWAGGER_BEARER_TOKEN_GUIDE.md)** - Como usar a documentação

## 🔗 Endpoints Principais

### Autenticação
- `POST /auth/login` - Login
- `POST /auth/register` - Registro
- `POST /auth/2fa/enable` - Ativar 2FA
- `POST /auth/2fa/verify` - Verificar 2FA

### Educacional
- `GET /course` - Listar cursos
- `GET /class` - Listar classes
- `GET /enrollment` - Listar matrículas
- `GET /attendance` - Listar presenças
- `GET /grade` - Listar notas

### Pagamentos
- `POST /payment/create-session` - Criar sessão de pagamento
- `POST /payment/subscription` - Criar assinatura
- `POST /payment/webhook` - Webhook do Stripe

## 🧪 Testes

```bash
# Testes unitários
yarn test

# Testes e2e
yarn test:e2e

# Coverage
yarn test:cov
```

## 🚀 Deploy

```bash
# Build
yarn build

# Executar migrations em produção
yarn run:migration:prod

# Iniciar em produção
yarn start:prod
```

## 📄 Licença

Este projeto está sob a licença MIT.

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📞 Suporte

Para suporte, entre em contato através dos issues do GitHub.
