# 🔐 **Migration de Seed: Roles e Permissões Padrão - EduSys**

## 📋 **<PERSON><PERSON><PERSON> Geral**

Migration criada: `CreateDefaultRolesAndPermissions1752067147881`

Esta migration estabelece a base do sistema de controle de acesso criando:
- ✅ **47 permissões** granulares para todos os módulos
- ✅ **4 roles padrão** para os tipos de usuário
- ✅ **Associações role_permissions** configuradas
- ✅ **Sistema RBAC completo** pronto para uso

---

## 🎭 **ROLES CRIADAS**

### **1. 👨‍🎓 ALUNO (student)**
- **ID:** `11111111-1111-1111-1111-111111111111`
- **Descrição:** Estudante matriculado em cursos
- **Acesso:** Visualizar suas próprias informações

### **2. 👨‍🏫 PROFESSOR (teacher)**
- **ID:** `22222222-2222-2222-2222-222222222222`
- **Descrição:** <PERSON>ente responsável por turmas
- **Acesso:** Gerenciar turmas, presença e notas

### **3. 👨‍👩‍👧‍👦 RESPONSÁVEL (guardian)**
- **ID:** `33333333-3333-3333-3333-333333333333`
- **Descrição:** Responsável legal por alunos menores
- **Acesso:** Ver informações dos alunos sob sua responsabilidade

### **4. 👨‍💼 ADMINISTRAÇÃO (admin)**
- **ID:** `44444444-4444-4444-4444-444444444444`
- **Descrição:** Funcionário administrativo da instituição
- **Acesso:** Controle total do sistema

---

## 🔑 **PERMISSÕES CRIADAS (47 total)**

### **📚 Módulo Educacional - Cursos (4)**
- `course.view` - Visualizar cursos disponíveis
- `course.create` - Criar novos cursos
- `course.update` - Editar cursos existentes
- `course.delete` - Deletar cursos

### **🏫 Módulo Educacional - Turmas (4)**
- `class.view` - Visualizar turmas
- `class.create` - Criar novas turmas
- `class.update` - Editar turmas existentes
- `class.delete` - Deletar turmas

### **📝 Módulo Educacional - Matrículas (6)**
- `enrollment.view` - Ver todas as matrículas da instituição
- `enrollment.view.own` - Ver suas próprias matrículas
- `enrollment.create` - Criar novas matrículas
- `enrollment.approve` - Aprovar matrículas pendentes
- `enrollment.reject` - Rejeitar matrículas pendentes
- `enrollment.cancel` - Cancelar matrículas ativas

### **📊 Módulo Educacional - Presença (5)**
- `attendance.view` - Ver todas as presenças
- `attendance.view.own` - Ver sua própria presença
- `attendance.create` - Registrar presença dos alunos
- `attendance.update` - Editar registros de presença
- `attendance.delete` - Deletar registros de presença

### **📝 Módulo Educacional - Notas (5)**
- `grade.view` - Ver todas as notas
- `grade.view.own` - Ver suas próprias notas
- `grade.create` - Lançar notas dos alunos
- `grade.update` - Editar notas existentes
- `grade.delete` - Deletar notas

### **💰 Módulo Financeiro - Geral (2)**
- `financial.view` - Acessar módulo financeiro
- `financial.manage` - Gerenciar transações financeiras

### **💳 Módulo Financeiro - Transações (4)**
- `transaction.view` - Ver transações financeiras
- `transaction.create` - Criar transações financeiras
- `transaction.update` - Editar transações financeiras
- `transaction.delete` - Deletar transações financeiras

### **🧾 Módulo Financeiro - Faturas (6)**
- `invoice.view` - Ver todas as faturas
- `invoice.view.own` - Ver suas próprias faturas
- `invoice.create` - Criar faturas
- `invoice.update` - Editar faturas
- `invoice.pay` - Marcar fatura como paga
- `invoice.cancel` - Cancelar faturas

### **💳 Módulo Financeiro - Métodos de Pagamento (4)**
- `payment_method.view` - Ver métodos de pagamento
- `payment_method.create` - Criar métodos de pagamento
- `payment_method.update` - Editar métodos de pagamento
- `payment_method.delete` - Deletar métodos de pagamento

### **📊 Relatórios (2)**
- `report.view` - Visualizar relatórios
- `report.generate` - Gerar relatórios personalizados

### **📈 Dashboard (2)**
- `dashboard.view` - Acessar dashboard
- `dashboard.stats` - Ver estatísticas da instituição

---

## 🎯 **MATRIZ DE PERMISSÕES POR ROLE**

| Permissão | Aluno | Professor | Responsável | Admin |
|-----------|-------|-----------|-------------|-------|
| `course.view` | ✅ | ✅ | ✅ | ✅ |
| `course.create` | ❌ | ❌ | ❌ | ✅ |
| `class.view` | ✅ | ✅ | ✅ | ✅ |
| `enrollment.view.own` | ✅ | ❌ | ✅ | ✅ |
| `enrollment.view` | ❌ | ✅ | ❌ | ✅ |
| `enrollment.approve` | ❌ | ❌ | ❌ | ✅ |
| `attendance.view.own` | ✅ | ❌ | ✅ | ✅ |
| `attendance.create` | ❌ | ✅ | ❌ | ✅ |
| `grade.view.own` | ✅ | ❌ | ✅ | ✅ |
| `grade.create` | ❌ | ✅ | ❌ | ✅ |
| `financial.view` | ❌ | ❌ | ❌ | ✅ |
| `invoice.view.own` | ✅ | ❌ | ✅ | ✅ |
| `dashboard.view` | ✅ | ✅ | ✅ | ✅ |
| `report.view` | ❌ | ✅ | ❌ | ✅ |

### **📊 Resumo por Role:**
- **👨‍🎓 Aluno:** 7 permissões (apenas visualização própria)
- **👨‍🏫 Professor:** 11 permissões (gestão educacional)
- **👨‍👩‍👧‍👦 Responsável:** 7 permissões (visualização dependentes)
- **👨‍💼 Admin:** 47 permissões (controle total)

---

## 🚀 **Como Executar a Migration**

### **1. Executar a Migration:**
```bash
yarn run:migration
```

### **2. Verificar se foi Aplicada:**
```bash
yarn typeorm migration:show
```

### **3. Verificar Dados Criados:**
```sql
-- Ver roles criadas
SELECT * FROM core.roles WHERE created_by = 'system';

-- Ver permissões criadas
SELECT * FROM core.permissions WHERE created_by = 'system';

-- Ver associações
SELECT r.name as role, p.name as permission 
FROM core.role_permissions rp
JOIN core.roles r ON rp.role_id = r.id
JOIN core.permissions p ON rp.permission_id = p.id
WHERE r.created_by = 'system'
ORDER BY r.name, p.name;
```

---

## 🔧 **Próximos Passos**

### **1. Implementar PermissionGuard:**
```typescript
@Injectable()
export class PermissionGuard implements CanActivate {
  // Validar permissões específicas
}
```

### **2. Criar PermissionService:**
```typescript
@Injectable()
export class PermissionService {
  async getUserPermissions(userId: string): Promise<string[]>
  async hasPermission(userId: string, permission: string): Promise<boolean>
}
```

### **3. Atualizar JWT Strategy:**
```typescript
// Incluir role do usuário no token
return {
  sub: user.id,
  email: user.email,
  name: user.name,
  institution_id: user.institution_id,
  role: userRole // 🆕 Adicionar
};
```

### **4. Aplicar nos Controllers:**
```typescript
@Get()
@UseGuards(JwtAuthGuard, PermissionGuard)
@RequirePermissions('course.view')
async getCourses() {
  // Endpoint protegido
}
```

---

## ⚠️ **Observações Importantes**

1. **Roles Globais:** As roles criadas são globais (institution_id = NULL)
2. **Permissões Granulares:** Sistema permite controle fino de acesso
3. **Extensibilidade:** Fácil adicionar novas permissões/roles
4. **Segurança:** Princípio do menor privilégio aplicado
5. **Auditoria:** Todos os registros têm created_by = 'system'

---

## 🎯 **Resultado Final**

✅ **Sistema RBAC completo** pronto para uso
✅ **4 tipos de usuário** bem definidos
✅ **47 permissões granulares** cobrindo todo o sistema
✅ **Base sólida** para controle de acesso
✅ **Escalabilidade** para futuras expansões

**🎉 Agora o sistema está pronto para implementar controle de acesso granular em todos os módulos!**
