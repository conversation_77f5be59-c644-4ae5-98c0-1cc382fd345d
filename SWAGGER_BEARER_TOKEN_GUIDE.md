# 🔐 **Guia de Configuração do Bearer Token no Swagger - EduSys API**

## 📋 **<PERSON><PERSON><PERSON> Geral**

O Swagger da EduSys API foi configurado para suportar autenticação via <PERSON>er Token (JWT), permitindo testar todas as rotas protegidas diretamente na interface do Swagger.

---

## 🚀 **Configuração Implementada**

### **1. Configuração no main.ts**
```typescript
const config = new DocumentBuilder()
  .setTitle('EduSYS API')
  .setDescription('Education Software - Sistema de Gestão Educacional')
  .setVersion('1.0')
  .addBearerAuth(
    {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
      name: 'JWT',
      description: 'Enter JWT token',
      in: 'header'
    },
    'JWT-auth' // Nome importante para referenciar nos controllers
  )
  .build();
```

### **2. Anotação nos Controllers**
```typescript
@Controller('auth')
export class AuthController {
  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')  // 🔑 Indica que precisa de autenticação
  @ApiResponse({
    status: HttpStatus.OK,
    type: LogoutPresenter,
    description: 'User logged out successfully'
  })
  async logout(@Req() request: Request) {
    // Rota protegida
  }
}
```

---

## 🔧 **Como Usar no Swagger**

### **Passo 1: Acessar o Swagger**
```
http://localhost:3000/api
```

### **Passo 2: Fazer Login**
1. Vá para o endpoint `POST /auth/login`
2. Clique em "Try it out"
3. Insira as credenciais:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
4. Execute a requisição
5. Copie o `accessToken` da resposta

### **Passo 3: Configurar Autenticação**
1. No topo da página do Swagger, clique no botão **"Authorize"** 🔒
2. Na modal que abrir, cole o token JWT no campo "Value"
3. **Formato**: `Bearer seu-jwt-token-aqui` (o "Bearer " é adicionado automaticamente)
4. Clique em "Authorize"
5. Clique em "Close"

### **Passo 4: Testar Rotas Protegidas**
Agora você pode testar qualquer rota que tenha o ícone de cadeado 🔒, como:
- `POST /auth/logout`
- `PUT /user/:id`
- `POST /permission`
- E outras rotas protegidas

---

## 🛡️ **Rotas com Bearer Auth Configuradas**

### **Autenticação**
- ✅ `POST /auth/logout` - Logout (protegida)

### **Usuários**
- ✅ `PUT /user/:id` - Atualizar usuário (protegida)

### **Permissões**
- ✅ `POST /permission` - Criar permissão (protegida)

### **Outras Rotas**
Todas as rotas que usam `@UseGuards(JwtAuthGuard)` devem ter `@ApiBearerAuth('JWT-auth')` para aparecer no Swagger.

---

## 📝 **Exemplo de Implementação**

### **Controller Completo**
```typescript
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { AuditInterceptor } from '../../common/interceptors/audit.interceptor';

@Controller('example')
@ApiTags('Example')
export class ExampleController {
  @Post()
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')  // 🔑 Configuração do Bearer Auth
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Resource created successfully'
  })
  async create(@Body() data: CreateDto) {
    // Rota protegida com auditoria automática
  }

  @Get()
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Public endpoint - no auth required'
  })
  async getPublic() {
    // Rota pública - sem autenticação
  }
}
```

---

## 🎯 **Benefícios da Configuração**

### **1. Interface Intuitiva**
- ✅ Botão "Authorize" visível no topo
- ✅ Ícones de cadeado 🔒 nas rotas protegidas
- ✅ Indicação clara de quais rotas precisam de autenticação

### **2. Facilidade de Teste**
- ✅ Configure uma vez, use em todas as rotas
- ✅ Token é automaticamente incluído nos headers
- ✅ Não precisa copiar/colar token em cada requisição

### **3. Documentação Clara**
- ✅ Desenvolvedores sabem quais rotas são protegidas
- ✅ Formato do token está documentado
- ✅ Processo de autenticação é claro

---

## 🔍 **Verificação Visual no Swagger**

### **Antes da Autenticação**
- Rotas protegidas mostram ícone de cadeado 🔒
- Botão "Authorize" no topo está disponível
- Tentativas de usar rotas protegidas retornam 401

### **Após a Autenticação**
- Botão "Authorize" mostra que está autenticado
- Rotas protegidas funcionam normalmente
- Headers incluem automaticamente: `Authorization: Bearer {token}`

---

## 🚨 **Troubleshooting**

### **Token Expirado**
- **Problema**: Requisições retornam 401 após um tempo
- **Solução**: Use o endpoint `POST /auth/refresh` para renovar o token

### **Token Inválido**
- **Problema**: Erro 401 mesmo com token
- **Solução**: Verifique se o token está correto e não foi truncado

### **Rota Não Protegida no Swagger**
- **Problema**: Rota deveria ter cadeado mas não tem
- **Solução**: Adicione `@ApiBearerAuth('JWT-auth')` no método

---

## ✅ **Status da Implementação**

- [x] **Configuração do Bearer Auth no Swagger**
- [x] **Botão Authorize funcional**
- [x] **Rotas protegidas identificadas**
- [x] **Headers automáticos configurados**
- [x] **Documentação completa**
- [x] **Exemplos de uso implementados**

---

**🎉 O Swagger está completamente configurado para autenticação Bearer Token!**

**📍 Acesse: http://localhost:3000/api**
