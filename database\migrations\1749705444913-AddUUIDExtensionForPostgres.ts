import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUUIDExtensionForPostgres1749705444913
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE SCHEMA core;`);
    await queryRunner.query(`CREATE SCHEMA educational;`);
    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP EXTENSION IF EXISTS "uuid-ossp";`);
    await queryRunner.query(`DROP SCHEMA core CASCADE;`);
    await queryRunner.query(`DROP SCHEMA educational CASCADE;`);
  }
}
