import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateUsersTable1749709407572 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: 'core',
        name: 'users',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'name', type: 'varchar', isNullable: false },
          { name: 'email', type: 'varchar', isUnique: true, isNullable: false },
          { name: 'password_hash', type: 'varchar', isNullable: true },
          { name: 'phone_number', type: 'varchar', isNullable: false },
          {
            name: 'tax_id',
            type: 'varchar',
            isNullable: false,
            isUnique: true
          },
          { name: 'birth_date', type: 'date', isNullable: false },
          { name: 'institution_id', type: 'uuid', isNullable: true },
          {
            name: 'is_enabled_2fa_app',
            type: 'boolean',
            isNullable: false,
            default: false
          },
          {
            name: 'is_email_verified',
            type: 'boolean',
            isNullable: false,
            default: false
          },
          {
            name: 'is_phone_verified',
            type: 'boolean',
            isNullable: false,
            default: false
          },
          { name: 'secret_key_2fa', type: 'varchar', isNullable: false },
          {
            name: 'actived',
            type: 'boolean',
            isNullable: false,
            default: true
          },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: true
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true },
          { name: 'updated_by', type: 'uuid', isNullable: true }
        ]
      }),
      true
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      new Table({
        schema: 'core',
        name: 'users'
      }),
      true
    );
  }
}
