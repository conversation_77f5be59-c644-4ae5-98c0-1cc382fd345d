import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey
} from 'typeorm';

export class CreateInstitutionTableAndAddressTableWithForeignKeys1750125809671
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: 'core',
        name: 'addresses',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'street', type: 'varchar', isNullable: false },
          { name: 'number', type: 'varchar', isNullable: false },
          { name: 'complement', type: 'varchar', isNullable: true },
          { name: 'neighborhood', type: 'varchar', isNullable: false },
          { name: 'city', type: 'varchar', isNullable: false },
          { name: 'state', type: 'varchar', isNullable: false },
          {
            name: 'zip_code',
            type: 'varchar',
            isNullable: false
          },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true }
        ]
      }),
      true
    );
    await queryRunner.createTable(
      new Table({
        schema: 'core',
        name: 'institutions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'name', type: 'varchar', isNullable: false },
          { name: 'legal_name', type: 'varchar', isNullable: false },
          { name: 'phone_number', type: 'varchar', isNullable: false },
          {
            name: 'tax_id',
            type: 'varchar',
            isNullable: false,
            isUnique: true
          },
          { name: 'incorporation_date', type: 'date', isNullable: true },
          { name: 'address_id', type: 'uuid', isNullable: false },
          { name: 'plan_id', type: 'uuid', isNullable: true },
          { name: 'main_institution_id', type: 'uuid', isNullable: true },
          {
            name: 'status',
            type: 'varchar',
            isNullable: false,
            default: "'inactive'"
          },
          { name: 'email', type: 'varchar', isNullable: false },
          { name: 'website', type: 'varchar', isNullable: true },
          {
            name: 'actived',
            type: 'boolean',
            isNullable: false,
            default: true
          },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          {
            name: 'created_by',
            type: 'uuid'
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true },
          { name: 'updated_by', type: 'uuid', isNullable: true }
        ]
      }),
      true
    );
    await queryRunner.createForeignKey(
      'core.users',
      new TableForeignKey({
        name: 'users_institution_id_fkey',
        columnNames: ['institution_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'core.institutions',
        onDelete: 'NO ACTION',
        onUpdate: 'CASCADE'
      })
    );
    await queryRunner.createForeignKey(
      'core.institutions',
      new TableForeignKey({
        name: 'institutions_address_id_fkey',
        columnNames: ['address_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'core.addresses',
        onDelete: 'NO ACTION',
        onUpdate: 'CASCADE'
      })
    );
    await queryRunner.createForeignKey(
      'core.institutions',
      new TableForeignKey({
        name: 'institutions_main_institution_id_fkey',
        columnNames: ['main_institution_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'core.institutions',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey(
      'core.institutions',
      'institutions_main_institution_id_fkey'
    );
    await queryRunner.dropForeignKey(
      'core.institutions',
      'institutions_address_id_fkey'
    );
    await queryRunner.dropForeignKey('core.users', 'users_institution_id_fkey');
    await queryRunner.dropTable(
      new Table({
        schema: 'core',
        name: 'institutions'
      }),
      true
    );
    await queryRunner.dropTable(
      new Table({
        schema: 'core',
        name: 'addresses'
      }),
      true
    );
  }
}
