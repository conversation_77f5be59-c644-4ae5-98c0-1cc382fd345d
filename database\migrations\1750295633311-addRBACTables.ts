import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey
} from 'typeorm';

export class AddRBACTables1750295633311 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: 'core',
        name: 'permissions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'name', type: 'varchar', isNullable: false },
          { name: 'key', type: 'varchar', isNullable: false },
          { name: 'description', type: 'varchar', isNullable: true },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: true
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true },
          { name: 'updated_by', type: 'uuid', isNullable: true }
        ]
      }),
      true
    );

    await queryRunner.createTable(
      new Table({
        schema: 'core',
        name: 'roles',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'name', type: 'varchar', isNullable: false },
          { name: 'key', type: 'varchar', isNullable: false, isUnique: true },
          { name: 'description', type: 'varchar', isNullable: true },
          { name: 'institution_id', type: 'uuid', isNullable: true },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: true
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true },
          { name: 'updated_by', type: 'uuid', isNullable: true }
        ]
      }),
      true
    );

    await queryRunner.createTable(
      new Table({
        schema: 'core',
        name: 'role_permissions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'role_id', type: 'uuid', isNullable: false },
          { name: 'permission_id', type: 'uuid', isNullable: false },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: true
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true },
          { name: 'updated_by', type: 'uuid', isNullable: true }
        ]
      }),
      true
    );

    await queryRunner.createTable(
      new Table({
        schema: 'core',
        name: 'user_roles',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'role_id', type: 'uuid', isNullable: false },
          { name: 'user_id', type: 'uuid', isNullable: true },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: true
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true },
          { name: 'updated_by', type: 'uuid', isNullable: true }
        ]
      }),
      true
    );

    await queryRunner.createForeignKey(
      'core.roles',
      new TableForeignKey({
        name: 'roles_institution_id_fkey',
        columnNames: ['institution_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'core.institutions',
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'core.role_permissions',
      new TableForeignKey({
        name: 'role_permissions_role_id_fkey',
        columnNames: ['role_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'core.roles',
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'core.role_permissions',
      new TableForeignKey({
        name: 'role_permission_id_fkey',
        columnNames: ['permission_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'core.permissions',
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'core.user_roles',
      new TableForeignKey({
        name: 'user_roles_user_id_fkey',
        columnNames: ['user_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'core.users',
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'core.user_roles',
      new TableForeignKey({
        name: 'user_roles_role_id_fkey',
        columnNames: ['role_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'core.roles',
        onDelete: 'CASCADE'
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey('core.roles', 'roles_institution_id_fkey');
    await queryRunner.dropForeignKey(
      'core.role_permissions',
      'role_permissions_role_id_fkey'
    );
    await queryRunner.dropForeignKey(
      'core.role_permissions',
      'role_permission_id_fkey'
    );
    await queryRunner.dropForeignKey(
      'core.user_roles',
      'user_roles_user_id_fkey'
    );
    await queryRunner.dropForeignKey(
      'core.user_roles',
      'user_roles_role_id_fkey'
    );
    await queryRunner.dropTable('core.permissions');
    await queryRunner.dropTable('core.roles');
    await queryRunner.dropTable('core.role_permissions');
    await queryRunner.dropTable('core.user_roles');
  }
}
