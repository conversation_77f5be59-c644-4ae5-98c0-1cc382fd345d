import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePlansTable1752028318664 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE core.plans (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'BRL',
        billing_period VARCHAR(20) NOT NULL DEFAULT 'monthly', -- monthly, yearly
        max_users INTEGER,
        max_institutions INTEGER,
        features JSONB DEFAULT '{}',
        stripe_price_id VARCHAR(255),
        is_active BOOLEAN DEFAULT true,
        created_by UUID NOT NULL,
        updated_by UUID NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    await queryRunner.query(`
      CREATE INDEX idx_plans_active ON core.plans(is_active);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_plans_billing_period ON core.plans(billing_period);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE core.plans;`);
  }
}
