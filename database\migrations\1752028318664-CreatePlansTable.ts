import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreatePlansTable1752028318664 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: 'core',
        name: 'plans',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'name', type: 'varchar', length: '255', isNullable: false },
          { name: 'description', type: 'text', isNullable: true },
          {
            name: 'price',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: false
          },
          {
            name: 'currency',
            type: 'varchar',
            length: '3',
            isNullable: false,
            default: "'BRL'"
          },
          {
            name: 'billing_period',
            type: 'varchar',
            length: '20',
            isNullable: false,
            default: "'monthly'"
          },
          { name: 'max_users', type: 'integer', isNullable: true },
          { name: 'max_institutions', type: 'integer', isNullable: true },
          {
            name: 'features',
            type: 'jsonb',
            isNullable: false,
            default: "'{}'"
          },
          {
            name: 'stripe_price_id',
            type: 'varchar',
            length: '255',
            isNullable: true
          },
          {
            name: 'is_active',
            type: 'boolean',
            isNullable: false,
            default: true
          },
          { name: 'created_by', type: 'uuid', isNullable: false },
          { name: 'updated_by', type: 'uuid', isNullable: false },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true }
        ]
      }),
      true
    );

    await queryRunner.query(`
      CREATE INDEX idx_plans_active ON core.plans(is_active);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_plans_billing_period ON core.plans(billing_period);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      new Table({
        schema: 'core',
        name: 'plans'
      }),
      true
    );
  }
}
