import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class CreateSubscriptionsTable1752028337593 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: 'core',
        name: 'subscriptions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'institution_id', type: 'uuid', isNullable: false },
          { name: 'plan_id', type: 'uuid', isNullable: false },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            isNullable: false,
            default: "'active'"
          },
          { name: 'stripe_subscription_id', type: 'varchar', length: '255', isNullable: true },
          { name: 'stripe_customer_id', type: 'varchar', length: '255', isNullable: true },
          { name: 'current_period_start', type: 'timestamp', isNullable: true },
          { name: 'current_period_end', type: 'timestamp', isNullable: true },
          { name: 'trial_start', type: 'timestamp', isNullable: true },
          { name: 'trial_end', type: 'timestamp', isNullable: true },
          { name: 'canceled_at', type: 'timestamp', isNullable: true },
          { name: 'ended_at', type: 'timestamp', isNullable: true },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: false,
            default: "'{}'"
          },
          { name: 'created_by', type: 'uuid', isNullable: false },
          { name: 'updated_by', type: 'uuid', isNullable: false },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true }
        ]
      }),
      true
    );

    await queryRunner.createForeignKey(
      'subscriptions',
      new TableForeignKey({
        columnNames: ['institution_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'institutions',
        referencedSchema: 'core',
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'subscriptions',
      new TableForeignKey({
        columnNames: ['plan_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'plans',
        referencedSchema: 'core',
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.query(`
      CREATE INDEX idx_subscriptions_institution ON core.subscriptions(institution_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_subscriptions_plan ON core.subscriptions(plan_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_subscriptions_status ON core.subscriptions(status);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_subscriptions_stripe_subscription ON core.subscriptions(stripe_subscription_id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      new Table({
        schema: 'core',
        name: 'subscriptions'
      }),
      true
    );
  }
}
