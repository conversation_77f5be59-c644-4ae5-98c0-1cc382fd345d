import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSubscriptionsTable1752028337593 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE core.subscriptions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        institution_id UUID NOT NULL,
        plan_id UUID NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, inactive, canceled, expired, trial
        stripe_subscription_id VARCHAR(255),
        stripe_customer_id VARCHAR(255),
        current_period_start TIMESTAMP,
        current_period_end TIMESTAMP,
        trial_start TIMESTAMP,
        trial_end TIMESTAMP,
        canceled_at TIMESTAMP,
        ended_at TIMESTAMP,
        metadata JSONB DEFAULT '{}',
        created_by UUID NOT NULL,
        updated_by UUID NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        CONSTRAINT fk_subscriptions_institution
          FOREIGN KEY (institution_id) REFERENCES core.institutions(id) ON DELETE CASCADE,
        CONSTRAINT fk_subscriptions_plan
          FOREIGN KEY (plan_id) REFERENCES core.plans(id) ON DELETE RESTRICT,
        CONSTRAINT unique_active_subscription_per_institution
          UNIQUE (institution_id) DEFERRABLE INITIALLY DEFERRED
      );
    `);

    await queryRunner.query(`
      CREATE INDEX idx_subscriptions_institution ON core.subscriptions(institution_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_subscriptions_plan ON core.subscriptions(plan_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_subscriptions_status ON core.subscriptions(status);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_subscriptions_stripe_subscription ON core.subscriptions(stripe_subscription_id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE core.subscriptions;`);
  }
}
