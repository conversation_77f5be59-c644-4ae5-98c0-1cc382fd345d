import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedPlansTable1752028395654 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Inserir planos mensais
    await queryRunner.manager
      .createQueryBuilder()
      .insert()
      .into('core.plans')
      .values([
        {
          name: 'Básico',
          description: 'Plano ideal para pequenas instituições de ensino',
          price: 29.9,
          currency: 'BRL',
          billing_period: 'monthly',
          max_users: 50,
          max_institutions: 1,
          features: { modules: ['users', 'classes', 'grades'] },
          is_active: true,
          created_by: () => 'uuid_generate_v4()',
          updated_by: () => 'uuid_generate_v4()'
        },
        {
          name: 'Profissional',
          description: 'Plano completo para instituições em crescimento',
          price: 79.9,
          currency: 'BRL',
          billing_period: 'monthly',
          max_users: 200,
          max_institutions: 3,
          features: {
            modules: ['users', 'classes', 'grades', 'reports', 'analytics']
          },
          is_active: true,
          created_by: () => 'uuid_generate_v4()',
          updated_by: () => 'uuid_generate_v4()'
        },
        {
          name: 'Enterprise',
          description: 'Solução completa para grandes instituições',
          price: 199.9,
          currency: 'BRL',
          billing_period: 'monthly',
          max_users: 1000,
          max_institutions: 10,
          features: { modules: ['all'], custom_integrations: true },
          is_active: true,
          created_by: () => 'uuid_generate_v4()',
          updated_by: () => 'uuid_generate_v4()'
        }
      ])
      .execute();

    // Inserir planos anuais com desconto
    await queryRunner.manager
      .createQueryBuilder()
      .insert()
      .into('core.plans')
      .values([
        {
          name: 'Básico Anual',
          description: 'Plano básico com desconto anual (2 meses grátis)',
          price: 299.0,
          currency: 'BRL',
          billing_period: 'yearly',
          max_users: 50,
          max_institutions: 1,
          features: { modules: ['users', 'classes', 'grades'] },
          is_active: true,
          created_by: () => 'uuid_generate_v4()',
          updated_by: () => 'uuid_generate_v4()'
        },
        {
          name: 'Profissional Anual',
          description: 'Plano profissional com desconto anual (2 meses grátis)',
          price: 799.0,
          currency: 'BRL',
          billing_period: 'yearly',
          max_users: 200,
          max_institutions: 3,
          features: {
            modules: ['users', 'classes', 'grades', 'reports', 'analytics']
          },
          is_active: true,
          created_by: () => 'uuid_generate_v4()',
          updated_by: () => 'uuid_generate_v4()'
        },
        {
          name: 'Enterprise Anual',
          description: 'Solução enterprise com desconto anual (2 meses grátis)',
          price: 1999.0,
          currency: 'BRL',
          billing_period: 'yearly',
          max_users: 1000,
          max_institutions: 10,
          features: { modules: ['all'], custom_integrations: true },
          is_active: true,
          created_by: () => 'uuid_generate_v4()',
          updated_by: () => 'uuid_generate_v4()'
        }
      ])
      .execute();
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.manager
      .createQueryBuilder()
      .delete()
      .from('core.plans')
      .execute();
  }
}
