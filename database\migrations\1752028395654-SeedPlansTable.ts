import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedPlansTable1752028395654 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Inserir planos básicos
    await queryRunner.query(`
      INSERT INTO core.plans (
        id, name, description, price, currency, billing_period,
        max_users, max_institutions, features, is_active,
        created_by, updated_by
      ) VALUES
      (
        uuid_generate_v4(),
        'Básico',
        'Plano ideal para pequenas instituições de ensino',
        29.90,
        'BRL',
        'monthly',
        50,
        1,
        '{"modules": ["users", "classes", "grades"], "storage_gb": 5, "support": "email"}',
        true,
        uuid_generate_v4(),
        uuid_generate_v4()
      ),
      (
        uuid_generate_v4(),
        'Profissional',
        'Plano completo para instituições em crescimento',
        79.90,
        'BRL',
        'monthly',
        200,
        3,
        '{"modules": ["users", "classes", "grades", "reports", "analytics"], "storage_gb": 25, "support": "email_phone"}',
        true,
        uuid_generate_v4(),
        uuid_generate_v4()
      ),
      (
        uuid_generate_v4(),
        'Enterprise',
        'Solução completa para grandes instituições',
        199.90,
        'BRL',
        'monthly',
        1000,
        10,
        '{"modules": ["all"], "storage_gb": 100, "support": "priority", "custom_integrations": true}',
        true,
        uuid_generate_v4(),
        uuid_generate_v4()
      );
    `);

    // Inserir planos anuais com desconto
    await queryRunner.query(`
      INSERT INTO core.plans (
        id, name, description, price, currency, billing_period,
        max_users, max_institutions, features, is_active,
        created_by, updated_by
      ) VALUES
      (
        uuid_generate_v4(),
        'Básico Anual',
        'Plano básico com desconto anual (2 meses grátis)',
        299.00,
        'BRL',
        'yearly',
        50,
        1,
        '{"modules": ["users", "classes", "grades"], "storage_gb": 5, "support": "email"}',
        true,
        uuid_generate_v4(),
        uuid_generate_v4()
      ),
      (
        uuid_generate_v4(),
        'Profissional Anual',
        'Plano profissional com desconto anual (2 meses grátis)',
        799.00,
        'BRL',
        'yearly',
        200,
        3,
        '{"modules": ["users", "classes", "grades", "reports", "analytics"], "storage_gb": 25, "support": "email_phone"}',
        true,
        uuid_generate_v4(),
        uuid_generate_v4()
      ),
      (
        uuid_generate_v4(),
        'Enterprise Anual',
        'Solução enterprise com desconto anual (2 meses grátis)',
        1999.00,
        'BRL',
        'yearly',
        1000,
        10,
        '{"modules": ["all"], "storage_gb": 100, "support": "priority", "custom_integrations": true}',
        true,
        uuid_generate_v4(),
        uuid_generate_v4()
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DELETE FROM core.plans;`);
  }
}
