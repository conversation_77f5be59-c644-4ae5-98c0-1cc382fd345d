import { MigrationInterface, QueryRunner, TableForeign<PERSON><PERSON> } from 'typeorm';

export class RenamePlanIdToSubscriptionIdInInstitutions1752030727619 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Primeiro, remover qualquer foreign key existente na coluna plan_id (se existir)
    const table = await queryRunner.getTable('core.institutions');
    const existingForeignKey = table?.foreignKeys.find(
      fk => fk.columnNames.indexOf('plan_id') !== -1
    );

    if (existingForeignKey) {
      await queryRunner.dropForeignKey('core.institutions', existingForeignKey);
    }

    // Renomear a coluna plan_id para subscription_id
    await queryRunner.renameColumn(
      'core.institutions',
      'plan_id',
      'subscription_id'
    );

    // Criar foreign key para subscription_id referenciando subscriptions.id
    await queryRunner.createForeignKey(
      'core.institutions',
      new TableForeignKey({
        columnNames: ['subscription_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'subscriptions',
        referencedSchema: 'core',
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE'
      })
    );

    // Criar índice para melhor performance
    await queryRunner.query(`
      CREATE INDEX idx_institutions_subscription_id ON core.institutions(subscription_id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remover o índice
    await queryRunner.query(`
      DROP INDEX IF EXISTS core.idx_institutions_subscription_id;
    `);

    // Remover a foreign key
    const table = await queryRunner.getTable('core.institutions');
    const foreignKey = table?.foreignKeys.find(
      fk => fk.columnNames.indexOf('subscription_id') !== -1
    );

    if (foreignKey) {
      await queryRunner.dropForeignKey('core.institutions', foreignKey);
    }

    // Renomear a coluna de volta para plan_id
    await queryRunner.renameColumn(
      'core.institutions',
      'subscription_id',
      'plan_id'
    );
  }
}
