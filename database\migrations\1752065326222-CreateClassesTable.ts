import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey
} from 'typeorm';

export class CreateClassesTable1752065326222 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: 'educational',
        name: 'classes',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'course_id', type: 'uuid', isNullable: false },
          { name: 'name', type: 'varchar', length: '255', isNullable: false },
          { name: 'description', type: 'text', isNullable: true },
          { name: 'max_students', type: 'integer', isNullable: false },
          {
            name: 'current_students',
            type: 'integer',
            isNullable: false,
            default: 0
          },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            isNullable: false,
            default: "'open'"
          },
          { name: 'start_date', type: 'timestamp', isNullable: true },
          { name: 'end_date', type: 'timestamp', isNullable: true },
          {
            name: 'schedule',
            type: 'jsonb',
            isNullable: true,
            comment: 'Horários e dias da semana da turma'
          },
          { name: 'created_by', type: 'uuid', isNullable: false },
          { name: 'updated_by', type: 'uuid', isNullable: true },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true }
        ]
      }),
      true
    );

    // Foreign Keys
    await queryRunner.createForeignKey(
      'educational.classes',
      new TableForeignKey({
        columnNames: ['course_id'],
        referencedTableName: 'educational.courses',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'educational.classes',
      new TableForeignKey({
        columnNames: ['created_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'educational.classes',
      new TableForeignKey({
        columnNames: ['updated_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    // Indexes
    await queryRunner.query(`
      CREATE INDEX idx_classes_course ON educational.classes(course_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_classes_status ON educational.classes(status);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_classes_dates ON educational.classes(start_date, end_date);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_classes_students ON educational.classes(current_students, max_students);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      new Table({
        schema: 'educational',
        name: 'classes'
      }),
      true
    );
  }
}
