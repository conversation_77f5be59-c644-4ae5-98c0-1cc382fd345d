import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey
} from 'typeorm';

export class CreateEnrollmentsTable1752065333854 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: 'educational',
        name: 'enrollments',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'student_id', type: 'uuid', isNullable: false },
          { name: 'class_id', type: 'uuid', isNullable: false },
          { name: 'institution_id', type: 'uuid', isNullable: false },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            isNullable: false,
            default: "'pending'"
          },
          {
            name: 'enrollment_date',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          { name: 'approval_date', type: 'timestamp', isNullable: true },
          { name: 'approved_by', type: 'uuid', isNullable: true },
          { name: 'rejection_reason', type: 'text', isNullable: true },
          { name: 'notes', type: 'text', isNullable: true },
          { name: 'created_by', type: 'uuid', isNullable: true },
          { name: 'updated_by', type: 'uuid', isNullable: true },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true }
        ]
      }),
      true
    );

    // Foreign Keys
    await queryRunner.createForeignKey(
      'educational.enrollments',
      new TableForeignKey({
        columnNames: ['student_id'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'educational.enrollments',
      new TableForeignKey({
        columnNames: ['class_id'],
        referencedTableName: 'educational.classes',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'educational.enrollments',
      new TableForeignKey({
        columnNames: ['institution_id'],
        referencedTableName: 'core.institutions',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'educational.enrollments',
      new TableForeignKey({
        columnNames: ['approved_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'educational.enrollments',
      new TableForeignKey({
        columnNames: ['created_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'educational.enrollments',
      new TableForeignKey({
        columnNames: ['updated_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    // Indexes
    await queryRunner.query(`
      CREATE INDEX idx_enrollments_student ON educational.enrollments(student_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_enrollments_class ON educational.enrollments(class_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_enrollments_institution ON educational.enrollments(institution_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_enrollments_status ON educational.enrollments(status);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_enrollments_dates ON educational.enrollments(enrollment_date, approval_date);
    `);

    // Unique constraint para evitar matrículas duplicadas
    await queryRunner.query(`
      CREATE UNIQUE INDEX idx_enrollments_unique_student_class
      ON educational.enrollments(student_id, class_id)
      WHERE status NOT IN ('rejected', 'cancelled');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      new Table({
        schema: 'educational',
        name: 'enrollments'
      }),
      true
    );
  }
}
