import {
    MigrationInterface,
    QueryRunner,
    Table,
    TableForeignKey
} from 'typeorm';

export class CreateAttendanceTable1752066229843 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: 'educational',
        name: 'attendance',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'enrollment_id', type: 'uuid', isNullable: false },
          { name: 'class_date', type: 'date', isNullable: false },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            isNullable: false,
            default: "'present'"
          },
          { name: 'notes', type: 'text', isNullable: true },
          { name: 'recorded_by', type: 'uuid', isNullable: false },
          { name: 'created_by', type: 'uuid', isNullable: false },
          { name: 'updated_by', type: 'uuid', isNullable: true },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true }
        ]
      }),
      true
    );

    // Foreign Keys
    await queryRunner.createForeignKey(
      'educational.attendance',
      new TableForeignKey({
        columnNames: ['enrollment_id'],
        referencedTableName: 'educational.enrollments',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'educational.attendance',
      new TableForeignKey({
        columnNames: ['recorded_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'educational.attendance',
      new TableForeignKey({
        columnNames: ['created_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'educational.attendance',
      new TableForeignKey({
        columnNames: ['updated_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    // Indexes
    await queryRunner.query(`
      CREATE INDEX idx_attendance_enrollment ON educational.attendance(enrollment_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_attendance_date ON educational.attendance(class_date);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_attendance_status ON educational.attendance(status);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_attendance_recorded_by ON educational.attendance(recorded_by);
    `);

    // Unique constraint para evitar registros duplicados de presença
    await queryRunner.query(`
      CREATE UNIQUE INDEX idx_attendance_unique_enrollment_date
      ON educational.attendance(enrollment_id, class_date);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      new Table({
        schema: 'educational',
        name: 'attendance'
      }),
      true
    );
  }
}
