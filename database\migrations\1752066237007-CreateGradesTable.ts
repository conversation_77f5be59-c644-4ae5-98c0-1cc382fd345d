import {
    MigrationInterface,
    QueryRunner,
    Table,
    TableForeignKey
} from 'typeorm';

export class CreateGradesTable1752066237007 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: 'educational',
        name: 'grades',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'enrollment_id', type: 'uuid', isNullable: false },
          {
            name: 'assessment_type',
            type: 'varchar',
            length: '50',
            isNullable: false
          },
          {
            name: 'assessment_name',
            type: 'varchar',
            length: '255',
            isNullable: false
          },
          {
            name: 'grade',
            type: 'decimal',
            precision: 5,
            scale: 2,
            isNullable: false
          },
          {
            name: 'max_grade',
            type: 'decimal',
            precision: 5,
            scale: 2,
            isNullable: false
          },
          {
            name: 'weight',
            type: 'decimal',
            precision: 3,
            scale: 2,
            isNullable: false,
            default: 1.0
          },
          { name: 'assessment_date', type: 'date', isNullable: false },
          { name: 'recorded_by', type: 'uuid', isNullable: false },
          { name: 'created_by', type: 'uuid', isNullable: false },
          { name: 'updated_by', type: 'uuid', isNullable: true },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true }
        ]
      }),
      true
    );

    // Foreign Keys
    await queryRunner.createForeignKey(
      'educational.grades',
      new TableForeignKey({
        columnNames: ['enrollment_id'],
        referencedTableName: 'educational.enrollments',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'educational.grades',
      new TableForeignKey({
        columnNames: ['recorded_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'educational.grades',
      new TableForeignKey({
        columnNames: ['created_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'educational.grades',
      new TableForeignKey({
        columnNames: ['updated_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    // Indexes
    await queryRunner.query(`
      CREATE INDEX idx_grades_enrollment ON educational.grades(enrollment_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_grades_assessment_type ON educational.grades(assessment_type);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_grades_assessment_date ON educational.grades(assessment_date);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_grades_recorded_by ON educational.grades(recorded_by);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      new Table({
        schema: 'educational',
        name: 'grades'
      }),
      true
    );
  }
}
