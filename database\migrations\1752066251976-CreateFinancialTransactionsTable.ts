import {
    MigrationInterface,
    QueryRunner,
    Table,
    TableForeignKey
} from 'typeorm';

export class CreateFinancialTransactionsTable1752066251976 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: 'core',
        name: 'financial_transactions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'institution_id', type: 'uuid', isNullable: false },
          {
            name: 'type',
            type: 'varchar',
            length: '20',
            isNullable: false
          },
          {
            name: 'category',
            type: 'varchar',
            length: '50',
            isNullable: false
          },
          {
            name: 'amount',
            type: 'decimal',
            precision: 12,
            scale: 2,
            isNullable: false
          },
          { name: 'description', type: 'text', isNullable: false },
          { name: 'reference_id', type: 'uuid', isNullable: true },
          {
            name: 'reference_type',
            type: 'varchar',
            length: '50',
            isNullable: true
          },
          { name: 'payment_method_id', type: 'uuid', isNullable: true },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            isNullable: false,
            default: "'pending'"
          },
          { name: 'transaction_date', type: 'timestamp', isNullable: false },
          { name: 'due_date', type: 'timestamp', isNullable: true },
          { name: 'paid_date', type: 'timestamp', isNullable: true },
          { name: 'created_by', type: 'uuid', isNullable: false },
          { name: 'updated_by', type: 'uuid', isNullable: true },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true }
        ]
      }),
      true
    );

    // Foreign Keys
    await queryRunner.createForeignKey(
      'core.financial_transactions',
      new TableForeignKey({
        columnNames: ['institution_id'],
        referencedTableName: 'core.institutions',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'core.financial_transactions',
      new TableForeignKey({
        columnNames: ['payment_method_id'],
        referencedTableName: 'core.payment_methods',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'core.financial_transactions',
      new TableForeignKey({
        columnNames: ['created_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'core.financial_transactions',
      new TableForeignKey({
        columnNames: ['updated_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    // Indexes
    await queryRunner.query(`
      CREATE INDEX idx_financial_transactions_institution ON core.financial_transactions(institution_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_financial_transactions_type ON core.financial_transactions(type);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_financial_transactions_category ON core.financial_transactions(category);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_financial_transactions_status ON core.financial_transactions(status);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_financial_transactions_reference ON core.financial_transactions(reference_id, reference_type);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_financial_transactions_dates ON core.financial_transactions(transaction_date, due_date, paid_date);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_financial_transactions_payment_method ON core.financial_transactions(payment_method_id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      new Table({
        schema: 'core',
        name: 'financial_transactions'
      }),
      true
    );
  }
}
