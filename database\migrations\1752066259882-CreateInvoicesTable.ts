import {
    MigrationInterface,
    QueryRunner,
    Table,
    TableForeignKey
} from 'typeorm';

export class CreateInvoicesTable1752066259882 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: 'core',
        name: 'invoices',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          { name: 'enrollment_id', type: 'uuid', isNullable: false },
          { name: 'institution_id', type: 'uuid', isNullable: false },
          {
            name: 'invoice_number',
            type: 'varchar',
            length: '50',
            isNullable: false,
            isUnique: true
          },
          {
            name: 'amount',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: false
          },
          { name: 'due_date', type: 'timestamp', isNullable: false },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            isNullable: false,
            default: "'pending'"
          },
          { name: 'payment_date', type: 'timestamp', isNullable: true },
          { name: 'payment_method_id', type: 'uuid', isNullable: true },
          { name: 'notes', type: 'text', isNullable: true },
          { name: 'created_by', type: 'uuid', isNullable: false },
          { name: 'updated_by', type: 'uuid', isNullable: true },
          {
            name: 'created_at',
            type: 'timestamp',
            isNullable: false,
            default: 'now()'
          },
          { name: 'updated_at', type: 'timestamp', isNullable: true }
        ]
      }),
      true
    );

    // Foreign Keys
    await queryRunner.createForeignKey(
      'core.invoices',
      new TableForeignKey({
        columnNames: ['enrollment_id'],
        referencedTableName: 'educational.enrollments',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'core.invoices',
      new TableForeignKey({
        columnNames: ['institution_id'],
        referencedTableName: 'core.institutions',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'core.invoices',
      new TableForeignKey({
        columnNames: ['payment_method_id'],
        referencedTableName: 'core.payment_methods',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'core.invoices',
      new TableForeignKey({
        columnNames: ['created_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'core.invoices',
      new TableForeignKey({
        columnNames: ['updated_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    // Indexes
    await queryRunner.query(`
      CREATE INDEX idx_invoices_enrollment ON core.invoices(enrollment_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_invoices_institution ON core.invoices(institution_id);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_invoices_status ON core.invoices(status);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_invoices_due_date ON core.invoices(due_date);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_invoices_payment_date ON core.invoices(payment_date);
    `);

    await queryRunner.query(`
      CREATE INDEX idx_invoices_payment_method ON core.invoices(payment_method_id);
    `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX idx_invoices_number ON core.invoices(invoice_number);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      new Table({
        schema: 'core',
        name: 'invoices'
      }),
      true
    );
  }
}
