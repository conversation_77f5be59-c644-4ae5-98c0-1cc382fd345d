import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateDefaultRolesAndPermissions1752067147881
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // ========================================
    // INSERIR PERMISSÕES BÁSICAS
    // ========================================
    await queryRunner.query(`
      INSERT INTO core.permissions (id, name, key, description, created_by, created_at) VALUES
      -- Módulo Educacional - Cursos
      (uuid_generate_v4(), 'Ver Cursos', 'course.view', 'Visualizar cursos disponíveis', 'system', now()),
      (uuid_generate_v4(), 'Criar Cursos', 'course.create', 'Criar novos cursos', 'system', now()),
      (uuid_generate_v4(), 'Editar Cursos', 'course.update', 'Editar cursos existentes', 'system', now()),
      (uuid_generate_v4(), 'Deletar Cursos', 'course.delete', 'Deletar cursos', 'system', now()),

      -- Módulo Educacional - Turmas
      (uuid_generate_v4(), 'Ver Turmas', 'class.view', 'Visualizar turmas', 'system', now()),
      (uuid_generate_v4(), 'Criar Turmas', 'class.create', 'Criar novas turmas', 'system', now()),
      (uuid_generate_v4(), 'Editar Turmas', 'class.update', 'Editar turmas existentes', 'system', now()),
      (uuid_generate_v4(), 'Deletar Turmas', 'class.delete', 'Deletar turmas', 'system', now()),

      -- Módulo Educacional - Matrículas
      (uuid_generate_v4(), 'Ver Todas Matrículas', 'enrollment.view', 'Ver todas as matrículas da instituição', 'system', now()),
      (uuid_generate_v4(), 'Ver Matrículas Próprias', 'enrollment.view.own', 'Ver suas próprias matrículas', 'system', now()),
      (uuid_generate_v4(), 'Criar Matrículas', 'enrollment.create', 'Criar novas matrículas', 'system', now()),
      (uuid_generate_v4(), 'Aprovar Matrículas', 'enrollment.approve', 'Aprovar matrículas pendentes', 'system', now()),
      (uuid_generate_v4(), 'Rejeitar Matrículas', 'enrollment.reject', 'Rejeitar matrículas pendentes', 'system', now()),
      (uuid_generate_v4(), 'Cancelar Matrículas', 'enrollment.cancel', 'Cancelar matrículas ativas', 'system', now()),

      -- Módulo Educacional - Presença
      (uuid_generate_v4(), 'Ver Todas Presenças', 'attendance.view', 'Ver todas as presenças', 'system', now()),
      (uuid_generate_v4(), 'Ver Presença Própria', 'attendance.view.own', 'Ver sua própria presença', 'system', now()),
      (uuid_generate_v4(), 'Registrar Presença', 'attendance.create', 'Registrar presença dos alunos', 'system', now()),
      (uuid_generate_v4(), 'Editar Presença', 'attendance.update', 'Editar registros de presença', 'system', now()),
      (uuid_generate_v4(), 'Deletar Presença', 'attendance.delete', 'Deletar registros de presença', 'system', now()),

      -- Módulo Educacional - Notas
      (uuid_generate_v4(), 'Ver Todas Notas', 'grade.view', 'Ver todas as notas', 'system', now()),
      (uuid_generate_v4(), 'Ver Notas Próprias', 'grade.view.own', 'Ver suas próprias notas', 'system', now()),
      (uuid_generate_v4(), 'Lançar Notas', 'grade.create', 'Lançar notas dos alunos', 'system', now()),
      (uuid_generate_v4(), 'Editar Notas', 'grade.update', 'Editar notas existentes', 'system', now()),
      (uuid_generate_v4(), 'Deletar Notas', 'grade.delete', 'Deletar notas', 'system', now()),

      -- Módulo Financeiro - Geral
      (uuid_generate_v4(), 'Ver Financeiro', 'financial.view', 'Acessar módulo financeiro', 'system', now()),
      (uuid_generate_v4(), 'Gerenciar Financeiro', 'financial.manage', 'Gerenciar transações financeiras', 'system', now()),

      -- Módulo Financeiro - Transações
      (uuid_generate_v4(), 'Ver Transações', 'transaction.view', 'Ver transações financeiras', 'system', now()),
      (uuid_generate_v4(), 'Criar Transações', 'transaction.create', 'Criar transações financeiras', 'system', now()),
      (uuid_generate_v4(), 'Editar Transações', 'transaction.update', 'Editar transações financeiras', 'system', now()),
      (uuid_generate_v4(), 'Deletar Transações', 'transaction.delete', 'Deletar transações financeiras', 'system', now()),

      -- Módulo Financeiro - Faturas
      (uuid_generate_v4(), 'Ver Todas Faturas', 'invoice.view', 'Ver todas as faturas', 'system', now()),
      (uuid_generate_v4(), 'Ver Faturas Próprias', 'invoice.view.own', 'Ver suas próprias faturas', 'system', now()),
      (uuid_generate_v4(), 'Criar Faturas', 'invoice.create', 'Criar faturas', 'system', now()),
      (uuid_generate_v4(), 'Editar Faturas', 'invoice.update', 'Editar faturas', 'system', now()),
      (uuid_generate_v4(), 'Marcar Fatura Paga', 'invoice.pay', 'Marcar fatura como paga', 'system', now()),
      (uuid_generate_v4(), 'Cancelar Faturas', 'invoice.cancel', 'Cancelar faturas', 'system', now()),

      -- Módulo Financeiro - Métodos de Pagamento
      (uuid_generate_v4(), 'Ver Métodos Pagamento', 'payment_method.view', 'Ver métodos de pagamento', 'system', now()),
      (uuid_generate_v4(), 'Criar Métodos Pagamento', 'payment_method.create', 'Criar métodos de pagamento', 'system', now()),
      (uuid_generate_v4(), 'Editar Métodos Pagamento', 'payment_method.update', 'Editar métodos de pagamento', 'system', now()),
      (uuid_generate_v4(), 'Deletar Métodos Pagamento', 'payment_method.delete', 'Deletar métodos de pagamento', 'system', now()),

      -- Relatórios
      (uuid_generate_v4(), 'Ver Relatórios', 'report.view', 'Visualizar relatórios', 'system', now()),
      (uuid_generate_v4(), 'Gerar Relatórios', 'report.generate', 'Gerar relatórios personalizados', 'system', now()),

      -- Dashboard
      (uuid_generate_v4(), 'Ver Dashboard', 'dashboard.view', 'Acessar dashboard', 'system', now()),
      (uuid_generate_v4(), 'Ver Estatísticas', 'dashboard.stats', 'Ver estatísticas da instituição', 'system', now())
    `);

    // ========================================
    // CRIAR ROLES PADRÃO (GLOBAIS)
    // ========================================
    await queryRunner.query(`
      INSERT INTO core.roles (id, name, key, description, institution_id, created_by, created_at) VALUES
      ('11111111-1111-1111-1111-111111111111', 'Aluno', 'student', 'Estudante matriculado em cursos', NULL, 'system', now()),
      ('*************-2222-2222-************', 'Professor', 'teacher', 'Docente responsável por turmas', NULL, 'system', now()),
      ('*************-3333-3333-************', 'Responsável', 'guardian', 'Responsável legal por alunos menores', NULL, 'system', now()),
      ('*************-4444-4444-************', 'Administração', 'admin', 'Funcionário administrativo da instituição', NULL, 'system', now())
    `);

    // ========================================
    // ASSOCIAR PERMISSÕES ÀS ROLES
    // ========================================

    // ROLE: ALUNO (student)
    // Permissões: Visualizar suas próprias informações
    await queryRunner.query(`
      INSERT INTO core.role_permissions (id, role_id, permission_id, created_by, created_at)
      SELECT
        uuid_generate_v4(),
        '11111111-1111-1111-1111-111111111111',
        p.id,
        'system',
        now()
      FROM core.permissions p
      WHERE p.key IN (
        'course.view',
        'class.view',
        'enrollment.view.own',
        'attendance.view.own',
        'grade.view.own',
        'invoice.view.own',
        'dashboard.view'
      )
    `);

    // ROLE: PROFESSOR (teacher)
    // Permissões: Gerenciar turmas, presença e notas
    await queryRunner.query(`
      INSERT INTO core.role_permissions (id, role_id, permission_id, created_by, created_at)
      SELECT
        uuid_generate_v4(),
        '*************-2222-2222-************',
        p.id,
        'system',
        now()
      FROM core.permissions p
      WHERE p.key IN (
        'course.view',
        'class.view',
        'enrollment.view',
        'attendance.view',
        'attendance.create',
        'attendance.update',
        'grade.view',
        'grade.create',
        'grade.update',
        'dashboard.view',
        'report.view'
      )
    `);

    // ROLE: RESPONSÁVEL (guardian)
    // Permissões: Ver informações dos alunos sob sua responsabilidade
    await queryRunner.query(`
      INSERT INTO core.role_permissions (id, role_id, permission_id, created_by, created_at)
      SELECT
        uuid_generate_v4(),
        '*************-3333-3333-************',
        p.id,
        'system',
        now()
      FROM core.permissions p
      WHERE p.key IN (
        'course.view',
        'class.view',
        'enrollment.view.own',
        'attendance.view.own',
        'grade.view.own',
        'invoice.view.own',
        'dashboard.view'
      )
    `);

    // ROLE: ADMINISTRAÇÃO (admin)
    // Permissões: Controle total do sistema
    await queryRunner.query(`
      INSERT INTO core.role_permissions (id, role_id, permission_id, created_by, created_at)
      SELECT
        uuid_generate_v4(),
        '*************-4444-4444-************',
        p.id,
        'system',
        now()
      FROM core.permissions p
      WHERE p.created_by = 'system'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remover associações role_permissions
    await queryRunner.query(`
      DELETE FROM core.role_permissions
      WHERE role_id IN (
        '11111111-1111-1111-1111-111111111111',
        '*************-2222-2222-************',
        '*************-3333-3333-************',
        '*************-4444-4444-************'
      )
    `);

    // Remover roles padrão
    await queryRunner.query(`
      DELETE FROM core.roles
      WHERE id IN (
        '11111111-1111-1111-1111-111111111111',
        '*************-2222-2222-************',
        '*************-3333-3333-************',
        '*************-4444-4444-************'
      )
    `);

    // Remover permissões criadas
    await queryRunner.query(`
      DELETE FROM core.permissions
      WHERE created_by = 'system'
      AND key LIKE 'course.%'
      OR key LIKE 'class.%'
      OR key LIKE 'enrollment.%'
      OR key LIKE 'attendance.%'
      OR key LIKE 'grade.%'
      OR key LIKE 'financial.%'
      OR key LIKE 'transaction.%'
      OR key LIKE 'invoice.%'
      OR key LIKE 'payment_method.%'
      OR key LIKE 'report.%'
      OR key LIKE 'dashboard.%'
    `);
  }
}
