import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateDefaultRolesAndPermissions1752067147881
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // ========================================
    // INSERIR PERMISSÕES BÁSICAS
    // ========================================
    await queryRunner.manager
      .createQueryBuilder()
      .insert()
      .into('core.permissions')
      .values([
        // Módulo Educacional - Cursos
        {
          name: 'Ver Cursos',
          key: 'course.view',
          description: 'Visualizar cursos disponíveis',
          created_by: 'system'
        },
        {
          name: 'Criar Cursos',
          key: 'course.create',
          description: 'Criar novos cursos',
          created_by: 'system'
        },
        {
          name: '<PERSON>ar Curs<PERSON>',
          key: 'course.update',
          description: 'Editar cursos existentes',
          created_by: 'system'
        },
        {
          name: 'Deletar Cursos',
          key: 'course.delete',
          description: 'Deletar cursos',
          created_by: 'system'
        },

        // Módulo Educacional - Turmas
        {
          name: 'Ver Turmas',
          key: 'class.view',
          description: 'Visualizar turmas',
          created_by: 'system'
        },
        {
          name: 'Criar Turmas',
          key: 'class.create',
          description: 'Criar novas turmas',
          created_by: 'system'
        },
        {
          name: 'Editar Turmas',
          key: 'class.update',
          description: 'Editar turmas existentes',
          created_by: 'system'
        },
        {
          name: 'Deletar Turmas',
          key: 'class.delete',
          description: 'Deletar turmas',
          created_by: 'system'
        },

        // Módulo Educacional - Matrículas
        {
          name: 'Ver Todas Matrículas',
          key: 'enrollment.view',
          description: 'Ver todas as matrículas da instituição',
          created_by: 'system'
        },
        {
          name: 'Ver Matrículas Próprias',
          key: 'enrollment.view.own',
          description: 'Ver suas próprias matrículas',
          created_by: 'system'
        },
        {
          name: 'Criar Matrículas',
          key: 'enrollment.create',
          description: 'Criar novas matrículas',
          created_by: 'system'
        },
        {
          name: 'Aprovar Matrículas',
          key: 'enrollment.approve',
          description: 'Aprovar matrículas pendentes',
          created_by: 'system'
        },
        {
          name: 'Rejeitar Matrículas',
          key: 'enrollment.reject',
          description: 'Rejeitar matrículas pendentes',
          created_by: 'system'
        },
        {
          name: 'Cancelar Matrículas',
          key: 'enrollment.cancel',
          description: 'Cancelar matrículas ativas',
          created_by: 'system'
        },

        // Módulo Educacional - Presença
        {
          name: 'Ver Todas Presenças',
          key: 'attendance.view',
          description: 'Ver todas as presenças',
          created_by: 'system'
        },
        {
          name: 'Ver Presença Própria',
          key: 'attendance.view.own',
          description: 'Ver sua própria presença',
          created_by: 'system'
        },
        {
          name: 'Registrar Presença',
          key: 'attendance.create',
          description: 'Registrar presença dos alunos',
          created_by: 'system'
        },
        {
          name: 'Editar Presença',
          key: 'attendance.update',
          description: 'Editar registros de presença',
          created_by: 'system'
        },
        {
          name: 'Deletar Presença',
          key: 'attendance.delete',
          description: 'Deletar registros de presença',
          created_by: 'system'
        }
      ])
      .execute();

    // Continuar inserindo as demais permissões
    await queryRunner.manager
      .createQueryBuilder()
      .insert()
      .into('core.permissions')
      .values([
        // Módulo Educacional - Notas
        {
          name: 'Ver Todas Notas',
          key: 'grade.view',
          description: 'Ver todas as notas',
          created_by: 'system'
        },
        {
          name: 'Ver Notas Próprias',
          key: 'grade.view.own',
          description: 'Ver suas próprias notas',
          created_by: 'system'
        },
        {
          name: 'Lançar Notas',
          key: 'grade.create',
          description: 'Lançar notas dos alunos',
          created_by: 'system'
        },
        {
          name: 'Editar Notas',
          key: 'grade.update',
          description: 'Editar notas existentes',
          created_by: 'system'
        },
        {
          name: 'Deletar Notas',
          key: 'grade.delete',
          description: 'Deletar notas',
          created_by: 'system'
        },

        // Módulo Financeiro - Geral
        {
          name: 'Ver Financeiro',
          key: 'financial.view',
          description: 'Acessar módulo financeiro',
          created_by: 'system'
        },
        {
          name: 'Gerenciar Financeiro',
          key: 'financial.manage',
          description: 'Gerenciar transações financeiras',
          created_by: 'system'
        },

        // Módulo Financeiro - Transações
        {
          name: 'Ver Transações',
          key: 'transaction.view',
          description: 'Ver transações financeiras',
          created_by: 'system'
        },
        {
          name: 'Criar Transações',
          key: 'transaction.create',
          description: 'Criar transações financeiras',
          created_by: 'system'
        },
        {
          name: 'Editar Transações',
          key: 'transaction.update',
          description: 'Editar transações financeiras',
          created_by: 'system'
        },
        {
          name: 'Deletar Transações',
          key: 'transaction.delete',
          description: 'Deletar transações financeiras',
          created_by: 'system'
        },

        // Módulo Financeiro - Faturas
        {
          name: 'Ver Todas Faturas',
          key: 'invoice.view',
          description: 'Ver todas as faturas',
          created_by: 'system'
        },
        {
          name: 'Ver Faturas Próprias',
          key: 'invoice.view.own',
          description: 'Ver suas próprias faturas',
          created_by: 'system'
        },
        {
          name: 'Criar Faturas',
          key: 'invoice.create',
          description: 'Criar faturas',
          created_by: 'system'
        },
        {
          name: 'Editar Faturas',
          key: 'invoice.update',
          description: 'Editar faturas',
          created_by: 'system'
        },
        {
          name: 'Marcar Fatura Paga',
          key: 'invoice.pay',
          description: 'Marcar fatura como paga',
          created_by: 'system'
        },
        {
          name: 'Cancelar Faturas',
          key: 'invoice.cancel',
          description: 'Cancelar faturas',
          created_by: 'system'
        },

        // Módulo Financeiro - Métodos de Pagamento
        {
          name: 'Ver Métodos Pagamento',
          key: 'payment_method.view',
          description: 'Ver métodos de pagamento',
          created_by: 'system'
        },
        {
          name: 'Criar Métodos Pagamento',
          key: 'payment_method.create',
          description: 'Criar métodos de pagamento',
          created_by: 'system'
        },
        {
          name: 'Editar Métodos Pagamento',
          key: 'payment_method.update',
          description: 'Editar métodos de pagamento',
          created_by: 'system'
        },
        {
          name: 'Deletar Métodos Pagamento',
          key: 'payment_method.delete',
          description: 'Deletar métodos de pagamento',
          created_by: 'system'
        },

        // Relatórios
        {
          name: 'Ver Relatórios',
          key: 'report.view',
          description: 'Visualizar relatórios',
          created_by: 'system'
        },
        {
          name: 'Gerar Relatórios',
          key: 'report.generate',
          description: 'Gerar relatórios personalizados',
          created_by: 'system'
        },

        // Dashboard
        {
          name: 'Ver Dashboard',
          key: 'dashboard.view',
          description: 'Acessar dashboard',
          created_by: 'system'
        },
        {
          name: 'Ver Estatísticas',
          key: 'dashboard.stats',
          description: 'Ver estatísticas da instituição',
          created_by: 'system'
        }
      ])
      .execute();

    // ========================================
    // CRIAR ROLES PADRÃO (GLOBAIS)
    // ========================================
    await queryRunner.manager
      .createQueryBuilder()
      .insert()
      .into('core.roles')
      .values([
        {
          id: '11111111-1111-1111-1111-111111111111',
          name: 'Aluno',
          key: 'student',
          description: 'Estudante matriculado em cursos',
          institution_id: null,
          created_by: 'system'
        },
        {
          id: '*************-2222-2222-************',
          name: 'Professor',
          key: 'teacher',
          description: 'Docente responsável por turmas',
          institution_id: null,
          created_by: 'system'
        },
        {
          id: '*************-3333-3333-************',
          name: 'Responsável',
          key: 'guardian',
          description: 'Responsável legal por alunos menores',
          institution_id: null,
          created_by: 'system'
        },
        {
          id: '*************-4444-4444-************',
          name: 'Administração',
          key: 'admin',
          description: 'Funcionário administrativo da instituição',
          institution_id: null,
          created_by: 'system'
        }
      ])
      .execute();

    // ========================================
    // ASSOCIAR PERMISSÕES ÀS ROLES
    // ========================================

    // ROLE: ALUNO (student)
    // Permissões: Visualizar suas próprias informações
    await queryRunner.query(`
      INSERT INTO core.role_permissions (id, role_id, permission_id, created_by, created_at)
      SELECT
        uuid_generate_v4(),
        '11111111-1111-1111-1111-111111111111',
        p.id,
        'system',
        now()
      FROM core.permissions p
      WHERE p.key IN (
        'course.view',
        'class.view',
        'enrollment.view.own',
        'attendance.view.own',
        'grade.view.own',
        'invoice.view.own',
        'dashboard.view'
      )
    `);

    // ROLE: PROFESSOR (teacher)
    // Permissões: Gerenciar turmas, presença e notas
    await queryRunner.query(`
      INSERT INTO core.role_permissions (id, role_id, permission_id, created_by, created_at)
      SELECT
        uuid_generate_v4(),
        '*************-2222-2222-************',
        p.id,
        'system',
        now()
      FROM core.permissions p
      WHERE p.key IN (
        'course.view',
        'class.view',
        'enrollment.view',
        'attendance.view',
        'attendance.create',
        'attendance.update',
        'grade.view',
        'grade.create',
        'grade.update',
        'dashboard.view',
        'report.view'
      )
    `);

    // ROLE: RESPONSÁVEL (guardian)
    // Permissões: Ver informações dos alunos sob sua responsabilidade
    await queryRunner.query(`
      INSERT INTO core.role_permissions (id, role_id, permission_id, created_by, created_at)
      SELECT
        uuid_generate_v4(),
        '*************-3333-3333-************',
        p.id,
        'system',
        now()
      FROM core.permissions p
      WHERE p.key IN (
        'course.view',
        'class.view',
        'enrollment.view.own',
        'attendance.view.own',
        'grade.view.own',
        'invoice.view.own',
        'dashboard.view'
      )
    `);

    // ROLE: ADMINISTRAÇÃO (admin)
    // Permissões: Controle total do sistema
    await queryRunner.query(`
      INSERT INTO core.role_permissions (id, role_id, permission_id, created_by, created_at)
      SELECT
        uuid_generate_v4(),
        '*************-4444-4444-************',
        p.id,
        'system',
        now()
      FROM core.permissions p
      WHERE p.created_by = 'system'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remover associações role_permissions
    await queryRunner.manager
      .createQueryBuilder()
      .delete()
      .from('core.role_permissions')
      .where('role_id IN (:...roleIds)', {
        roleIds: [
          '11111111-1111-1111-1111-111111111111',
          '*************-2222-2222-************',
          '*************-3333-3333-************',
          '*************-4444-4444-************'
        ]
      })
      .execute();

    // Remover roles padrão
    await queryRunner.manager
      .createQueryBuilder()
      .delete()
      .from('core.roles')
      .where('id IN (:...roleIds)', {
        roleIds: [
          '11111111-1111-1111-1111-111111111111',
          '*************-2222-2222-************',
          '*************-3333-3333-************',
          '*************-4444-4444-************'
        ]
      })
      .execute();

    // Remover permissões criadas
    await queryRunner.manager
      .createQueryBuilder()
      .delete()
      .from('core.permissions')
      .where('created_by = :createdBy', { createdBy: 'system' })
      .andWhere(
        '(key LIKE :course OR key LIKE :class OR key LIKE :enrollment OR key LIKE :attendance OR key LIKE :grade OR key LIKE :financial OR key LIKE :transaction OR key LIKE :invoice OR key LIKE :paymentMethod OR key LIKE :report OR key LIKE :dashboard)',
        {
          course: 'course.%',
          class: 'class.%',
          enrollment: 'enrollment.%',
          attendance: 'attendance.%',
          grade: 'grade.%',
          financial: 'financial.%',
          transaction: 'transaction.%',
          invoice: 'invoice.%',
          paymentMethod: 'payment_method.%',
          report: 'report.%',
          dashboard: 'dashboard.%'
        }
      )
      .execute();
  }
}
