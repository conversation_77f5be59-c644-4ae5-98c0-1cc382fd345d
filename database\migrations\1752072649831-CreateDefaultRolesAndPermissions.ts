import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateDefaultRolesAndPermissions1752072649831
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Criar um usuário temporário para ser o created_by
    const tempUserId = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';

    // ========================================
    // INSERIR PERMISSÕES BÁSICAS
    // ========================================
    await queryRunner.query(`
      INSERT INTO core.permissions (id, name, key, description, created_by, created_at) VALUES
      -- Módulo Educacional - Cursos
      (uuid_generate_v4(), 'Ver Cursos', 'course.view', 'Visualizar cursos disponíveis', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Cursos', 'course.create', 'Criar novos cursos', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Cursos', 'course.update', 'Editar cursos existentes', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Cursos', 'course.delete', 'Deletar cursos', '${tempUserId}', now()),

      -- Módulo Educacional - Turmas
      (uuid_generate_v4(), 'Ver Turmas', 'class.view', 'Visualizar turmas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Turmas', 'class.create', 'Criar novas turmas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Turmas', 'class.update', 'Editar turmas existentes', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Turmas', 'class.delete', 'Deletar turmas', '${tempUserId}', now()),

      -- Módulo Educacional - Matrículas
      (uuid_generate_v4(), 'Ver Todas Matrículas', 'enrollment.view', 'Ver todas as matrículas da instituição', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Ver Matrículas Próprias', 'enrollment.view.own', 'Ver suas próprias matrículas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Matrículas', 'enrollment.create', 'Criar novas matrículas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Aprovar Matrículas', 'enrollment.approve', 'Aprovar matrículas pendentes', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Rejeitar Matrículas', 'enrollment.reject', 'Rejeitar matrículas pendentes', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Cancelar Matrículas', 'enrollment.cancel', 'Cancelar matrículas ativas', '${tempUserId}', now()),

      -- Módulo Educacional - Presença
      (uuid_generate_v4(), 'Ver Todas Presenças', 'attendance.view', 'Ver todas as presenças', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Ver Presença Própria', 'attendance.view.own', 'Ver sua própria presença', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Registrar Presença', 'attendance.create', 'Registrar presença dos alunos', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Presença', 'attendance.update', 'Editar registros de presença', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Presença', 'attendance.delete', 'Deletar registros de presença', '${tempUserId}', now()),

      -- Módulo Educacional - Notas
      (uuid_generate_v4(), 'Ver Todas Notas', 'grade.view', 'Ver todas as notas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Ver Notas Próprias', 'grade.view.own', 'Ver suas próprias notas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Lançar Notas', 'grade.create', 'Lançar notas dos alunos', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Notas', 'grade.update', 'Editar notas existentes', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Notas', 'grade.delete', 'Deletar notas', '${tempUserId}', now()),

      -- Módulo Financeiro - Geral
      (uuid_generate_v4(), 'Ver Financeiro', 'financial.view', 'Acessar módulo financeiro', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Gerenciar Financeiro', 'financial.manage', 'Gerenciar transações financeiras', '${tempUserId}', now()),

      -- Módulo Financeiro - Transações
      (uuid_generate_v4(), 'Ver Transações', 'transaction.view', 'Ver transações financeiras', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Transações', 'transaction.create', 'Criar transações financeiras', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Transações', 'transaction.update', 'Editar transações financeiras', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Transações', 'transaction.delete', 'Deletar transações financeiras', '${tempUserId}', now()),

      -- Módulo Financeiro - Faturas
      (uuid_generate_v4(), 'Ver Todas Faturas', 'invoice.view', 'Ver todas as faturas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Ver Faturas Próprias', 'invoice.view.own', 'Ver suas próprias faturas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Faturas', 'invoice.create', 'Criar faturas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Faturas', 'invoice.update', 'Editar faturas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Marcar Fatura Paga', 'invoice.pay', 'Marcar fatura como paga', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Cancelar Faturas', 'invoice.cancel', 'Cancelar faturas', '${tempUserId}', now()),

      -- Módulo Financeiro - Métodos de Pagamento
      (uuid_generate_v4(), 'Ver Métodos Pagamento', 'payment_method.view', 'Ver métodos de pagamento', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Métodos Pagamento', 'payment_method.create', 'Criar métodos de pagamento', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Métodos Pagamento', 'payment_method.update', 'Editar métodos de pagamento', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Métodos Pagamento', 'payment_method.delete', 'Deletar métodos de pagamento', '${tempUserId}', now()),

      -- Relatórios
      (uuid_generate_v4(), 'Ver Relatórios', 'report.view', 'Visualizar relatórios', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Gerar Relatórios', 'report.generate', 'Gerar relatórios personalizados', '${tempUserId}', now()),

      -- Dashboard
      (uuid_generate_v4(), 'Ver Dashboard', 'dashboard.view', 'Acessar dashboard', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Ver Estatísticas', 'dashboard.stats', 'Ver estatísticas da instituição', '${tempUserId}', now())
    `);

    // ========================================
    // CRIAR ROLES PADRÃO (GLOBAIS)
    // ========================================
    await queryRunner.query(`
      INSERT INTO core.roles (id, name, key, description, institution_id, created_by, created_at) VALUES
      ('11111111-1111-1111-1111-111111111111', 'Aluno', 'student', 'Estudante matriculado em cursos', NULL, '${tempUserId}', now()),
      ('*************-2222-2222-************', 'Professor', 'teacher', 'Docente responsável por turmas', NULL, '${tempUserId}', now()),
      ('*************-3333-3333-************', 'Responsável', 'guardian', 'Responsável legal por alunos menores', NULL, '${tempUserId}', now()),
      ('*************-4444-4444-************', 'Administração', 'admin', 'Funcionário administrativo da instituição', NULL, '${tempUserId}', now())
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remover associações role_permissions
    await queryRunner.query(`
      DELETE FROM core.role_permissions
      WHERE role_id IN (
        '11111111-1111-1111-1111-111111111111',
        '*************-2222-2222-************',
        '*************-3333-3333-************',
        '*************-4444-4444-************'
      )
    `);

    // Remover roles padrão
    await queryRunner.query(`
      DELETE FROM core.roles
      WHERE id IN (
        '11111111-1111-1111-1111-111111111111',
        '*************-2222-2222-************',
        '*************-3333-3333-************',
        '*************-4444-4444-************'
      )
    `);

    // Remover permissões criadas
    await queryRunner.query(`
      DELETE FROM core.permissions
      WHERE created_by = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'
    `);
  }
}
