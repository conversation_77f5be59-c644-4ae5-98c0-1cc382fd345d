import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMissingPermissions1752073754598 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const tempUserId = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';

    // Adicionar permissões que estão faltando
    await queryRunner.query(`
      INSERT INTO core.permissions (id, name, key, description, created_by, created_at) VALUES
      -- Per<PERSON>s<PERSON><PERSON> de Usuário
      (uuid_generate_v4(), 'Ver Usuários', 'user.view', 'Visualizar usuários do sistema', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Usuários', 'user.create', 'Criar novos usuários', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Usuários', 'user.update', 'Editar usuários existentes', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Usuários', 'user.delete', 'Deletar usuários', '${tempUserId}', now()),

      -- Permissões de Role
      (uuid_generate_v4(), 'Ver Roles', 'role.view', 'Visualizar roles do sistema', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Roles', 'role.create', 'Criar novas roles', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Roles', 'role.update', 'Editar roles existentes', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Roles', 'role.delete', 'Deletar roles', '${tempUserId}', now()),

      -- Permissões de Permission
      (uuid_generate_v4(), 'Ver Permissões', 'permission.view', 'Visualizar permissões do sistema', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Permissões', 'permission.create', 'Criar novas permissões', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Permissões', 'permission.update', 'Editar permissões existentes', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Permissões', 'permission.delete', 'Deletar permissões', '${tempUserId}', now()),

      -- Permissões de Endereço
      (uuid_generate_v4(), 'Ver Endereços', 'address.view', 'Visualizar endereços', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Endereços', 'address.create', 'Criar novos endereços', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Endereços', 'address.update', 'Editar endereços existentes', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Endereços', 'address.delete', 'Deletar endereços', '${tempUserId}', now()),

      -- Permissões de Instituição
      (uuid_generate_v4(), 'Ver Instituições', 'institution.view', 'Visualizar instituições', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Instituições', 'institution.create', 'Criar novas instituições', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Instituições', 'institution.update', 'Editar instituições existentes', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Instituições', 'institution.delete', 'Deletar instituições', '${tempUserId}', now()),

      -- Permissões de Plano
      (uuid_generate_v4(), 'Ver Planos', 'plan.view', 'Visualizar planos disponíveis', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Planos', 'plan.create', 'Criar novos planos', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Planos', 'plan.update', 'Editar planos existentes', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Planos', 'plan.delete', 'Deletar planos', '${tempUserId}', now()),

      -- Permissões de Assinatura
      (uuid_generate_v4(), 'Ver Todas Assinaturas', 'subscription.view', 'Ver todas as assinaturas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Ver Assinaturas Próprias', 'subscription.view.own', 'Ver suas próprias assinaturas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Assinaturas', 'subscription.create', 'Criar novas assinaturas', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Assinaturas', 'subscription.update', 'Editar assinaturas existentes', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Cancelar Assinaturas', 'subscription.cancel', 'Cancelar assinaturas', '${tempUserId}', now()),

      -- Permissões de Role-Permission
      (uuid_generate_v4(), 'Ver Role-Permissões', 'role_permission.view', 'Ver associações role-permissão', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar Role-Permissões', 'role_permission.create', 'Criar associações role-permissão', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar Role-Permissões', 'role_permission.update', 'Editar associações role-permissão', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar Role-Permissões', 'role_permission.delete', 'Deletar associações role-permissão', '${tempUserId}', now()),

      -- Permissões de User-Role
      (uuid_generate_v4(), 'Ver User-Roles', 'user_role.view', 'Ver associações usuário-role', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Criar User-Roles', 'user_role.create', 'Criar associações usuário-role', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Editar User-Roles', 'user_role.update', 'Editar associações usuário-role', '${tempUserId}', now()),
      (uuid_generate_v4(), 'Deletar User-Roles', 'user_role.delete', 'Deletar associações usuário-role', '${tempUserId}', now())
    `);

    // Associar as novas permissões administrativas ao role ADMIN
    await queryRunner.query(`
      INSERT INTO core.role_permissions (id, role_id, permission_id, created_by, created_at)
      SELECT uuid_generate_v4(), '*************-4444-4444-************', p.id, '${tempUserId}', now()
      FROM core.permissions p
      WHERE p.key IN (
        'user.view', 'user.create', 'user.update', 'user.delete',
        'role.view', 'role.create', 'role.update', 'role.delete',
        'permission.view', 'permission.create', 'permission.update', 'permission.delete',
        'address.view', 'address.create', 'address.update', 'address.delete',
        'institution.view', 'institution.create', 'institution.update', 'institution.delete',
        'plan.view', 'plan.create', 'plan.update', 'plan.delete',
        'subscription.view', 'subscription.view.own', 'subscription.create', 'subscription.update', 'subscription.cancel',
        'role_permission.view', 'role_permission.create', 'role_permission.update', 'role_permission.delete',
        'user_role.view', 'user_role.create', 'user_role.update', 'user_role.delete'
      )
      AND p.created_by = '${tempUserId}'
    `);

    // Associar algumas permissões básicas aos outros roles
    await queryRunner.query(`
      INSERT INTO core.role_permissions (id, role_id, permission_id, created_by, created_at)
      SELECT uuid_generate_v4(), '11111111-1111-1111-1111-111111111111', p.id, '${tempUserId}', now()
      FROM core.permissions p
      WHERE p.key IN ('address.view', 'address.create', 'plan.view', 'subscription.view.own')
      AND p.created_by = '${tempUserId}'
    `);

    await queryRunner.query(`
      INSERT INTO core.role_permissions (id, role_id, permission_id, created_by, created_at)
      SELECT uuid_generate_v4(), '*************-2222-2222-************', p.id, '${tempUserId}', now()
      FROM core.permissions p
      WHERE p.key IN ('address.view', 'address.create', 'plan.view')
      AND p.created_by = '${tempUserId}'
    `);

    await queryRunner.query(`
      INSERT INTO core.role_permissions (id, role_id, permission_id, created_by, created_at)
      SELECT uuid_generate_v4(), '*************-3333-3333-************', p.id, '${tempUserId}', now()
      FROM core.permissions p
      WHERE p.key IN ('address.view', 'address.create', 'plan.view', 'subscription.view.own')
      AND p.created_by = '${tempUserId}'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const tempUserId = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';

    // Remover associações das novas permissões
    await queryRunner.query(`
      DELETE FROM core.role_permissions
      WHERE permission_id IN (
        SELECT id FROM core.permissions
        WHERE created_by = '${tempUserId}'
        AND key LIKE 'user.%'
        OR key LIKE 'role.%'
        OR key LIKE 'permission.%'
        OR key LIKE 'address.%'
        OR key LIKE 'institution.%'
        OR key LIKE 'plan.%'
        OR key LIKE 'subscription.%'
        OR key LIKE 'role_permission.%'
        OR key LIKE 'user_role.%'
      )
    `);

    // Remover as novas permissões
    await queryRunner.query(`
      DELETE FROM core.permissions
      WHERE created_by = '${tempUserId}'
      AND (
        key LIKE 'user.%'
        OR key LIKE 'role.%'
        OR key LIKE 'permission.%'
        OR key LIKE 'address.%'
        OR key LIKE 'institution.%'
        OR key LIKE 'plan.%'
        OR key LIKE 'subscription.%'
        OR key LIKE 'role_permission.%'
        OR key LIKE 'user_role.%'
      )
    `);
  }
}
