import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableForeignKey
} from 'typeorm';

export class AddInstitutionIdToClasses1752080000001 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add institution_id column to classes table
    await queryRunner.addColumn(
      'educational.classes',
      new TableColumn({
        name: 'institution_id',
        type: 'uuid',
        isNullable: false
      })
    );

    // Add foreign key constraint
    await queryRunner.createForeignKey(
      'educational.classes',
      new TableForeignKey({
        columnNames: ['institution_id'],
        referencedTableName: 'core.institutions',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    // Add index for better performance
    await queryRunner.query(`
      CREATE INDEX idx_classes_institution ON educational.classes(institution_id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key
    const table = await queryRunner.getTable('educational.classes');
    const foreignKey = table!.foreignKeys.find(
      fk => fk.columnNames.indexOf('institution_id') !== -1
    );
    if (foreignKey) {
      await queryRunner.dropForeignKey('educational.classes', foreignKey);
    }

    // Drop index
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_classes_institution;
    `);

    // Drop column
    await queryRunner.dropColumn('educational.classes', 'institution_id');
  }
}
