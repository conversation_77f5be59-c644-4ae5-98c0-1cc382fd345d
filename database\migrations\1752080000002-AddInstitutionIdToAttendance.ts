import {
  MigrationInterface,
  QueryRunner,
  <PERSON>C<PERSON>umn,
  TableForeignKey
} from 'typeorm';

export class AddInstitutionIdToAttendance1752080000002 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add institution_id column to attendance table
    await queryRunner.addColumn(
      'educational.attendance',
      new TableColumn({
        name: 'institution_id',
        type: 'uuid',
        isNullable: false
      })
    );

    // Add foreign key constraint
    await queryRunner.createForeignKey(
      'educational.attendance',
      new TableForeignKey({
        columnNames: ['institution_id'],
        referencedTableName: 'core.institutions',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    // Add index for better performance
    await queryRunner.query(`
      CREATE INDEX idx_attendance_institution ON educational.attendance(institution_id);
    `);

    // Update unique constraint to include institution_id
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_attendance_unique_enrollment_date;
    `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX idx_attendance_unique_enrollment_date_institution
      ON educational.attendance(enrollment_id, class_date, institution_id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop new unique constraint
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_attendance_unique_enrollment_date_institution;
    `);

    // Restore original unique constraint
    await queryRunner.query(`
      CREATE UNIQUE INDEX idx_attendance_unique_enrollment_date
      ON educational.attendance(enrollment_id, class_date);
    `);

    // Drop foreign key
    const table = await queryRunner.getTable('educational.attendance');
    const foreignKey = table!.foreignKeys.find(
      fk => fk.columnNames.indexOf('institution_id') !== -1
    );
    if (foreignKey) {
      await queryRunner.dropForeignKey('educational.attendance', foreignKey);
    }

    // Drop index
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_attendance_institution;
    `);

    // Drop column
    await queryRunner.dropColumn('educational.attendance', 'institution_id');
  }
}
