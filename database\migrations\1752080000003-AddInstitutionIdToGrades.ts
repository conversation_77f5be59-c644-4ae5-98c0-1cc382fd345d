import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableForeignKey
} from 'typeorm';

export class AddInstitutionIdToGrades1752080000003 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add institution_id column to grades table
    await queryRunner.addColumn(
      'educational.grades',
      new TableColumn({
        name: 'institution_id',
        type: 'uuid',
        isNullable: false
      })
    );

    // Add foreign key constraint
    await queryRunner.createForeignKey(
      'educational.grades',
      new TableForeignKey({
        columnNames: ['institution_id'],
        referencedTableName: 'core.institutions',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    // Add index for better performance
    await queryRunner.query(`
      CREATE INDEX idx_grades_institution ON educational.grades(institution_id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key
    const table = await queryRunner.getTable('educational.grades');
    const foreignKey = table!.foreignKeys.find(
      fk => fk.columnNames.indexOf('institution_id') !== -1
    );
    if (foreignKey) {
      await queryRunner.dropForeignKey('educational.grades', foreignKey);
    }

    // Drop index
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_grades_institution;
    `);

    // Drop column
    await queryRunner.dropColumn('educational.grades', 'institution_id');
  }
}
