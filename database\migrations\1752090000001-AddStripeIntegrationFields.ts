import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddStripeIntegrationFields1752090000001
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Adicionar campos específicos do Stripe à tabela payment_methods existente
    await queryRunner.addColumns('core.payment_methods', [
      new TableColumn({
        name: 'stripe_payment_method_id',
        type: 'varchar',
        length: '255',
        isNullable: true
      }),
      new TableColumn({
        name: 'stripe_customer_id',
        type: 'varchar',
        length: '255',
        isNullable: true
      }),
      new TableColumn({
        name: 'card_brand',
        type: 'varchar',
        length: '50',
        isNullable: true
      }),
      new TableColumn({
        name: 'card_last4',
        type: 'varchar',
        length: '4',
        isNullable: true
      }),
      new TableColumn({
        name: 'card_exp_month',
        type: 'integer',
        isNullable: true
      }),
      new TableColumn({
        name: 'card_exp_year',
        type: 'integer',
        isNullable: true
      }),
      new TableColumn({
        name: 'is_default',
        type: 'boolean',
        isNullable: false,
        default: false
      })
    ]);

    // Adicionar campos específicos do Stripe à tabela financial_transactions existente
    await queryRunner.addColumns('core.financial_transactions', [
      new TableColumn({
        name: 'stripe_payment_intent_id',
        type: 'varchar',
        length: '255',
        isNullable: true
      }),
      new TableColumn({
        name: 'stripe_session_id',
        type: 'varchar',
        length: '255',
        isNullable: true
      }),
      new TableColumn({
        name: 'payment_type',
        type: 'varchar',
        length: '20',
        isNullable: false,
        default: "'one_time'"
      }),
      new TableColumn({
        name: 'currency',
        type: 'varchar',
        length: '3',
        isNullable: false,
        default: "'BRL'"
      }),
      new TableColumn({
        name: 'metadata',
        type: 'jsonb',
        isNullable: true
      })
    ]);

    // Adicionar campos específicos do Stripe à tabela invoices existente
    await queryRunner.addColumns('core.invoices', [
      new TableColumn({
        name: 'stripe_invoice_id',
        type: 'varchar',
        length: '255',
        isNullable: true
      }),
      new TableColumn({
        name: 'stripe_payment_intent_id',
        type: 'varchar',
        length: '255',
        isNullable: true
      }),
      new TableColumn({
        name: 'currency',
        type: 'varchar',
        length: '3',
        isNullable: false,
        default: "'BRL'"
      }),
      new TableColumn({
        name: 'metadata',
        type: 'jsonb',
        isNullable: true
      })
    ]);

    // Criar índices para os novos campos do Stripe
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_payment_methods_stripe_payment_method
      ON core.payment_methods(stripe_payment_method_id);
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_payment_methods_stripe_customer
      ON core.payment_methods(stripe_customer_id);
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_payment_methods_default
      ON core.payment_methods(is_default);
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_financial_transactions_stripe_payment_intent
      ON core.financial_transactions(stripe_payment_intent_id);
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_financial_transactions_stripe_session
      ON core.financial_transactions(stripe_session_id);
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_financial_transactions_payment_type
      ON core.financial_transactions(payment_type);
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_financial_transactions_currency
      ON core.financial_transactions(currency);
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_invoices_stripe_invoice
      ON core.invoices(stripe_invoice_id);
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_invoices_stripe_payment_intent
      ON core.invoices(stripe_payment_intent_id);
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_invoices_currency
      ON core.invoices(currency);
    `);

    // Atualizar constraints de status para incluir novos valores do Stripe
    await queryRunner.query(`
      ALTER TABLE core.financial_transactions
      DROP CONSTRAINT IF EXISTS chk_financial_transactions_status;
    `);

    await queryRunner.query(`
      ALTER TABLE core.financial_transactions
      ADD CONSTRAINT chk_financial_transactions_status
      CHECK (status IN ('pending', 'processing', 'succeeded', 'failed', 'canceled', 'refunded'));
    `);

    await queryRunner.query(`
      ALTER TABLE core.invoices
      DROP CONSTRAINT IF EXISTS chk_invoices_status;
    `);

    await queryRunner.query(`
      ALTER TABLE core.invoices
      ADD CONSTRAINT chk_invoices_status
      CHECK (status IN ('pending', 'processing', 'succeeded', 'failed', 'canceled', 'refunded', 'draft', 'open', 'paid', 'void', 'uncollectible'));
    `);

    // Atualizar constraint de payment_type para financial_transactions
    await queryRunner.query(`
      ALTER TABLE core.financial_transactions
      ADD CONSTRAINT chk_financial_transactions_payment_type
      CHECK (payment_type IN ('one_time', 'subscription', 'refund'));
    `);

    // Atualizar constraint de type para payment_methods para incluir novos tipos
    await queryRunner.query(`
      ALTER TABLE core.payment_methods
      DROP CONSTRAINT IF EXISTS chk_payment_methods_type;
    `);

    await queryRunner.query(`
      ALTER TABLE core.payment_methods
      ADD CONSTRAINT chk_payment_methods_type
      CHECK (type IN ('card', 'boleto', 'pix', 'bank_transfer', 'cash', 'check'));
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remover constraints
    await queryRunner.query(`
      ALTER TABLE core.financial_transactions
      DROP CONSTRAINT IF EXISTS chk_financial_transactions_status;
    `);

    await queryRunner.query(`
      ALTER TABLE core.financial_transactions
      DROP CONSTRAINT IF EXISTS chk_financial_transactions_payment_type;
    `);

    await queryRunner.query(`
      ALTER TABLE core.invoices
      DROP CONSTRAINT IF EXISTS chk_invoices_status;
    `);

    await queryRunner.query(`
      ALTER TABLE core.payment_methods
      DROP CONSTRAINT IF EXISTS chk_payment_methods_type;
    `);

    // Remover índices
    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_payment_methods_stripe_payment_method;`
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_payment_methods_stripe_customer;`
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_payment_methods_default;`
    );

    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_financial_transactions_stripe_payment_intent;`
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_financial_transactions_stripe_session;`
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_financial_transactions_payment_type;`
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_financial_transactions_currency;`
    );

    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_invoices_stripe_invoice;`
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_invoices_stripe_payment_intent;`
    );
    await queryRunner.query(`DROP INDEX IF EXISTS idx_invoices_currency;`);

    // Remover colunas
    await queryRunner.dropColumns('core.payment_methods', [
      'stripe_payment_method_id',
      'stripe_customer_id',
      'card_brand',
      'card_last4',
      'card_exp_month',
      'card_exp_year',
      'is_default'
    ]);

    await queryRunner.dropColumns('core.financial_transactions', [
      'stripe_payment_intent_id',
      'stripe_session_id',
      'payment_type',
      'currency',
      'metadata'
    ]);

    await queryRunner.dropColumns('core.invoices', [
      'stripe_invoice_id',
      'stripe_payment_intent_id',
      'currency',
      'metadata'
    ]);
  }
}
