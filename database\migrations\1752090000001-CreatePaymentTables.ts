import {
  ForeignKey,
  Index,
  MigrationInterface,
  QueryRunner,
  Table
} from 'typeorm';

export class CreatePaymentTables1752090000001 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create financial schema
    await queryRunner.query(`CREATE SCHEMA IF NOT EXISTS financial;`);

    // Create payments table
    await queryRunner.createTable(
      new Table({
        schema: 'financial',
        name: 'payments',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          {
            name: 'institution_id',
            type: 'uuid',
            isNullable: false
          },
          {
            name: 'stripe_payment_intent_id',
            type: 'varchar',
            length: '255',
            isNullable: true
          },
          {
            name: 'stripe_session_id',
            type: 'varchar',
            length: '255',
            isNullable: true
          },
          {
            name: 'amount',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: false
          },
          {
            name: 'currency',
            type: 'varchar',
            length: '3',
            isNullable: false,
            default: "'BRL'"
          },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            isNullable: false,
            default: "'pending'"
          },
          {
            name: 'payment_type',
            type: 'varchar',
            length: '20',
            isNullable: false,
            default: "'one_time'"
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: false
          },
          {
            name: 'updated_by',
            type: 'uuid',
            isNullable: true
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
            isNullable: false
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true
          }
        ]
      }),
      true
    );

    // Create subscriptions table
    await queryRunner.createTable(
      new Table({
        schema: 'financial',
        name: 'subscriptions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          {
            name: 'institution_id',
            type: 'uuid',
            isNullable: false
          },
          {
            name: 'plan_id',
            type: 'uuid',
            isNullable: false
          },
          {
            name: 'stripe_subscription_id',
            type: 'varchar',
            length: '255',
            isNullable: false
          },
          {
            name: 'stripe_customer_id',
            type: 'varchar',
            length: '255',
            isNullable: false
          },
          {
            name: 'status',
            type: 'varchar',
            length: '30',
            isNullable: false,
            default: "'active'"
          },
          {
            name: 'current_period_start',
            type: 'timestamp',
            isNullable: false
          },
          {
            name: 'current_period_end',
            type: 'timestamp',
            isNullable: false
          },
          {
            name: 'trial_start',
            type: 'timestamp',
            isNullable: true
          },
          {
            name: 'trial_end',
            type: 'timestamp',
            isNullable: true
          },
          {
            name: 'canceled_at',
            type: 'timestamp',
            isNullable: true
          },
          {
            name: 'ended_at',
            type: 'timestamp',
            isNullable: true
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: false
          },
          {
            name: 'updated_by',
            type: 'uuid',
            isNullable: true
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
            isNullable: false
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true
          }
        ]
      }),
      true
    );

    // Create payment_methods table
    await queryRunner.createTable(
      new Table({
        schema: 'financial',
        name: 'payment_methods',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()'
          },
          {
            name: 'institution_id',
            type: 'uuid',
            isNullable: false
          },
          {
            name: 'stripe_payment_method_id',
            type: 'varchar',
            length: '255',
            isNullable: false
          },
          {
            name: 'stripe_customer_id',
            type: 'varchar',
            length: '255',
            isNullable: false
          },
          {
            name: 'type',
            type: 'varchar',
            length: '20',
            isNullable: false,
            default: "'card'"
          },
          {
            name: 'card_brand',
            type: 'varchar',
            length: '50',
            isNullable: true
          },
          {
            name: 'card_last4',
            type: 'varchar',
            length: '4',
            isNullable: true
          },
          {
            name: 'card_exp_month',
            type: 'integer',
            isNullable: true
          },
          {
            name: 'card_exp_year',
            type: 'integer',
            isNullable: true
          },
          {
            name: 'is_default',
            type: 'boolean',
            isNullable: false,
            default: false
          },
          {
            name: 'created_by',
            type: 'uuid',
            isNullable: false
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
            isNullable: false
          }
        ]
      }),
      true
    );

    // Create indexes
    await queryRunner.createIndex(
      'financial.payments',
      new Index('idx_payments_institution', ['institution_id'])
    );
    await queryRunner.createIndex(
      'financial.payments',
      new Index('idx_payments_stripe_payment_intent', [
        'stripe_payment_intent_id'
      ])
    );
    await queryRunner.createIndex(
      'financial.payments',
      new Index('idx_payments_stripe_session', ['stripe_session_id'])
    );
    await queryRunner.createIndex(
      'financial.payments',
      new Index('idx_payments_status', ['status'])
    );

    await queryRunner.createIndex(
      'financial.subscriptions',
      new Index('idx_subscriptions_institution', ['institution_id'])
    );
    await queryRunner.createIndex(
      'financial.subscriptions',
      new Index('idx_subscriptions_stripe_subscription', [
        'stripe_subscription_id'
      ])
    );
    await queryRunner.createIndex(
      'financial.subscriptions',
      new Index('idx_subscriptions_stripe_customer', ['stripe_customer_id'])
    );
    await queryRunner.createIndex(
      'financial.subscriptions',
      new Index('idx_subscriptions_status', ['status'])
    );

    await queryRunner.createIndex(
      'financial.payment_methods',
      new Index('idx_payment_methods_institution', ['institution_id'])
    );
    await queryRunner.createIndex(
      'financial.payment_methods',
      new Index('idx_payment_methods_stripe_payment_method', [
        'stripe_payment_method_id'
      ])
    );
    await queryRunner.createIndex(
      'financial.payment_methods',
      new Index('idx_payment_methods_stripe_customer', ['stripe_customer_id'])
    );

    // Create foreign keys
    await queryRunner.createForeignKey(
      'financial.payments',
      new ForeignKey({
        columnNames: ['institution_id'],
        referencedTableName: 'core.institutions',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'financial.payments',
      new ForeignKey({
        columnNames: ['created_by'],
        referencedTableName: 'core.users',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'financial.subscriptions',
      new ForeignKey({
        columnNames: ['institution_id'],
        referencedTableName: 'core.institutions',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'financial.subscriptions',
      new ForeignKey({
        columnNames: ['plan_id'],
        referencedTableName: 'core.plans',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );

    await queryRunner.createForeignKey(
      'financial.payment_methods',
      new ForeignKey({
        columnNames: ['institution_id'],
        referencedTableName: 'core.institutions',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT'
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('financial.payment_methods');
    await queryRunner.dropTable('financial.subscriptions');
    await queryRunner.dropTable('financial.payments');
    await queryRunner.query(`DROP SCHEMA IF EXISTS financial;`);
  }
}
