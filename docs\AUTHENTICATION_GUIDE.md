# 🔐 **<PERSON><PERSON><PERSON> de Autenticação - EduSys API**

## 📋 **V<PERSON><PERSON> Geral**

O sistema de autenticação do EduSys foi completamente implementado com as seguintes funcionalidades:

- ✅ **Login/Logout** completo
- ✅ **Refresh Token** automático
- ✅ **Definição de senha** para primeiro acesso
- ✅ **Preenchimento automático** de `created_by` e `updated_by`
- ✅ **Guards JWT** para proteção de rotas
- ✅ **Interceptor de auditoria** para rastreamento

---

## 🚀 **Endpoints de Autenticação**

### **1. Login**
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Resposta:**
```json
{
  "user": {
    "id": "uuid",
    "name": "Nome do Usuário",
    "email": "<EMAIL>",
    "institution_id": "uuid",
    "is_email_verified": true,
    "is_phone_verified": false,
    "actived": true
  },
  "accessToken": "jwt-access-token",
  "refreshToken": "jwt-refresh-token"
}
```

### **2. Logout**
```http
POST /auth/logout
Authorization: Bearer {accessToken}
```

**Resposta:**
```json
{
  "message": "Logout realizado com sucesso"
}
```

### **3. Refresh Token**
```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "jwt-refresh-token"
}
```

### **4. Definir Senha (Primeiro Acesso)**
```http
POST /auth/set-password
Content-Type: application/json

{
  "token": "first-access-token",
  "password": "newPassword123",
  "confirmPassword": "newPassword123"
}
```

---

## 🔒 **Proteção de Rotas**

### **Usando o Guard JWT**
```typescript
@Controller('user')
export class UserController {
  @Put(':id')
  @UseGuards(JwtAuthGuard)  // 🔐 Protege a rota
  @UseInterceptors(AuditInterceptor)  // 📝 Auditoria automática
  async updateUser(@Param('id') id: string, @Body() body: UpdateUserDto) {
    // Rota protegida - só usuários autenticados podem acessar
  }
}
```

### **Obtendo Usuário Atual**
```typescript
import { CurrentUser, CurrentUserData } from '../common/decorators/current-user.decorator';

@Controller('example')
export class ExampleController {
  @Get('profile')
  @UseGuards(JwtAuthGuard)
  async getProfile(@CurrentUser() user: CurrentUserData) {
    // user.sub = ID do usuário
    // user.email = Email do usuário
    // user.name = Nome do usuário
    // user.institution_id = ID da instituição
  }
}
```

---

## 📝 **Auditoria Automática**

### **Como Funciona**
O `AuditInterceptor` automaticamente preenche os campos de auditoria:

- **POST** (Criação): Define `created_by` e `created_at`
- **PUT/PATCH** (Atualização): Define `updated_by` e `updated_at`

### **Exemplo de Uso**
```typescript
@Controller('example')
export class ExampleController {
  @Post()
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(AuditInterceptor)
  async create(@Body() data: CreateDto) {
    // data.created_by será automaticamente preenchido com o ID do usuário
    // data.created_at será automaticamente preenchido com a data atual
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(AuditInterceptor)
  async update(@Param('id') id: string, @Body() data: UpdateDto) {
    // data.updated_by será automaticamente preenchido com o ID do usuário
    // data.updated_at será automaticamente preenchido com a data atual
  }
}
```

---

## 🍪 **Cookies HTTP-Only**

O sistema utiliza cookies HTTP-Only para maior segurança:

- **accessToken**: Expira em 15 minutos
- **refreshToken**: Expira em 7 dias
- **Secure**: Apenas HTTPS em produção
- **SameSite**: Proteção CSRF

---

## 🔧 **Configuração de Ambiente**

Adicione as seguintes variáveis ao seu `.env`:

```env
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRATION_TIME=900  # 15 minutos em segundos
JWT_REFRESH_SECRET=your-refresh-secret-key
JWT_REFRESH_EXPIRATION_TIME=604800  # 7 dias em segundos

# First Access
FIRST_LOGIN_URL=http://localhost:3000/set-password
```

---

## 🧪 **Testando a Autenticação**

### **1. Criar um Usuário**
```http
POST /user
Content-Type: application/json

{
  "name": "Teste User",
  "email": "<EMAIL>",
  "phone_number": "+5511999999999",
  "tax_id": "12345678901",
  "birth_date": "1990-01-01",
  "institution_id": "uuid-da-instituicao"
}
```

### **2. Verificar Email de Primeiro Acesso**
O usuário receberá um email com link para definir a senha.

### **3. Definir Senha**
Use o token do email para definir a senha.

### **4. Fazer Login**
Use email e senha para fazer login.

### **5. Acessar Rotas Protegidas**
Use o accessToken para acessar rotas protegidas.

---

## ✅ **Funcionalidades Implementadas**

- [x] **Sistema de Login/Logout completo**
- [x] **Refresh Token automático**
- [x] **Estratégias JWT configuradas**
- [x] **Guards de autenticação**
- [x] **Interceptor de auditoria**
- [x] **Decorator para usuário atual**
- [x] **Cookies HTTP-Only seguros**
- [x] **Validação de usuário ativo**
- [x] **Preenchimento automático de campos de auditoria**
- [x] **Documentação Swagger completa**

---

## 🎯 **Próximos Passos Sugeridos**

1. **Implementar 2FA** (já preparado no sistema)
2. **Adicionar rate limiting** para login
3. **Implementar blacklist de tokens**
4. **Adicionar logs de auditoria**
5. **Implementar recuperação de senha**

---

**🎉 O sistema de autenticação está completamente funcional e pronto para uso!**
