# 🗄️ **Migrations do Módulo Educacional - EduSys**

## 📋 **Visão Geral**

For<PERSON> **8 migrations completas** para os módulos educacional e financeiro seguindo exatamente os padrões estabelecidos no projeto:

### **📚 Módulo Educacional:**
1. **CreateCoursesTable** - Tabela de cursos
2. **CreateClassesTable** - Tabela de turmas
3. **CreateEnrollmentsTable** - Tabela de matrículas
4. **CreateAttendanceTable** - Tabela de presença
5. **CreateGradesTable** - Tabela de notas

### **💰 Módulo Financeiro:**
6. **CreatePaymentMethodsTable** - Métodos de pagamento
7. **CreateFinancialTransactionsTable** - Transações financeiras
8. **CreateInvoicesTable** - Faturas

---

## 🏗️ **Estrutura das Migrations**

### **1. CreateCoursesTable (1752065318281)**

#### **Schema:** `educational.courses`

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | UUID | Chave primária |
| `institution_id` | UUID | FK para institutions |
| `name` | VARCHAR(255) | Nome do curso |
| `description` | TEXT | Descrição detalhada |
| `duration_months` | INTEGER | Duração em meses |
| `price` | DECIMAL(10,2) | Preço do curso |
| `max_students` | INTEGER | Máximo de alunos |
| `status` | VARCHAR(20) | active/inactive/suspended |
| `start_date` | TIMESTAMP | Data de início |
| `end_date` | TIMESTAMP | Data de término |
| `created_by` | UUID | FK para users |
| `updated_by` | UUID | FK para users |
| `created_at` | TIMESTAMP | Data de criação |
| `updated_at` | TIMESTAMP | Data de atualização |

#### **Foreign Keys:**
- `institution_id` → `core.institutions(id)` ON DELETE CASCADE
- `created_by` → `core.users(id)` ON DELETE RESTRICT
- `updated_by` → `core.users(id)` ON DELETE RESTRICT

#### **Indexes:**
- `idx_courses_institution` - Busca por instituição
- `idx_courses_status` - Filtro por status
- `idx_courses_dates` - Busca por período

---

### **2. CreateClassesTable (1752065326222)**

#### **Schema:** `educational.classes`

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | UUID | Chave primária |
| `course_id` | UUID | FK para courses |
| `name` | VARCHAR(255) | Nome da turma |
| `description` | TEXT | Descrição da turma |
| `max_students` | INTEGER | Máximo de alunos |
| `current_students` | INTEGER | Alunos atuais (default: 0) |
| `status` | VARCHAR(20) | open/closed/in_progress/finished |
| `start_date` | TIMESTAMP | Data de início |
| `end_date` | TIMESTAMP | Data de término |
| `schedule` | JSONB | Horários e dias da semana |
| `created_by` | UUID | FK para users |
| `updated_by` | UUID | FK para users |
| `created_at` | TIMESTAMP | Data de criação |
| `updated_at` | TIMESTAMP | Data de atualização |

#### **Foreign Keys:**
- `course_id` → `educational.courses(id)` ON DELETE CASCADE
- `created_by` → `core.users(id)` ON DELETE RESTRICT
- `updated_by` → `core.users(id)` ON DELETE RESTRICT

#### **Indexes:**
- `idx_classes_course` - Busca por curso
- `idx_classes_status` - Filtro por status
- `idx_classes_dates` - Busca por período
- `idx_classes_students` - Controle de vagas

---

### **3. CreateEnrollmentsTable (1752065333854)**

#### **Schema:** `educational.enrollments`

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | UUID | Chave primária |
| `student_id` | UUID | FK para users (aluno) |
| `class_id` | UUID | FK para classes |
| `institution_id` | UUID | FK para institutions |
| `status` | VARCHAR(20) | pending/approved/rejected/active/suspended/completed |
| `enrollment_date` | TIMESTAMP | Data da inscrição |
| `approval_date` | TIMESTAMP | Data da aprovação |
| `approved_by` | UUID | FK para users (quem aprovou) |
| `rejection_reason` | TEXT | Motivo da rejeição |
| `notes` | TEXT | Observações |
| `created_by` | UUID | FK para users |
| `updated_by` | UUID | FK para users |
| `created_at` | TIMESTAMP | Data de criação |
| `updated_at` | TIMESTAMP | Data de atualização |

#### **Foreign Keys:**
- `student_id` → `core.users(id)` ON DELETE CASCADE
- `class_id` → `educational.classes(id)` ON DELETE CASCADE
- `institution_id` → `core.institutions(id)` ON DELETE CASCADE
- `approved_by` → `core.users(id)` ON DELETE RESTRICT
- `created_by` → `core.users(id)` ON DELETE RESTRICT
- `updated_by` → `core.users(id)` ON DELETE RESTRICT

#### **Indexes:**
- `idx_enrollments_student` - Busca por aluno
- `idx_enrollments_class` - Busca por turma
- `idx_enrollments_institution` - Busca por instituição
- `idx_enrollments_status` - Filtro por status
- `idx_enrollments_dates` - Busca por período
- `idx_enrollments_unique_student_class` - Evita matrículas duplicadas

#### **Constraint Único:**
```sql
CREATE UNIQUE INDEX idx_enrollments_unique_student_class
ON educational.enrollments(student_id, class_id)
WHERE status NOT IN ('rejected', 'cancelled');
```

---

### **4. CreateAttendanceTable (1752066229843)**

#### **Schema:** `educational.attendance`

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | UUID | Chave primária |
| `enrollment_id` | UUID | FK para enrollments |
| `class_date` | DATE | Data da aula |
| `status` | VARCHAR(20) | present/absent/late/justified |
| `notes` | TEXT | Observações |
| `recorded_by` | UUID | FK para users (quem registrou) |
| `created_by` | UUID | FK para users |
| `updated_by` | UUID | FK para users |
| `created_at` | TIMESTAMP | Data de criação |
| `updated_at` | TIMESTAMP | Data de atualização |

#### **Foreign Keys:**
- `enrollment_id` → `educational.enrollments(id)` ON DELETE CASCADE
- `recorded_by` → `core.users(id)` ON DELETE RESTRICT
- `created_by` → `core.users(id)` ON DELETE RESTRICT
- `updated_by` → `core.users(id)` ON DELETE RESTRICT

#### **Indexes:**
- `idx_attendance_enrollment` - Busca por matrícula
- `idx_attendance_date` - Busca por data
- `idx_attendance_status` - Filtro por status
- `idx_attendance_recorded_by` - Busca por quem registrou
- `idx_attendance_unique_enrollment_date` - Evita registros duplicados

---

### **5. CreateGradesTable (1752066237007)**

#### **Schema:** `educational.grades`

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | UUID | Chave primária |
| `enrollment_id` | UUID | FK para enrollments |
| `assessment_type` | VARCHAR(50) | exam/assignment/project/participation |
| `assessment_name` | VARCHAR(255) | Nome da avaliação |
| `grade` | DECIMAL(5,2) | Nota obtida |
| `max_grade` | DECIMAL(5,2) | Nota máxima |
| `weight` | DECIMAL(3,2) | Peso da avaliação (default: 1.0) |
| `assessment_date` | DATE | Data da avaliação |
| `recorded_by` | UUID | FK para users (quem lançou) |
| `created_by` | UUID | FK para users |
| `updated_by` | UUID | FK para users |
| `created_at` | TIMESTAMP | Data de criação |
| `updated_at` | TIMESTAMP | Data de atualização |

#### **Foreign Keys:**
- `enrollment_id` → `educational.enrollments(id)` ON DELETE CASCADE
- `recorded_by` → `core.users(id)` ON DELETE RESTRICT
- `created_by` → `core.users(id)` ON DELETE RESTRICT
- `updated_by` → `core.users(id)` ON DELETE RESTRICT

#### **Indexes:**
- `idx_grades_enrollment` - Busca por matrícula
- `idx_grades_assessment_type` - Filtro por tipo de avaliação
- `idx_grades_assessment_date` - Busca por data
- `idx_grades_recorded_by` - Busca por quem lançou

---

### **6. CreatePaymentMethodsTable (*************)**

#### **Schema:** `core.payment_methods`

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | UUID | Chave primária |
| `institution_id` | UUID | FK para institutions |
| `name` | VARCHAR(255) | Nome do método |
| `type` | VARCHAR(50) | cash/credit_card/debit_card/bank_transfer/pix/check |
| `is_active` | BOOLEAN | Se está ativo (default: true) |
| `created_by` | UUID | FK para users |
| `updated_by` | UUID | FK para users |
| `created_at` | TIMESTAMP | Data de criação |
| `updated_at` | TIMESTAMP | Data de atualização |

#### **Foreign Keys:**
- `institution_id` → `core.institutions(id)` ON DELETE CASCADE
- `created_by` → `core.users(id)` ON DELETE RESTRICT
- `updated_by` → `core.users(id)` ON DELETE RESTRICT

#### **Indexes:**
- `idx_payment_methods_institution` - Busca por instituição
- `idx_payment_methods_type` - Filtro por tipo
- `idx_payment_methods_active` - Filtro por ativo

---

### **7. CreateFinancialTransactionsTable (*************)**

#### **Schema:** `core.financial_transactions`

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | UUID | Chave primária |
| `institution_id` | UUID | FK para institutions |
| `type` | VARCHAR(20) | income/expense |
| `category` | VARCHAR(50) | enrollment_fee/monthly_fee/material/salary/infrastructure/other |
| `amount` | DECIMAL(12,2) | Valor da transação |
| `description` | TEXT | Descrição |
| `reference_id` | UUID | ID de referência (enrollment, invoice, etc) |
| `reference_type` | VARCHAR(50) | Tipo da referência |
| `payment_method_id` | UUID | FK para payment_methods |
| `status` | VARCHAR(20) | pending/completed/cancelled |
| `transaction_date` | TIMESTAMP | Data da transação |
| `due_date` | TIMESTAMP | Data de vencimento |
| `paid_date` | TIMESTAMP | Data do pagamento |
| `created_by` | UUID | FK para users |
| `updated_by` | UUID | FK para users |
| `created_at` | TIMESTAMP | Data de criação |
| `updated_at` | TIMESTAMP | Data de atualização |

#### **Foreign Keys:**
- `institution_id` → `core.institutions(id)` ON DELETE CASCADE
- `payment_method_id` → `core.payment_methods(id)` ON DELETE RESTRICT
- `created_by` → `core.users(id)` ON DELETE RESTRICT
- `updated_by` → `core.users(id)` ON DELETE RESTRICT

#### **Indexes:**
- `idx_financial_transactions_institution` - Busca por instituição
- `idx_financial_transactions_type` - Filtro por tipo
- `idx_financial_transactions_category` - Filtro por categoria
- `idx_financial_transactions_status` - Filtro por status
- `idx_financial_transactions_reference` - Busca por referência
- `idx_financial_transactions_dates` - Busca por datas
- `idx_financial_transactions_payment_method` - Busca por método de pagamento

---

### **8. CreateInvoicesTable (1752066259882)**

#### **Schema:** `core.invoices`

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | UUID | Chave primária |
| `enrollment_id` | UUID | FK para enrollments |
| `institution_id` | UUID | FK para institutions |
| `invoice_number` | VARCHAR(50) | Número da fatura (único) |
| `amount` | DECIMAL(10,2) | Valor da fatura |
| `due_date` | TIMESTAMP | Data de vencimento |
| `status` | VARCHAR(20) | pending/paid/overdue/cancelled |
| `payment_date` | TIMESTAMP | Data do pagamento |
| `payment_method_id` | UUID | FK para payment_methods |
| `notes` | TEXT | Observações |
| `created_by` | UUID | FK para users |
| `updated_by` | UUID | FK para users |
| `created_at` | TIMESTAMP | Data de criação |
| `updated_at` | TIMESTAMP | Data de atualização |

#### **Foreign Keys:**
- `enrollment_id` → `educational.enrollments(id)` ON DELETE CASCADE
- `institution_id` → `core.institutions(id)` ON DELETE CASCADE
- `payment_method_id` → `core.payment_methods(id)` ON DELETE RESTRICT
- `created_by` → `core.users(id)` ON DELETE RESTRICT
- `updated_by` → `core.users(id)` ON DELETE RESTRICT

#### **Indexes:**
- `idx_invoices_enrollment` - Busca por matrícula
- `idx_invoices_institution` - Busca por instituição
- `idx_invoices_status` - Filtro por status
- `idx_invoices_due_date` - Busca por vencimento
- `idx_invoices_payment_date` - Busca por pagamento
- `idx_invoices_payment_method` - Busca por método de pagamento
- `idx_invoices_number` - Índice único no número da fatura

---

## 🔄 **Relacionamentos Completos**

```
Institution (1) ──→ (N) Course
Course (1) ──→ (N) Class
Class (1) ──→ (N) Enrollment
Enrollment (1) ──→ (N) Attendance
Enrollment (1) ──→ (N) Grade
Enrollment (1) ──→ (N) Invoice

Institution (1) ──→ (N) PaymentMethod
Institution (1) ──→ (N) FinancialTransaction
Institution (1) ──→ (N) Invoice

PaymentMethod (1) ──→ (N) FinancialTransaction
PaymentMethod (1) ──→ (N) Invoice
Invoice (1) ──→ (N) FinancialTransaction

User (1) ──→ (N) Enrollment (como student)
User (1) ──→ (N) Enrollment (como approved_by)
User (1) ──→ (N) Attendance (como recorded_by)
User (1) ──→ (N) Grade (como recorded_by)
```

---

## 🎯 **Status Definidos**

### **Course Status:**
- `active` - Curso ativo e disponível
- `inactive` - Curso inativo
- `suspended` - Curso suspenso temporariamente

### **Class Status:**
- `open` - Turma aberta para matrículas
- `closed` - Turma fechada para matrículas
- `in_progress` - Turma em andamento
- `finished` - Turma finalizada

### **Enrollment Status:**
- `pending` - Aguardando aprovação
- `approved` - Aprovada pela secretaria
- `rejected` - Rejeitada pela secretaria
- `active` - Matrícula ativa (após pagamento)
- `suspended` - Matrícula suspensa
- `completed` - Curso concluído

### **Attendance Status:**
- `present` - Presente
- `absent` - Ausente
- `late` - Atrasado
- `justified` - Falta justificada

### **Assessment Types (Grades):**
- `exam` - Prova
- `assignment` - Trabalho
- `project` - Projeto
- `participation` - Participação

### **Payment Method Types:**
- `cash` - Dinheiro
- `credit_card` - Cartão de crédito
- `debit_card` - Cartão de débito
- `bank_transfer` - Transferência bancária
- `pix` - PIX
- `check` - Cheque

### **Financial Transaction Types:**
- `income` - Receita
- `expense` - Despesa

### **Financial Transaction Categories:**
- `enrollment_fee` - Taxa de matrícula
- `monthly_fee` - Mensalidade
- `material` - Material didático
- `salary` - Salário
- `infrastructure` - Infraestrutura
- `other` - Outros

### **Financial Transaction Status:**
- `pending` - Pendente
- `completed` - Concluída
- `cancelled` - Cancelada

### **Invoice Status:**
- `pending` - Pendente
- `paid` - Paga
- `overdue` - Vencida
- `cancelled` - Cancelada

---

## 🚀 **Como Executar as Migrations**

### **1. Executar todas as migrations:**
```bash
yarn run:migration
```

### **2. Reverter última migration:**
```bash
yarn revert:migration
```

### **3. Verificar status:**
```bash
yarn typeorm migration:show
```

---

## 📊 **Exemplo de Dados**

### **Course:**
```json
{
  "id": "uuid-curso",
  "institution_id": "uuid-instituicao",
  "name": "Desenvolvimento Web Full Stack",
  "description": "Curso completo de desenvolvimento web",
  "duration_months": 12,
  "price": 2500.00,
  "max_students": 30,
  "status": "active",
  "start_date": "2024-08-01T00:00:00Z",
  "end_date": "2025-07-31T23:59:59Z"
}
```

### **Class:**
```json
{
  "id": "uuid-turma",
  "course_id": "uuid-curso",
  "name": "Turma 2024.2 - Noturno",
  "max_students": 25,
  "current_students": 0,
  "status": "open",
  "schedule": {
    "days": ["monday", "wednesday", "friday"],
    "start_time": "19:00",
    "end_time": "22:00"
  }
}
```

### **Enrollment:**
```json
{
  "id": "uuid-matricula",
  "student_id": "uuid-aluno",
  "class_id": "uuid-turma",
  "institution_id": "uuid-instituicao",
  "status": "pending",
  "enrollment_date": "2024-07-09T10:30:00Z"
}
```

---

## ✅ **Validações Implementadas**

1. **Integridade Referencial** - Todas as FKs configuradas
2. **Prevenção de Duplicatas** - Constraint único em enrollments
3. **Indexes Otimizados** - Para consultas frequentes
4. **Cascade Apropriado** - DELETE CASCADE onde necessário
5. **Auditoria Completa** - Campos created_by/updated_by

---

**🎉 Migrations do módulo educacional criadas com sucesso seguindo todos os padrões do projeto!**
