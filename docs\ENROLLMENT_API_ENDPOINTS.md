# 🌐 **API Endpoints - Sistema de Matrículas EduSys**

## 📚 **Módulo Educacional**

### **🎓 Courses (Cursos)**
```
GET    /course                    - Listar cursos da instituição
GET    /course/:id                - Buscar curso por ID
POST   /course                    - Criar novo curso
PUT    /course/:id                - Atualizar curso
DELETE /course/:id                - Deletar curso
GET    /course/active             - Listar cursos ativos
GET    /course/:id/classes        - Listar turmas do curso
```

### **🏫 Classes (Turmas)**
```
GET    /class                     - Listar turmas da instituição
GET    /class/:id                 - Buscar turma por ID
POST   /class                     - Criar nova turma
PUT    /class/:id                 - Atualizar turma
DELETE /class/:id                 - Deletar turma
GET    /class/open                - Listar turmas abertas para matrícula
GET    /class/:id/enrollments     - Listar matrículas da turma
GET    /class/:id/students        - Listar alunos da turma
```

### **📝 Enrollments (Matrículas)**
```
GET    /enrollment                - Listar matrículas da instituição
GET    /enrollment/:id            - Buscar matrícula por ID
POST   /enrollment/public         - Inscrição pública (sem auth)
POST   /enrollment                - Criar matrícula interna
PUT    /enrollment/:id            - Atualizar matrícula
DELETE /enrollment/:id            - Cancelar matrícula
PUT    /enrollment/:id/approve    - Aprovar matrícula
PUT    /enrollment/:id/reject     - Rejeitar matrícula
GET    /enrollment/pending        - Listar matrículas pendentes
GET    /enrollment/student/:id    - Matrículas do estudante
```

### **📊 Attendance (Presença)**
```
GET    /attendance                - Listar presenças
GET    /attendance/enrollment/:id - Presenças de uma matrícula
POST   /attendance                - Registrar presença
PUT    /attendance/:id            - Atualizar presença
DELETE /attendance/:id            - Remover registro de presença
GET    /attendance/class/:id/date/:date - Presença da turma em data específica
POST   /attendance/bulk           - Registrar presença em lote
```

### **📝 Grades (Notas)**
```
GET    /grade                     - Listar notas
GET    /grade/enrollment/:id      - Notas de uma matrícula
POST   /grade                     - Lançar nota
PUT    /grade/:id                 - Atualizar nota
DELETE /grade/:id                 - Remover nota
GET    /grade/class/:id           - Notas da turma
POST   /grade/bulk                - Lançar notas em lote
GET    /grade/report/:enrollmentId - Boletim do aluno
```

---

## 💰 **Módulo Financeiro**

### **💳 Payment Methods (Métodos de Pagamento)**
```
GET    /payment-method            - Listar métodos de pagamento
GET    /payment-method/:id        - Buscar método por ID
POST   /payment-method            - Criar método de pagamento
PUT    /payment-method/:id        - Atualizar método
DELETE /payment-method/:id        - Deletar método
GET    /payment-method/active     - Listar métodos ativos
```

### **💰 Financial Transactions (Transações Financeiras)**
```
GET    /financial-transaction     - Listar transações
GET    /financial-transaction/:id - Buscar transação por ID
POST   /financial-transaction     - Criar transação
PUT    /financial-transaction/:id - Atualizar transação
DELETE /financial-transaction/:id - Cancelar transação
GET    /financial-transaction/income - Listar receitas
GET    /financial-transaction/expense - Listar despesas
GET    /financial-transaction/report - Relatório financeiro
GET    /financial-transaction/period/:start/:end - Transações por período
```

### **🧾 Invoices (Faturas)**
```
GET    /invoice                   - Listar faturas
GET    /invoice/:id               - Buscar fatura por ID
POST   /invoice                   - Criar fatura
PUT    /invoice/:id               - Atualizar fatura
DELETE /invoice/:id               - Cancelar fatura
PUT    /invoice/:id/pay           - Marcar como paga
GET    /invoice/pending           - Faturas pendentes
GET    /invoice/overdue           - Faturas vencidas
GET    /invoice/enrollment/:id    - Faturas de uma matrícula
GET    /invoice/:id/pdf           - Gerar PDF da fatura
```

---

## 🔄 **Endpoints de Integração**

### **📋 Dashboard**
```
GET    /dashboard/stats           - Estatísticas gerais
GET    /dashboard/enrollments     - Resumo de matrículas
GET    /dashboard/financial       - Resumo financeiro
GET    /dashboard/courses         - Resumo de cursos
```

### **📊 Reports (Relatórios)**
```
GET    /report/enrollment         - Relatório de matrículas
GET    /report/financial          - Relatório financeiro
GET    /report/attendance         - Relatório de presença
GET    /report/grades             - Relatório de notas
GET    /report/students           - Relatório de alunos
POST   /report/custom             - Relatório customizado
```

### **🔔 Notifications**
```
GET    /notification              - Listar notificações
POST   /notification/enrollment   - Notificar nova matrícula
POST   /notification/payment      - Notificar pagamento
POST   /notification/overdue      - Notificar vencimento
```

---

## 🌐 **Endpoints Públicos (Sem Autenticação)**

### **📝 Public Enrollment**
```
GET    /public/courses            - Listar cursos disponíveis
GET    /public/course/:id         - Detalhes do curso
GET    /public/course/:id/classes - Turmas disponíveis
POST   /public/enrollment         - Inscrição pública
GET    /public/enrollment/:token  - Consultar status da inscrição
```

### **ℹ️ Public Information**
```
GET    /public/institution/:id    - Informações da instituição
GET    /public/payment-methods    - Métodos de pagamento aceitos
```

---

## 🔐 **Permissões por Endpoint**

### **👨‍💼 Admin/Secretaria**
- ✅ Todos os endpoints
- ✅ Aprovar/rejeitar matrículas
- ✅ Gerenciar cursos e turmas
- ✅ Controle financeiro completo

### **👨‍🏫 Professor**
- ✅ Visualizar turmas atribuídas
- ✅ Registrar presença
- ✅ Lançar notas
- ❌ Não pode gerenciar matrículas
- ❌ Não pode acessar financeiro

### **👨‍🎓 Aluno**
- ✅ Visualizar suas matrículas
- ✅ Consultar notas e presença
- ✅ Visualizar faturas
- ❌ Não pode alterar dados

### **🌐 Público**
- ✅ Visualizar cursos disponíveis
- ✅ Fazer inscrição
- ✅ Consultar status da inscrição
- ❌ Não pode acessar dados internos

---

## 📋 **Exemplos de Request/Response**

### **POST /public/enrollment**
```json
{
  "class_id": "uuid-da-turma",
  "student_data": {
    "name": "João Silva",
    "email": "<EMAIL>",
    "phone_number": "+5511999999999",
    "tax_id": "12345678901",
    "birth_date": "1990-01-01",
    "address": {
      "street": "Rua das Flores, 123",
      "city": "São Paulo",
      "state": "SP",
      "zip_code": "01234-567"
    }
  }
}
```

### **PUT /enrollment/:id/approve**
```json
{
  "notes": "Documentação aprovada",
  "generate_invoice": true
}
```

### **POST /attendance/bulk**
```json
{
  "class_id": "uuid-da-turma",
  "class_date": "2024-07-09",
  "attendances": [
    {
      "enrollment_id": "uuid-matricula-1",
      "status": "present"
    },
    {
      "enrollment_id": "uuid-matricula-2", 
      "status": "absent"
    }
  ]
}
```

---

**🎯 Total: ~60 endpoints cobrindo todo o fluxo de matrículas e gestão financeira!**
