# 🎓 **Planejamento do Sistema de Matrículas - EduSys**

## 📋 **Visão Geral**

Sistema completo de administração de matrículas com:
- ✅ **Matrícula pública** (candidatos se inscrevem online)
- ✅ **Aprovação interna** (secretaria aprova/rejeita)
- ✅ **Gestão educacional** (cursos, turmas, presença, notas)
- ✅ **Módulo financeiro** (controle de entradas e saídas)

---

## 🏗️ **Arquitetura do Sistema**

### **Módulos Principais:**
1. **📚 Módulo Educacional** - Cursos, turmas, matrículas, presença, notas
2. **💰 <PERSON><PERSON><PERSON>lo Financeiro** - Transações, pagamentos, faturas
3. **🔄 Integração** - Fluxos entre módulos

---

## 📊 **Entidades do Módulo Educacional**

### **1. Course (Curso)**
```typescript
- id: string (UUID)
- institution_id: string (FK)
- name: string
- description: string
- duration_months: number
- price: number
- max_students: number
- status: 'active' | 'inactive' | 'suspended'
- start_date: Date
- end_date: Date
- created_by: string
- updated_by: string
- created_at: Date
- updated_at: Date
```

### **2. Class (Turma)**
```typescript
- id: string (UUID)
- course_id: string (FK)
- name: string
- description: string
- max_students: number
- current_students: number
- status: 'open' | 'closed' | 'in_progress' | 'finished'
- start_date: Date
- end_date: Date
- schedule: JSON (dias/horários)
- created_by: string
- updated_by: string
- created_at: Date
- updated_at: Date
```

### **3. Enrollment (Matrícula)**
```typescript
- id: string (UUID)
- student_id: string (FK User)
- class_id: string (FK)
- institution_id: string (FK)
- status: 'pending' | 'approved' | 'rejected' | 'active' | 'suspended' | 'completed'
- enrollment_date: Date
- approval_date: Date
- approved_by: string (FK User)
- rejection_reason: string
- notes: string
- created_by: string
- updated_by: string
- created_at: Date
- updated_at: Date
```

### **4. Attendance (Presença)**
```typescript
- id: string (UUID)
- enrollment_id: string (FK)
- class_date: Date
- status: 'present' | 'absent' | 'late' | 'justified'
- notes: string
- recorded_by: string (FK User)
- created_by: string
- updated_by: string
- created_at: Date
- updated_at: Date
```

### **5. Grade (Nota)**
```typescript
- id: string (UUID)
- enrollment_id: string (FK)
- assessment_type: 'exam' | 'assignment' | 'project' | 'participation'
- assessment_name: string
- grade: number
- max_grade: number
- weight: number
- assessment_date: Date
- recorded_by: string (FK User)
- created_by: string
- updated_by: string
- created_at: Date
- updated_at: Date
```

---

## 💰 **Entidades do Módulo Financeiro**

### **1. FinancialTransaction (Transação Financeira)**
```typescript
- id: string (UUID)
- institution_id: string (FK)
- type: 'income' | 'expense'
- category: 'enrollment_fee' | 'monthly_fee' | 'material' | 'salary' | 'infrastructure' | 'other'
- amount: number
- description: string
- reference_id: string (FK - enrollment, invoice, etc)
- reference_type: 'enrollment' | 'invoice' | 'expense' | 'other'
- payment_method_id: string (FK)
- status: 'pending' | 'completed' | 'cancelled'
- transaction_date: Date
- due_date: Date
- paid_date: Date
- created_by: string
- updated_by: string
- created_at: Date
- updated_at: Date
```

### **2. PaymentMethod (Método de Pagamento)**
```typescript
- id: string (UUID)
- institution_id: string (FK)
- name: string
- type: 'cash' | 'credit_card' | 'debit_card' | 'bank_transfer' | 'pix' | 'check'
- is_active: boolean
- created_by: string
- updated_by: string
- created_at: Date
- updated_at: Date
```

### **3. Invoice (Fatura)**
```typescript
- id: string (UUID)
- enrollment_id: string (FK)
- institution_id: string (FK)
- invoice_number: string
- amount: number
- due_date: Date
- status: 'pending' | 'paid' | 'overdue' | 'cancelled'
- payment_date: Date
- payment_method_id: string (FK)
- notes: string
- created_by: string
- updated_by: string
- created_at: Date
- updated_at: Date
```

---

## 🔄 **Fluxo de Matrícula Pública**

### **1. Inscrição Pública**
```
Candidato → Formulário Online → Enrollment (status: 'pending')
```

### **2. Análise Interna**
```
Secretaria → Analisa → Aprova/Rejeita → Enrollment (status: 'approved'/'rejected')
```

### **3. Ativação da Matrícula**
```
Aprovação → Gera Invoice → Pagamento → Enrollment (status: 'active')
```

### **4. Acompanhamento**
```
Matrícula Ativa → Attendance + Grades → Conclusão
```

---

## 📁 **Estrutura de Arquivos (Padrão Existente)**

### **Domain Layer:**
```
src/domain/entities/
├── course.entity.ts
├── class.entity.ts
├── enrollment.entity.ts
├── attendance.entity.ts
├── grade.entity.ts
├── financial-transaction.entity.ts
├── payment-method.entity.ts
└── invoice.entity.ts

src/domain/abstractions/
├── course-repository.abstraction.ts
├── class-repository.abstraction.ts
├── enrollment-repository.abstraction.ts
├── attendance-repository.abstraction.ts
├── grade-repository.abstraction.ts
├── financial-transaction-repository.abstraction.ts
├── payment-method-repository.abstraction.ts
└── invoice-repository.abstraction.ts
```

### **Infrastructure Layer:**
```
src/infrastructure/entities/
├── course.entity.ts
├── class.entity.ts
├── enrollment.entity.ts
├── attendance.entity.ts
├── grade.entity.ts
├── financial-transaction.entity.ts
├── payment-method.entity.ts
└── invoice.entity.ts

src/infrastructure/repositories/
├── course-repository.ts
├── class-repository.ts
├── enrollment-repository.ts
├── attendance-repository.ts
├── grade-repository.ts
├── financial-transaction-repository.ts
├── payment-method-repository.ts
└── invoice-repository.ts
```

### **Use Cases:**
```
src/usecases/
├── course/
├── class/
├── enrollment/
├── attendance/
├── grade/
├── financial-transaction/
├── payment-method/
└── invoice/
```

### **Controllers:**
```
src/infrastructure/controllers/
├── course/
├── class/
├── enrollment/
├── attendance/
├── grade/
├── financial-transaction/
├── payment-method/
└── invoice/
```

---

## 🗄️ **Migrations (Ordem de Criação)**

1. **CreateCoursesTable** - Tabela de cursos
2. **CreateClassesTable** - Tabela de turmas
3. **CreateEnrollmentsTable** - Tabela de matrículas
4. **CreateAttendanceTable** - Tabela de presença
5. **CreateGradesTable** - Tabela de notas
6. **CreatePaymentMethodsTable** - Métodos de pagamento
7. **CreateFinancialTransactionsTable** - Transações financeiras
8. **CreateInvoicesTable** - Faturas

---

## 🎯 **Próximos Passos**

1. **Criar entidades e migrations** seguindo padrão TypeORM
2. **Implementar repositórios** com métodos específicos
3. **Desenvolver use cases** para cada funcionalidade
4. **Criar controllers** com validações e documentação Swagger
5. **Implementar fluxo de matrícula pública**
6. **Integrar módulo financeiro**
7. **Testes e validação**

---

## 💡 **Exemplo de Implementação (Padrão Existente)**

### **1. Entidade Course (Domain)**
```typescript
// src/domain/entities/course.entity.ts
export class CourseEntity {
  id: string;
  institution_id: string;
  name: string;
  description: string;
  duration_months: number;
  price: number;
  max_students: number;
  status: 'active' | 'inactive' | 'suspended';
  start_date: Date;
  end_date: Date;
  created_by: string;
  updated_by: string;
  created_at: Date;
  updated_at: Date;
}
```

### **2. Migration Example**
```typescript
// src/infrastructure/config/typeorm/migrations/CreateCoursesTable.ts
export class CreateCoursesTable1234567890 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'courses',
        columns: [
          { name: 'id', type: 'uuid', isPrimary: true, generationStrategy: 'uuid', default: 'uuid_generate_v4()' },
          { name: 'institution_id', type: 'uuid', isNullable: false },
          { name: 'name', type: 'varchar', length: '255', isNullable: false },
          { name: 'description', type: 'text', isNullable: true },
          { name: 'duration_months', type: 'int', isNullable: false },
          { name: 'price', type: 'decimal', precision: 10, scale: 2, isNullable: false },
          { name: 'max_students', type: 'int', isNullable: false },
          { name: 'status', type: 'enum', enum: ['active', 'inactive', 'suspended'], default: "'active'" },
          { name: 'start_date', type: 'timestamp', isNullable: true },
          { name: 'end_date', type: 'timestamp', isNullable: true },
          { name: 'created_by', type: 'uuid', isNullable: false },
          { name: 'updated_by', type: 'uuid', isNullable: true },
          { name: 'created_at', type: 'timestamp', default: 'CURRENT_TIMESTAMP' },
          { name: 'updated_at', type: 'timestamp', default: 'CURRENT_TIMESTAMP' }
        ],
        foreignKeys: [
          { columnNames: ['institution_id'], referencedTableName: 'institutions', referencedColumnNames: ['id'] },
          { columnNames: ['created_by'], referencedTableName: 'users', referencedColumnNames: ['id'] },
          { columnNames: ['updated_by'], referencedTableName: 'users', referencedColumnNames: ['id'] }
        ]
      })
    );
  }
}
```

### **3. Use Case Example**
```typescript
// src/usecases/course/create-course.usecase.ts
@Injectable()
export class CreateCourseUseCase {
  constructor(
    private readonly courseRepository: CourseRepository,
    private readonly logger: LoggerService
  ) {}

  async execute(courseData: CreateCourseDto, currentUser: CurrentUserData): Promise<CourseEntity> {
    this.logger.log('CREATE_COURSE_USE_CASE', 'Iniciando criação de curso');

    const course = await this.courseRepository.create({
      ...courseData,
      institution_id: currentUser.institution_id,
      created_by: currentUser.sub,
      created_at: new Date()
    });

    this.logger.log('CREATE_COURSE_USE_CASE', 'Curso criado com sucesso');
    return course;
  }
}
```

### **4. Controller Example**
```typescript
// src/infrastructure/controllers/course/course.controller.ts
@Controller('course')
@ApiTags('Courses')
@UseGuards(JwtAuthGuard)
@UseInterceptors(AuditInterceptor)
export class CourseController {
  @Post()
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({ status: HttpStatus.CREATED, type: CoursePresenter })
  async createCourse(
    @Body() createCourseDto: CreateCourseDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<CoursePresenter> {
    const course = await this.createCourseUsecaseProxy
      .getInstance()
      .execute(createCourseDto, user);
    return new CoursePresenter(course);
  }
}
```

---

## 🎯 **Cronograma de Implementação**

### **Semana 1: Fundação**
- ✅ Entidades do módulo educacional
- ✅ Migrations básicas
- ✅ Repositórios base

### **Semana 2: Módulo Educacional**
- ✅ Use cases de cursos e turmas
- ✅ Controllers e DTOs
- ✅ Fluxo de matrícula básico

### **Semana 3: Módulo Financeiro**
- ✅ Entidades financeiras
- ✅ Use cases de transações
- ✅ Integração com matrículas

### **Semana 4: Integração e Testes**
- ✅ Fluxo completo de matrícula pública
- ✅ Testes unitários
- ✅ Documentação final

---

**🚀 Pronto para implementação seguindo exatamente os padrões estabelecidos!**
