# 🧭 **GUIA DE NAVEGAÇÃO RÁPIDA - EDUSYS**

## 🎯 **ONDE ENCONTRAR CADA COISA**

### **📋 DOCUMENTAÇÕES PRINCIPAIS**
- **📊 Status Geral do Projeto:** `docs/STATUS_PROJETO_COMPLETO.md`
- **💳 Módulo Subscription Detalhado:** `docs/MODULO_SUBSCRIPTION_DETALHADO.md`
- **📚 Módulo Cursos:** `docs/MODULO_CURSOS_IMPLEMENTADO.md`
- **🗄️ Migrations Completas:** `docs/COMPLETE_MIGRATIONS_SUMMARY.md`
- **🧭 Este Guia:** `docs/GUIA_NAVEGACAO_RAPIDA.md`

---

## 🏗️ **ESTRUTURA DO PROJETO**

### **📁 Organização por Camadas (Clean Architecture)**

```
📂 src/
├── 📁 domain/                    # Camada de Domínio
│   ├── 📁 entities/             # Entidades de negócio
│   └── 📁 abstractions/         # Interfaces dos repositórios
├── 📁 usecases/                 # Casos de Uso
├── 📁 infrastructure/           # Camada de Infraestrutura
│   ├── 📁 entities/            # Entidades TypeORM
│   ├── 📁 repositories/        # Implementações dos repositórios
│   ├── 📁 controllers/         # Controllers REST
│   ├── 📁 config/              # Configurações
│   ├── 📁 guards/              # Guards de autenticação
│   └── 📁 common/              # Interceptors, filters, etc.
└── 📁 database/migrations/      # Migrations do banco
```

---

## 🔍 **COMO ENCONTRAR FUNCIONALIDADES**

### **🎯 Por Módulo:**

#### **💳 ASSINATURAS (Subscription)**
```
📍 Entidade Domain: src/domain/entities/subscription.entity.ts
📍 Entidade TypeORM: src/infrastructure/entities/subscription.entity.ts
📍 Repository: src/infrastructure/repositories/subscription-repository.ts
📍 Use Cases: src/usecases/subscription/
📍 Controller: src/infrastructure/controllers/subscription/
📍 DTOs: src/infrastructure/controllers/subscription/subscription.dto.ts
📍 Migration: database/migrations/*CreateSubscriptionsTable*
```

#### **📋 PLANOS (Plan)**
```
📍 Entidade Domain: src/domain/entities/plan.entity.ts
📍 Entidade TypeORM: src/infrastructure/entities/plan.entity.ts
📍 Repository: src/infrastructure/repositories/plan-repository.ts
📍 Use Cases: src/usecases/plan/
📍 Controller: src/infrastructure/controllers/plan/
```

#### **👥 USUÁRIOS (User)**
```
📍 Entidade Domain: src/domain/entities/user.entity.ts
📍 Entidade TypeORM: src/infrastructure/entities/user.entity.ts
📍 Repository: src/infrastructure/repositories/user-repository.ts
📍 Use Cases: src/usecases/user/
📍 Controller: src/infrastructure/controllers/user/
```

#### **🏢 INSTITUIÇÕES (Institution)**
```
📍 Entidade Domain: src/domain/entities/institution.entity.ts
📍 Entidade TypeORM: src/infrastructure/entities/institution.entity.ts
📍 Repository: src/infrastructure/repositories/institution-repository.ts
📍 Use Cases: src/usecases/institution/
📍 Controller: src/infrastructure/controllers/institution/
```

#### **📚 CURSOS (Course)**
```
📍 Entidade Domain: src/domain/entities/course.entity.ts
📍 Entidade TypeORM: src/infrastructure/entities/course.entity.ts
📍 Repository: src/infrastructure/repositories/course-repository.ts
📍 Use Cases: src/usecases/course/
📍 Controller: src/infrastructure/controllers/course/
```

---

## 🔧 **FUNCIONALIDADES POR TIPO**

### **🔐 AUTENTICAÇÃO**
```
📍 Login: src/usecases/auth/login.usecase.ts
📍 Logout: src/usecases/auth/logout.usecase.ts
📍 Refresh: src/usecases/auth/refresh-token.usecase.ts
📍 Guards: src/infrastructure/guards/
📍 Controller: src/infrastructure/controllers/auth/
```

### **🛡️ AUTORIZAÇÃO**
```
📍 Permissions: src/infrastructure/controllers/permission/
📍 Roles: src/infrastructure/controllers/role/
📍 Role-Permissions: src/infrastructure/controllers/role-permission/
📍 User-Roles: src/infrastructure/controllers/user-role/
```

### **🗄️ BANCO DE DADOS**
```
📍 Configuração: src/infrastructure/config/typeorm/
📍 Migrations: database/migrations/
📍 Entities: src/infrastructure/entities/
📍 Repositories: src/infrastructure/repositories/
```

### **📝 VALIDAÇÕES**
```
📍 DTOs: src/infrastructure/controllers/*/**.dto.ts
📍 Guards: src/infrastructure/guards/
📍 Interceptors: src/infrastructure/common/interceptors/
```

---

## 🎯 **CENÁRIOS COMUNS**

### **🔍 "Quero entender como funciona X"**

#### **💳 Assinaturas:**
1. Leia: `docs/MODULO_SUBSCRIPTION_DETALHADO.md`
2. Veja: `src/usecases/subscription/create-subscription.usecase.ts`
3. Teste: `src/infrastructure/controllers/subscription/subscription.controller.ts`

#### **📚 Cursos:**
1. Leia: `docs/MODULO_CURSOS_IMPLEMENTADO.md`
2. Veja: `src/usecases/course/`
3. Teste: `src/infrastructure/controllers/course/`

#### **👥 Usuários:**
1. Veja: `src/usecases/user/create-user.usecase.ts`
2. Entenda: `src/infrastructure/entities/user.entity.ts`
3. Teste: `src/infrastructure/controllers/user/user.controller.ts`

### **🛠️ "Quero implementar Y"**

#### **Novo Módulo:**
1. Copie estrutura de: `src/usecases/course/` (exemplo completo)
2. Siga padrão: Domain → Infrastructure → Use Cases → Controller
3. Adicione em: `src/infrastructure/repositories/repositories.module.ts`

#### **Nova Funcionalidade:**
1. Crie use case em: `src/usecases/[modulo]/`
2. Adicione no controller: `src/infrastructure/controllers/[modulo]/`
3. Crie DTO se necessário: `src/infrastructure/controllers/[modulo]/[modulo].dto.ts`

#### **Nova Migration:**
1. Execute: `npm run create:migration NomeDaMigration`
2. Edite: `database/migrations/[timestamp]-NomeDaMigration.ts`
3. Execute: `npm run run:migration`

### **🐛 "Tenho um problema com Z"**

#### **Erro de Autenticação:**
1. Verifique: `src/infrastructure/guards/jwt-auth.guard.ts`
2. Teste: Login via `POST /auth/login`
3. Valide: Token no header `Authorization: Bearer <token>`

#### **Erro de Banco:**
1. Verifique: `src/infrastructure/config/typeorm/typeorm.config.ts`
2. Teste: Conexão com `npm run migration:show`
3. Valide: Migrations com `npm run run:migration`

#### **Erro de Validação:**
1. Verifique: DTOs em `src/infrastructure/controllers/*/**.dto.ts`
2. Teste: Payload da requisição
3. Valide: Decorators `@IsNotEmpty()`, `@IsUUID()`, etc.

---

## 📚 **COMANDOS ÚTEIS**

### **🗄️ Banco de Dados:**
```bash
# Criar migration
npm run create:migration NomeDaMigration

# Executar migrations
npm run run:migration

# Reverter última migration
npm run revert:migration

# Ver status das migrations
npm run migration:show
```

### **🏗️ Desenvolvimento:**
```bash
# Iniciar em desenvolvimento
npm run start:dev

# Build do projeto
npm run build

# Formatar código
npm run format

# Executar testes
npm run test
```

### **📋 Logs e Debug:**
```bash
# Ver logs da aplicação
npm run start:dev

# Debug específico
console.log() nos use cases

# Verificar build
npm run build
```

---

## 🎯 **PRÓXIMOS PASSOS RECOMENDADOS**

### **📋 Para Entender o Projeto:**
1. Leia: `docs/STATUS_PROJETO_COMPLETO.md`
2. Explore: Módulo de Subscription (completo)
3. Compare: Com módulo de Course (completo)
4. Identifique: Padrões arquiteturais

### **🛠️ Para Continuar Desenvolvimento:**
1. Escolha: Módulo para implementar (veja status)
2. Copie: Estrutura de módulo completo
3. Adapte: Para suas necessidades
4. Teste: Com Postman collection

### **🔍 Para Debug:**
1. Use: Console.log nos use cases
2. Verifique: Logs da aplicação
3. Teste: Endpoints individualmente
4. Valide: Banco de dados

---

## 🆘 **AJUDA RÁPIDA**

### **❓ Dúvidas Frequentes:**

**Q: Como criar um novo módulo?**
A: Copie a estrutura do módulo `course` e adapte.

**Q: Como funciona a autenticação?**
A: JWT tokens via `POST /auth/login`, guards automáticos.

**Q: Como adicionar nova migration?**
A: `npm run create:migration Nome` → editar → `npm run run:migration`

**Q: Onde estão os campos de cada entidade?**
A: `src/infrastructure/entities/[nome].entity.ts`

**Q: Como testar endpoints?**
A: Use a collection do Postman: `EduSys_API_Collection_COMPLETE.postman_collection.json`

### **🔗 Links Rápidos:**
- **Status Geral:** `docs/STATUS_PROJETO_COMPLETO.md`
- **Subscription:** `docs/MODULO_SUBSCRIPTION_DETALHADO.md`
- **Postman:** `EduSys_API_Collection_COMPLETE.postman_collection.json`
- **Migrations:** `database/migrations/`

**🎯 Este guia te ajuda a navegar 100% do projeto!**
