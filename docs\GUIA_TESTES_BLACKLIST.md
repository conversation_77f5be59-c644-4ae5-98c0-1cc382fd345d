# 🧪 **GUIA DE TESTES - SISTEMA BLACKLIST REDIS**

## 🎯 **OBJETIVO**

Guia prático para testar o sistema de blacklist de tokens JWT implementado com Redis no EduSys.

---

## 🔧 **PRÉ-REQUISITOS**

### **1. Redis Rodando:**
```bash
# Verificar se Redis está rodando
redis-cli ping
# Deve retornar: PONG

# Se não estiver rodando (Windows com Docker):
docker run -d -p 6379:6379 redis:alpine

# Ou instalar Redis localmente
```

### **2. Aplicação Configurada:**
```env
# Verificar .env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

### **3. Postman Collection:**
- Importar `EduSys_API_Collection_COMPLETE.postman_collection.json`
- Configurar environment com URL da API

---

## 🧪 **ROTEIRO DE TESTES**

### **FASE 1: 🚪 TESTES DE LOGOUT**

#### **Teste 1.1: Logout Básico**
```
1. POST /auth/login
   - Fazer login e obter tokens
   - Salvar accessToken e refreshToken

2. POST /auth/logout
   - Usar token válido
   - Verificar resposta 200 OK

3. Verificar Blacklist no Redis:
   redis-cli
   > KEYS blacklist:*
   > GET blacklist:{seu_access_token}
   > TTL blacklist:{seu_access_token}
```

#### **Teste 1.2: Token Invalidado**
```
1. Após logout, tentar usar o token:
   GET /user (com token do logout)
   - Deve retornar 401 Unauthorized
   - Mensagem: "Token has been invalidated"

2. Verificar logs da aplicação:
   - Deve aparecer: "Token blacklisted for user: {user_id}"
```

#### **Teste 1.3: Múltiplos Tokens**
```
1. Fazer login em múltiplas sessões
2. Fazer logout com access + refresh token
3. Verificar se ambos foram para blacklist:
   redis-cli
   > KEYS blacklist:*
   (Deve mostrar 2 chaves)
```

---

### **FASE 2: 🔄 TESTES DE REFRESH TOKEN**

#### **Teste 2.1: Refresh Normal**
```
1. POST /auth/login
   - Obter refreshToken

2. POST /auth/refresh
   - Usar refreshToken válido
   - Verificar novos tokens retornados

3. Verificar Blacklist:
   redis-cli
   > KEYS blacklist:*
   (RefreshToken antigo deve estar na blacklist)
```

#### **Teste 2.2: Refresh Token na Blacklist**
```
1. Fazer refresh uma vez (token antigo vai para blacklist)
2. Tentar usar o refreshToken antigo novamente:
   POST /auth/refresh (com token antigo)
   - Deve retornar 401 Unauthorized
   - Mensagem: "Refresh token has been invalidated"
```

#### **Teste 2.3: Novos Tokens Funcionam**
```
1. Após refresh bem-sucedido
2. Usar novo accessToken:
   GET /user (com novo token)
   - Deve retornar 200 OK
   - Token não deve estar na blacklist
```

---

### **FASE 3: 🛡️ TESTES DE VALIDAÇÃO JWT**

#### **Teste 3.1: Token Válido**
```
1. POST /auth/login
2. GET /user (com token válido)
   - Deve retornar 200 OK
   - Verificar dados do usuário
```

#### **Teste 3.2: Token na Blacklist**
```
1. Fazer logout (token vai para blacklist)
2. Tentar qualquer endpoint protegido:
   GET /user, GET /course, etc.
   - Todos devem retornar 401 Unauthorized
```

#### **Teste 3.3: Diferentes Endpoints**
```
Testar com token na blacklist:
- GET /user → 401
- GET /course → 401  
- POST /course → 401
- GET /institution → 401

Todos devem falhar com mesma mensagem.
```

---

### **FASE 4: ⚡ TESTES DE PERFORMANCE**

#### **Teste 4.1: TTL Automático**
```
1. Fazer login e logout
2. Verificar TTL no Redis:
   redis-cli
   > TTL blacklist:{token}
   
3. Aguardar TTL expirar (ou usar token com TTL curto)
4. Verificar se chave foi removida automaticamente:
   > GET blacklist:{token}
   (Deve retornar nil)
```

#### **Teste 4.2: Múltiplos Tokens**
```
1. Fazer login em 5+ sessões diferentes
2. Fazer logout de todas (múltiplos tokens)
3. Verificar performance:
   - Tempo de resposta do logout
   - Quantidade de chaves no Redis
   - TTL de cada token
```

#### **Teste 4.3: Estrutura das Chaves**
```
redis-cli
> KEYS blacklist:*
> INFO memory
> DBSIZE

Verificar:
- Formato das chaves: blacklist:{jwt_completo}
- Valor: "blacklisted"
- TTL apropriado
```

---

### **FASE 5: 🔧 TESTES DE CONFIGURAÇÃO**

#### **Teste 5.1: Conexão Redis**
```
1. Iniciar aplicação
2. Verificar logs:
   ✅ Redis connected successfully

3. Parar Redis e reiniciar aplicação:
   ❌ Redis connection error: ...

4. Reiniciar Redis:
   ✅ Redis connected successfully
```

#### **Teste 5.2: Fail-Safe**
```
1. Parar Redis: docker stop {redis_container}
2. Tentar fazer login:
   - Deve funcionar normalmente
   - Logout pode falhar, mas não quebra app

3. Tentar usar tokens:
   - Deve funcionar (fail-safe ativo)
   - Verificar logs de erro Redis
```

#### **Teste 5.3: Reconexão**
```
1. Com aplicação rodando, parar Redis
2. Fazer algumas operações (devem falhar)
3. Reiniciar Redis
4. Fazer logout/refresh:
   - Deve voltar a funcionar automaticamente
```

---

## 📊 **COMANDOS ÚTEIS REDIS**

### **🔍 Monitoramento:**
```bash
# Ver todas as chaves de blacklist
redis-cli KEYS "blacklist:*"

# Ver valor de uma chave
redis-cli GET "blacklist:{token}"

# Ver TTL de uma chave
redis-cli TTL "blacklist:{token}"

# Monitorar comandos em tempo real
redis-cli MONITOR

# Informações do Redis
redis-cli INFO memory
redis-cli INFO stats
```

### **🧹 Limpeza:**
```bash
# Remover todas as chaves de blacklist
redis-cli EVAL "return redis.call('del', unpack(redis.call('keys', 'blacklist:*')))" 0

# Limpar todo o banco (CUIDADO!)
redis-cli FLUSHDB
```

---

## 📋 **CHECKLIST DE VALIDAÇÃO**

### **✅ Funcionalidades Básicas:**
- [ ] Login gera tokens válidos
- [ ] Logout invalida tokens no Redis
- [ ] Refresh invalida token antigo
- [ ] Tokens na blacklist são rejeitados
- [ ] TTL automático funciona

### **✅ Segurança:**
- [ ] Impossível reutilizar tokens após logout
- [ ] Impossível reutilizar refresh tokens
- [ ] Validação em todos os endpoints protegidos
- [ ] Logs de segurança apropriados

### **✅ Performance:**
- [ ] Operações em lote funcionam
- [ ] TTL baseado na expiração do JWT
- [ ] Limpeza automática de tokens expirados
- [ ] Reconexão automática do Redis

### **✅ Robustez:**
- [ ] Fail-safe quando Redis está offline
- [ ] Logs de erro apropriados
- [ ] Aplicação não quebra sem Redis
- [ ] Reconexão automática funciona

---

## 🐛 **TROUBLESHOOTING**

### **❌ Redis não conecta:**
```
Verificar:
1. Redis está rodando? redis-cli ping
2. Porta correta? REDIS_PORT=6379
3. Host correto? REDIS_HOST=localhost
4. Senha necessária? REDIS_PASSWORD=
```

### **❌ Tokens não são invalidados:**
```
Verificar:
1. Logs da aplicação para erros Redis
2. Chaves no Redis: KEYS blacklist:*
3. TTL das chaves: TTL blacklist:{token}
4. Configuração do TokenBlacklistService
```

### **❌ Fail-safe não funciona:**
```
Verificar:
1. Método isBlacklisted() retorna false em erro
2. Logs de erro Redis aparecem
3. Aplicação continua funcionando
4. Try-catch nos métodos do serviço
```

---

## 🎯 **RESULTADOS ESPERADOS**

### **✅ Sucesso Total:**
- Todos os tokens são invalidados corretamente
- Performance adequada (< 100ms por operação)
- Logs claros e informativos
- Fail-safe funciona quando necessário
- TTL automático limpa tokens expirados

### **📊 Métricas de Sucesso:**
- **0 tokens** reutilizados após logout
- **100%** dos refresh tokens invalidados
- **< 50ms** tempo de verificação de blacklist
- **0 erros** de aplicação por falha do Redis

**🎉 SISTEMA DE BLACKLIST TOTALMENTE FUNCIONAL E SEGURO!**
