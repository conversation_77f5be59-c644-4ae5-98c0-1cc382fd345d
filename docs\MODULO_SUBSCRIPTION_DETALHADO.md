# 💳 **MÓDULO DE ASSINATURAS - DOCUMENTAÇÃO DETALHADA**

## 🎯 **VISÃO GERAL**

O módulo de **Subscription** gerencia as assinaturas das instituições aos planos do sistema EduSys. Cada instituição pode ter apenas **UMA assinatura ativa** por vez.

---

## 📋 **ESTRUTURA COMPLETA**

### **🏗️ Arquitetura Clean**
```
📁 Domain Layer
├── entities/subscription.entity.ts        ✅ Entidade de domínio
└── abstractions/subscription-repository.abstraction.ts ✅ Interface

📁 Infrastructure Layer
├── entities/subscription.entity.ts        ✅ Entidade TypeORM
├── repositories/subscription-repository.ts ✅ Implementação
├── controllers/subscription/
│   ├── subscription.controller.ts         ✅ Controller REST
│   ├── subscription.dto.ts               ✅ DTOs
│   └── subscription.presenter.ts         ✅ Presenter
└── usecases-proxy/subscription/           ✅ Proxy module

📁 Use Cases
├── create-subscription.usecase.ts         ✅ Criar assinatura
├── update-subscription.usecase.ts         ✅ Atualizar assinatura
├── delete-subscription.usecase.ts         ✅ Cancelar assinatura
├── get-subscription.usecase.ts           ✅ Buscar assinatura
└── get-subscriptions.usecase.ts          ✅ Listar assinaturas
```

---

## 🗄️ **ESTRUTURA DO BANCO DE DADOS**

### **📊 Tabela: `core.subscriptions`**

```sql
CREATE TABLE core.subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    institution_id UUID NOT NULL REFERENCES core.institutions(id) ON DELETE CASCADE,
    plan_id UUID NOT NULL REFERENCES core.plans(id) ON DELETE RESTRICT,
    status VARCHAR(20) DEFAULT 'active',
    stripe_subscription_id VARCHAR NULL,
    stripe_customer_id VARCHAR NULL,
    current_period_start TIMESTAMP NULL,
    current_period_end TIMESTAMP NULL,
    trial_start TIMESTAMP NULL,
    trial_end TIMESTAMP NULL,
    canceled_at TIMESTAMP NULL,
    ended_at TIMESTAMP NULL,
    metadata JSONB DEFAULT '{}',
    created_by UUID NOT NULL,
    updated_by UUID NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL
);
```

### **🔗 Relacionamentos:**
- **institution_id** → `core.institutions(id)` (CASCADE)
- **plan_id** → `core.plans(id)` (RESTRICT)

### **📝 Índices:**
- Primary Key: `id`
- Foreign Keys: `institution_id`, `plan_id`
- Unique: Uma assinatura ativa por instituição (lógica de negócio)

---

## 📋 **CAMPOS DETALHADOS**

### **🔑 Campos Obrigatórios:**
```typescript
id: string                    // UUID gerado automaticamente
institution_id: string        // Referência à instituição
plan_id: string              // Referência ao plano
status: string               // Status da assinatura
created_by: string           // Usuário que criou
```

### **📅 Campos de Período:**
```typescript
current_period_start: Date   // Início do período atual
current_period_end: Date     // Fim do período atual
trial_start: Date           // Início do trial (opcional)
trial_end: Date             // Fim do trial (opcional)
canceled_at: Date           // Data de cancelamento (opcional)
ended_at: Date              // Data de encerramento (opcional)
```

### **💳 Campos Stripe:**
```typescript
stripe_subscription_id: string  // ID da assinatura no Stripe
stripe_customer_id: string      // ID do cliente no Stripe
```

### **📊 Campos de Metadados:**
```typescript
metadata: Record<string, any>   // Dados adicionais em JSON
```

### **🕐 Campos de Auditoria:**
```typescript
created_by: string           // Usuário que criou
updated_by: string           // Usuário que atualizou (nullable)
created_at: Date            // Data de criação
updated_at: Date            // Data de atualização (nullable)
```

---

## 🎯 **STATUS POSSÍVEIS**

### **✅ Status Ativos:**
- **`active`** - Assinatura ativa e funcionando
- **`trialing`** - Em período de trial

### **⚠️ Status de Transição:**
- **`incomplete`** - Pagamento pendente
- **`past_due`** - Pagamento em atraso

### **❌ Status Inativos:**
- **`canceled`** - Cancelada pelo usuário
- **`expired`** - Expirada por falta de pagamento
- **`inactive`** - Inativa por outros motivos

---

## 🔧 **FUNCIONALIDADES IMPLEMENTADAS**

### **1. ✅ Criar Assinatura**
```typescript
POST /subscription
```

**Validações:**
- ✅ Instituição deve existir
- ✅ Plano deve existir e estar ativo
- ✅ Instituição não pode ter assinatura ativa
- ✅ Campos obrigatórios validados

**Ação Especial:**
- 🔄 **Atualiza automaticamente a instituição** com `subscription_id`
- 🔄 **Define status da instituição** como 'active'

### **2. ✅ Buscar Assinatura por ID**
```typescript
GET /subscription/:id
```

**Retorna:**
- Dados completos da assinatura
- Relacionamentos com instituição e plano

### **3. ✅ Listar Assinaturas**
```typescript
GET /subscription
```

**Filtros disponíveis:**
- Por instituição
- Por plano
- Por status
- Por Stripe subscription ID

### **4. ✅ Atualizar Assinatura**
```typescript
PUT /subscription/:id
```

**Campos atualizáveis:**
- Status
- Datas de período
- Metadados
- IDs do Stripe

### **5. ✅ Cancelar Assinatura**
```typescript
DELETE /subscription/:id
```

**Ações:**
- Define `canceled_at`
- Atualiza status para 'canceled'
- Mantém histórico

---

## 🔍 **MÉTODOS DO REPOSITORY**

### **📋 Métodos Implementados:**

```typescript
// CRUD Básico
insert(subscription: Partial<SubscriptionEntity>): Promise<SubscriptionEntity>
findAll(): Promise<SubscriptionEntity[]>
findById(id: string): Promise<SubscriptionEntity | null>
update(id: string, subscription: SubscriptionEntity): Promise<SubscriptionEntity>
delete(id: string): Promise<void>

// Buscas Específicas
findByInstitutionId(institutionId: string): Promise<SubscriptionEntity[]>
findActiveByInstitutionId(institutionId: string): Promise<SubscriptionEntity | null>
findByPlanId(planId: string): Promise<SubscriptionEntity[]>
findByStatus(status: string): Promise<SubscriptionEntity[]>

// Integração Stripe
findByStripeSubscriptionId(stripeId: string): Promise<SubscriptionEntity | null>
```

---

## 🎯 **REGRAS DE NEGÓCIO**

### **🔒 Regras Críticas:**

1. **Uma Assinatura Ativa por Instituição**
   - Instituição só pode ter UMA assinatura com status 'active'
   - Validação no use case `create-subscription`

2. **Plano Deve Estar Ativo**
   - Só permite criar assinatura com planos `is_active = true`

3. **Instituição Deve Existir**
   - Validação de existência antes de criar assinatura

4. **Atualização Automática da Instituição**
   - Quando assinatura é criada, atualiza `institution.subscription_id`
   - Define `institution.status = 'active'`

### **📅 Regras de Período:**

1. **Trial Period**
   - `trial_start` e `trial_end` são opcionais
   - Se definidos, status pode ser 'trialing'

2. **Current Period**
   - `current_period_start` e `current_period_end` definem período de cobrança
   - Atualizados automaticamente pelo Stripe

---

## 🔄 **FLUXO DE CRIAÇÃO DE ASSINATURA**

```mermaid
graph TD
    A[Receber CreateSubscriptionDto] --> B[Validar Instituição]
    B --> C[Validar Plano Ativo]
    C --> D[Verificar Assinatura Ativa Existente]
    D --> E{Já tem assinatura ativa?}
    E -->|Sim| F[Retornar Erro CONFLICT]
    E -->|Não| G[Criar Nova Assinatura]
    G --> H[Atualizar Instituição]
    H --> I[Definir subscription_id]
    I --> J[Definir status = 'active']
    J --> K[Log de Sucesso]
    K --> L[Retornar Assinatura Criada]
```

---

## 📊 **RELACIONAMENTOS**

### **🏢 Com Instituição:**
```typescript
// Subscription pertence a uma Institution
@ManyToOne(() => Institution, { onDelete: 'CASCADE' })
institution: Institution

// Institution pode ter uma Subscription
@OneToOne(() => Subscription, { onDelete: 'SET NULL' })
subscription: Subscription
```

### **📋 Com Plano:**
```typescript
// Subscription pertence a um Plan
@ManyToOne(() => Plan, { onDelete: 'RESTRICT' })
plan: Plan

// Plan pode ter muitas Subscriptions
@OneToMany(() => Subscription, subscription => subscription.plan)
subscriptions: Subscription[]
```

---

## 🧪 **EXEMPLOS DE USO**

### **1. Criar Assinatura:**
```json
POST /subscription
{
  "institution_id": "uuid-da-instituicao",
  "plan_id": "uuid-do-plano",
  "status": "active",
  "trial_start": "2024-01-01T00:00:00Z",
  "trial_end": "2024-01-07T23:59:59Z",
  "metadata": {
    "promocao": "desconto_primeiro_mes",
    "origem": "site"
  }
}
```

### **2. Buscar Assinatura Ativa da Instituição:**
```typescript
const subscription = await subscriptionRepository
  .findActiveByInstitutionId('institution-uuid');
```

### **3. Listar por Status:**
```typescript
const activeSubscriptions = await subscriptionRepository
  .findByStatus('active');
```

---

## 🚨 **PONTOS DE ATENÇÃO**

### **⚠️ Cuidados Importantes:**

1. **Constraint de Assinatura Única**
   - Implementada via lógica de negócio, não constraint DB
   - Verificar sempre antes de criar nova assinatura

2. **Relacionamento com Institution**
   - Quando assinatura é criada, institution é atualizada
   - Manter sincronização entre as entidades

3. **Integração Stripe**
   - Campos `stripe_*` são opcionais
   - Podem ser preenchidos posteriormente via webhook

4. **Campos Nullable**
   - `updated_by` e `updated_at` são nullable
   - Preenchidos apenas quando há atualização

---

## 🎯 **PRÓXIMOS PASSOS**

### **🔄 Melhorias Futuras:**
1. **Webhook Stripe** - Sincronização automática
2. **Renovação Automática** - Use case para renovar assinaturas
3. **Histórico de Mudanças** - Log de alterações de status
4. **Notificações** - Alertas de vencimento/cancelamento
5. **Métricas** - Dashboard de assinaturas ativas

**📚 Esta documentação cobre 100% do módulo de Subscription implementado!**
