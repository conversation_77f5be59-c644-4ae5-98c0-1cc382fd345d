# Educ-Sys API

This repository contains the backend API for the Educ-Sys project.

## Technologies Used

* NestJS
* PostgreSQL
* TypeORM

## Setup

1. Clone the repository:
   ```bash
   git clone <repository_url>
   ```
2. Install dependencies:
   ```bash
   yarn install
   ```
3. Configure the database connection in env file.
4. Run database migrations:
   ```bash
   yarn run:migration
   ```
5. Start the server:
   ```bash
   yarn start
   ```

## API Endpoints

(Documentation of API endpoints will be added here)

## Contributing

(Information on how to contribute will be added here)

## License

This project is private.
