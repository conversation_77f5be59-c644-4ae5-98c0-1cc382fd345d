# 🔒 **IMPLEMENTAÇÃO REDIS BLACKLIST - EDUSYS**

## 🎯 **VISÃO GERAL**

Implementação completa do sistema de blacklist de tokens JWT usando Redis para invalidação segura de tokens de acesso e refresh tokens.

---

## 🏗️ **ARQUITETURA IMPLEMENTADA**

### **📁 Estrutura de Arquivos:**

```
📂 src/infrastructure/
├── 📁 config/redis/
│   ├── redis.config.ts           ✅ Configuração do Redis
│   └── redis.module.ts           ✅ Módulo do Redis
├── 📁 services/token-blacklist/
│   ├── token-blacklist.service.ts ✅ Serviço de blacklist
│   └── token-blacklist.module.ts  ✅ Módulo do serviço
└── 📁 common/strategies/
    └── jwt.strategy.ts           ✅ Verificação de blacklist
```

---

## ⚙️ **CONFIGURAÇÃO DO REDIS**

### **🔧 RedisConfigService:**

```typescript
// src/infrastructure/config/redis/redis.config.ts
@Injectable()
export class RedisConfigService {
  private readonly redisClient: Redis;

  constructor(private configService: ConfigService) {
    this.redisClient = new Redis({
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD'),
      db: this.configService.get<number>('REDIS_DB', 0),
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });
  }

  getClient(): Redis {
    return this.redisClient;
  }
}
```

### **📋 Variáveis de Ambiente:**

```env
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

---

## 🛡️ **SERVIÇO DE BLACKLIST**

### **🔒 TokenBlacklistService:**

#### **Métodos Principais:**

```typescript
// Adicionar token à blacklist
async addToBlacklist(token: string, expirationTime?: number): Promise<void>

// Verificar se token está na blacklist
async isBlacklisted(token: string): Promise<boolean>

// Adicionar múltiplos tokens (logout de todas as sessões)
async addMultipleToBlacklist(tokens: string[]): Promise<void>

// Remover token da blacklist (raramente usado)
async removeFromBlacklist(token: string): Promise<void>

// Limpeza automática de tokens expirados
async cleanupExpiredTokens(): Promise<void>
```

#### **🔑 Funcionalidades Especiais:**

1. **TTL Automático:**
   - Extrai tempo de expiração do próprio token JWT
   - Define TTL no Redis baseado na expiração do token
   - Tokens expirados são removidos automaticamente

2. **Fail-Safe:**
   - Em caso de erro no Redis, permite o token (não bloqueia sistema)
   - Logs detalhados para debug

3. **Performance:**
   - Usa pipeline para operações em lote
   - Chaves organizadas com prefixo `blacklist:`

---

## 🔄 **INTEGRAÇÃO COM USE CASES**

### **🚪 Logout UseCase:**

```typescript
// src/usecases/auth/logout.usecase.ts
async execute(logoutData: LogoutRequest): Promise<void> {
  // Invalidar tokens na blacklist
  const tokensToBlacklist: string[] = [];

  if (logoutData.accessToken) {
    tokensToBlacklist.push(logoutData.accessToken);
  }

  if (logoutData.refreshToken) {
    tokensToBlacklist.push(logoutData.refreshToken);
  }

  if (tokensToBlacklist.length > 0) {
    await this.tokenBlacklistService.addMultipleToBlacklist(tokensToBlacklist);
  }
}
```

### **🔄 Refresh Token UseCase:**

```typescript
// src/usecases/auth/refresh-token.usecase.ts
async execute(refreshToken: string): Promise<RefreshTokenResponse> {
  // Verificar se o refresh token está na blacklist
  const isBlacklisted = await this.tokenBlacklistService.isBlacklisted(refreshToken);
  if (isBlacklisted) {
    throw new HttpException('Refresh token has been invalidated', HttpStatus.UNAUTHORIZED);
  }

  // ... processar refresh ...

  // Invalidar o refresh token antigo
  await this.tokenBlacklistService.addToBlacklist(refreshToken);
}
```

---

## 🛡️ **VERIFICAÇÃO NO JWT STRATEGY**

### **🔍 Validação Automática:**

```typescript
// src/infrastructure/common/strategies/jwt.strategy.ts
async validate(request: Request, payload: any) {
  // Extrair o token do request
  const token = request.cookies?.accessToken || 
               (request.headers.authorization?.replace('Bearer ', ''));

  // Verificar se o token está na blacklist
  if (token) {
    const isBlacklisted = await this.tokenBlacklistService.isBlacklisted(token);
    if (isBlacklisted) {
      this.exceptionService.UnauthorizedException({
        message: 'Token has been invalidated'
      });
    }
  }

  // ... continuar validação ...
}
```

---

## 🔧 **FLUXOS DE FUNCIONAMENTO**

### **🚪 Fluxo de Logout:**

```mermaid
graph TD
    A[POST /auth/logout] --> B[Extrair Tokens do Request]
    B --> C[Chamar LogoutUseCase]
    C --> D[Adicionar Tokens à Blacklist]
    D --> E[Limpar Cookies]
    E --> F[Retornar Sucesso]
```

### **🔄 Fluxo de Refresh Token:**

```mermaid
graph TD
    A[POST /auth/refresh] --> B[Verificar Token na Blacklist]
    B --> C{Token na Blacklist?}
    C -->|Sim| D[Retornar 401 Unauthorized]
    C -->|Não| E[Validar Token JWT]
    E --> F[Gerar Novos Tokens]
    F --> G[Invalidar Token Antigo]
    G --> H[Retornar Novos Tokens]
```

### **🔍 Fluxo de Validação JWT:**

```mermaid
graph TD
    A[Request com JWT] --> B[JWT Strategy]
    B --> C[Extrair Token]
    C --> D[Verificar Blacklist]
    D --> E{Token na Blacklist?}
    E -->|Sim| F[Retornar 401 Unauthorized]
    E -->|Não| G[Continuar Validação]
    G --> H[Autorizar Request]
```

---

## 📊 **ESTRUTURA NO REDIS**

### **🔑 Formato das Chaves:**

```
blacklist:{jwt_token_completo}
```

### **💾 Valores Armazenados:**

```
Chave: blacklist:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Valor: "blacklisted"
TTL: 3600 (segundos até expiração do token)
```

### **⏰ TTL Automático:**

- **Extração do Token:** TTL baseado no campo `exp` do JWT
- **TTL Padrão:** 7 dias se não conseguir extrair
- **Limpeza Automática:** Redis remove chaves expiradas automaticamente

---

## 🧪 **TESTES E VALIDAÇÃO**

### **✅ Cenários de Teste:**

#### **Logout:**
- [ ] Logout com access token válido
- [ ] Logout com refresh token válido
- [ ] Logout com ambos os tokens
- [ ] Logout sem tokens
- [ ] Verificar se tokens foram invalidados

#### **Refresh Token:**
- [ ] Refresh com token válido
- [ ] Refresh com token na blacklist
- [ ] Verificar se token antigo foi invalidado
- [ ] Verificar se novos tokens funcionam

#### **Validação JWT:**
- [ ] Request com token válido
- [ ] Request com token na blacklist
- [ ] Request sem token
- [ ] Verificar logs de segurança

---

## 🔧 **CONFIGURAÇÃO E DEPLOY**

### **📋 Pré-requisitos:**

1. **Redis Server:**
   ```bash
   # Instalar Redis (Ubuntu/Debian)
   sudo apt update
   sudo apt install redis-server
   
   # Iniciar Redis
   sudo systemctl start redis-server
   sudo systemctl enable redis-server
   ```

2. **Dependências Node.js:**
   ```bash
   npm install ioredis
   npm install --save-dev @types/ioredis
   ```

3. **Variáveis de Ambiente:**
   ```env
   REDIS_HOST=localhost
   REDIS_PORT=6379
   REDIS_PASSWORD=sua_senha_redis
   REDIS_DB=0
   ```

### **🚀 Inicialização:**

```typescript
// A aplicação conecta automaticamente ao Redis na inicialização
// Logs de conexão aparecem no console:
// ✅ Redis connected successfully
```

---

## 🛡️ **SEGURANÇA E BOAS PRÁTICAS**

### **🔒 Medidas de Segurança:**

1. **Fail-Safe:** Sistema continua funcionando mesmo se Redis falhar
2. **TTL Automático:** Tokens expirados são removidos automaticamente
3. **Logs Detalhados:** Todas as operações são logadas
4. **Validação Dupla:** Verificação no JWT Strategy e Use Cases

### **⚡ Performance:**

1. **Pipeline:** Operações em lote para múltiplos tokens
2. **TTL Inteligente:** Baseado na expiração real do token
3. **Conexão Lazy:** Conecta apenas quando necessário
4. **Retry Logic:** Reconexão automática em caso de falha

### **📊 Monitoramento:**

1. **Logs Estruturados:** Contexto claro para debug
2. **Métricas Redis:** Conexões, operações, erros
3. **Health Checks:** Verificação de conectividade

---

## 🎯 **BENEFÍCIOS DA IMPLEMENTAÇÃO**

### **✅ Segurança:**
- **Invalidação Imediata:** Tokens são invalidados instantaneamente
- **Logout Seguro:** Impossível reutilizar tokens após logout
- **Refresh Seguro:** Tokens antigos são invalidados automaticamente

### **✅ Performance:**
- **Cache Distribuído:** Redis oferece alta performance
- **TTL Automático:** Limpeza automática sem overhead
- **Operações Atômicas:** Garantia de consistência

### **✅ Escalabilidade:**
- **Distribuído:** Funciona em múltiplas instâncias da aplicação
- **Horizontal:** Redis pode ser clusterizado
- **Resiliente:** Fail-safe em caso de problemas

**🎉 IMPLEMENTAÇÃO COMPLETA E ROBUSTA DO SISTEMA DE BLACKLIST!**

O sistema agora oferece invalidação segura de tokens com alta performance e escalabilidade, seguindo as melhores práticas de segurança para aplicações JWT.
