# 💳 Guia de Integração Stripe - EduSys API

## 🎯 Visão Geral

O EduSys API possui integração completa com Stripe para processar:
- **Pagamentos únicos** (mensalidades, taxas, etc.)
- **Assinaturas recorrentes** (planos mensais/anuais)
- **Webhooks** para sincronização automática
- **Multi-tenancy** com isolamento por instituição

## ⚙️ Configuração

### 1. Variáveis de Ambiente

Configure as seguintes variáveis no seu arquivo `.env`:

```bash
# Chaves do Stripe
STRIPE_SECRET_KEY=sk_test_51...
STRIPE_PUBLISHABLE_KEY=pk_test_51...
STRIPE_WEBHOOK_SECRET=whsec_...

# Configurações
STRIPE_CURRENCY=BRL
STRIPE_SUCCESS_URL=http://localhost:3000/payment/success
STRIPE_CANCEL_URL=http://localhost:3000/payment/cancel
```

### 2. Obter Chaves do Stripe

1. Acesse [Stripe Dashboard](https://dashboard.stripe.com/)
2. Vá em **Developers > API keys**
3. Copie as chaves **Publishable key** e **Secret key**
4. Para webhooks, vá em **Developers > Webhooks** e crie um endpoint

### 3. Configurar Webhooks

URL do webhook: `https://sua-api.com/api/v1/payment/webhook`

Eventos necessários:
- `checkout.session.completed`
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`

## 🚀 Uso da API

### Pagamentos Únicos

#### 1. Criar Sessão de Pagamento

```http
POST /api/v1/payment/create-session
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "amount": 2999,
  "currency": "BRL",
  "description": "Mensalidade Janeiro 2024",
  "payment_type": "one_time",
  "success_url": "https://myapp.com/success",
  "cancel_url": "https://myapp.com/cancel",
  "metadata": {
    "course_id": "uuid",
    "student_id": "uuid"
  }
}
```

#### Resposta:
```json
{
  "session_id": "cs_1234567890",
  "session_url": "https://checkout.stripe.com/c/pay/cs_1234567890",
  "payment_id": "uuid"
}
```

#### 2. Redirecionar para Checkout

```javascript
// Frontend - redirecionar para session_url
window.location.href = response.session_url;
```

### Assinaturas Recorrentes

#### 1. Criar Planos no Stripe

Primeiro, crie produtos e preços no Stripe Dashboard:

```bash
# Exemplo via Stripe CLI
stripe products create --name="Plano Básico" --description="Plano básico mensal"
stripe prices create --product=prod_xxx --unit-amount=2999 --currency=brl --recurring[interval]=month
```

#### 2. Criar Assinatura

```http
POST /api/v1/payment/subscription
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "plan_id": "price_1234567890",
  "customer_email": "<EMAIL>",
  "customer_name": "João Silva",
  "trial_days": 7,
  "metadata": {
    "department": "vendas"
  }
}
```

#### Resposta:
```json
{
  "subscription_id": "sub_1234567890",
  "customer_id": "cus_1234567890",
  "status": "trialing",
  "current_period_end": "2024-02-01T00:00:00.000Z",
  "trial_end": "2024-01-08T00:00:00.000Z",
  "latest_invoice_url": "https://invoice.stripe.com/..."
}
```

## 🔄 Webhooks

Os webhooks são processados automaticamente para:

### Pagamentos Únicos
- **checkout.session.completed**: Atualiza status para "succeeded"
- **payment_intent.succeeded**: Confirma pagamento
- **payment_intent.payment_failed**: Marca como "failed"

### Assinaturas
- **customer.subscription.created**: Registra nova assinatura
- **customer.subscription.updated**: Atualiza dados da assinatura
- **customer.subscription.deleted**: Marca como cancelada
- **invoice.payment_succeeded**: Ativa assinatura
- **invoice.payment_failed**: Marca como "past_due"

## 🛡️ Segurança

### Multi-Tenancy
- Todos os pagamentos são isolados por `institution_id`
- Usuários só acessam dados da própria instituição
- Validação automática de acesso

### Validação de Webhooks
- Verificação de assinatura Stripe
- Proteção contra replay attacks
- Logs de segurança

## 📊 Monitoramento

### Status de Pagamentos
- `pending`: Aguardando processamento
- `processing`: Em processamento
- `succeeded`: Pagamento aprovado
- `failed`: Pagamento falhou
- `canceled`: Pagamento cancelado

### Status de Assinaturas
- `active`: Assinatura ativa
- `trialing`: Em período de trial
- `past_due`: Pagamento em atraso
- `canceled`: Assinatura cancelada
- `incomplete`: Pagamento incompleto

## 🧪 Testes

### Cartões de Teste

```bash
# Cartão aprovado
****************

# Cartão recusado
****************

# Cartão que requer autenticação
****************
```

### Webhook Testing

```bash
# Instalar Stripe CLI
stripe listen --forward-to localhost:3000/api/v1/payment/webhook

# Simular eventos
stripe trigger payment_intent.succeeded
stripe trigger customer.subscription.created
```

## 🔧 Configuração Avançada

### Métodos de Pagamento Brasileiros

```javascript
// Configurar checkout para aceitar PIX e Boleto
{
  payment_method_types: ['card', 'boleto'],
  // Para PIX, usar payment_method_types: ['pix']
}
```

### Parcelamento

```javascript
// Configurar parcelamento no checkout
{
  payment_method_options: {
    card: {
      installments: {
        enabled: true,
        plan: {
          count: 12,
          interval: 'month',
          type: 'fixed_count'
        }
      }
    }
  }
}
```

## 📈 Relatórios

### Consultar Pagamentos

```http
GET /api/v1/payment/payments
Authorization: Bearer {jwt_token}
```

### Consultar Assinaturas

```http
GET /api/v1/payment/subscriptions
Authorization: Bearer {jwt_token}
```

## 🚨 Tratamento de Erros

### Erros Comuns

```javascript
// Chave inválida
{
  "error": "Invalid API key provided"
}

// Valor inválido
{
  "error": "Amount must be at least 50 cents"
}

// Webhook inválido
{
  "error": "Webhook signature verification failed"
}
```

### Retry Logic

```javascript
// Implementar retry para webhooks
const maxRetries = 3;
let retryCount = 0;

while (retryCount < maxRetries) {
  try {
    await processWebhook(event);
    break;
  } catch (error) {
    retryCount++;
    if (retryCount === maxRetries) {
      throw error;
    }
    await sleep(1000 * retryCount);
  }
}
```

## 📚 Recursos Adicionais

- [Documentação Stripe](https://stripe.com/docs)
- [Stripe CLI](https://stripe.com/docs/stripe-cli)
- [Webhooks Guide](https://stripe.com/docs/webhooks)
- [Testing Guide](https://stripe.com/docs/testing)

## 🆘 Suporte

Para problemas com a integração Stripe:

1. Verifique os logs da aplicação
2. Consulte o Stripe Dashboard
3. Use o Stripe CLI para debug
4. Verifique a configuração de webhooks

## 🔄 Atualizações

Esta integração é compatível com:
- Stripe API Version: 2024-12-18.acacia
- Node.js 18+
- TypeScript 4.9+
