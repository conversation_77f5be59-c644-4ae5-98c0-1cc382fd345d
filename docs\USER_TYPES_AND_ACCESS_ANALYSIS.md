# 🔐 **Análise de Tipos de Usuário e Controle de Acesso - EduSys**

## 📊 **SITUAÇÃO ATUAL DO SISTEMA**

### **✅ Sistema RBAC Já Implementado:**
- ✅ **Tabelas:** `users`, `roles`, `permissions`, `user_roles`, `role_permissions`
- ✅ **JWT Strategy** configurada
- ✅ **Guards:** `JwtAuthGuard` para autenticação
- ✅ **Decorators:** `@CurrentUser()` para obter dados do usuário
- ✅ **Auditoria:** `AuditInterceptor` para campos created_by/updated_by

### **🔍 Estrutura Atual:**
```
User (1) ──→ (N) UserRole ──→ (N) Role ──→ (N) RolePermission ──→ (N) Permission
```

### **📋 Dados Disponíveis no JWT:**
```typescript
interface CurrentUserData {
  sub: string;           // ID do usuário
  email: string;         // Email do usuário
  name: string;          // Nome do usuário
  institution_id?: string; // ID da instituição
}
```

---

## 🎯 **4 TIPOS DE USUÁRIO NECESSÁRIOS**

### **1. 👨‍🎓 ALUNO**
- **Descrição:** Estudante matriculado em cursos
- **Acesso:** Visualizar suas próprias informações, notas, presença e faturas
- **Restrições:** Não pode alterar dados, apenas consultar

### **2. 👨‍🏫 PROFESSOR**
- **Descrição:** Docente responsável por turmas
- **Acesso:** Gerenciar turmas atribuídas, registrar presença e lançar notas
- **Restrições:** Não pode gerenciar matrículas nem acessar financeiro

### **3. 👨‍👩‍👧‍👦 RESPONSÁVEL**
- **Descrição:** Responsável legal por alunos menores de idade
- **Acesso:** Visualizar informações dos alunos sob sua responsabilidade
- **Restrições:** Acesso limitado aos dados dos alunos vinculados

### **4. 👨‍💼 SECRETARIA/ADMINISTRAÇÃO**
- **Descrição:** Funcionários administrativos da instituição
- **Acesso:** Controle total do sistema da instituição
- **Restrições:** Limitado à sua instituição

---

## 🏗️ **PROPOSTA DE IMPLEMENTAÇÃO**

### **Opção 1: Usar Sistema RBAC Existente (RECOMENDADA)**

#### **Vantagens:**
- ✅ **Aproveita estrutura existente**
- ✅ **Flexibilidade total** - permissões granulares
- ✅ **Escalabilidade** - fácil adicionar novos tipos
- ✅ **Já implementado** - apenas configurar roles

#### **Implementação:**
1. **Criar 4 roles padrão** para cada instituição
2. **Definir permissões específicas** para cada role
3. **Atribuir roles aos usuários** via `user_roles`
4. **Criar guards específicos** para validar permissões

---

### **Opção 2: Campo user_type na Tabela Users**

#### **Vantagens:**
- ✅ **Simplicidade** - um campo apenas
- ✅ **Performance** - sem JOINs complexos

#### **Desvantagens:**
- ❌ **Menos flexível** - tipos fixos
- ❌ **Dificulta customização** por instituição
- ❌ **Não aproveita RBAC existente**

---

## 🎯 **IMPLEMENTAÇÃO RECOMENDADA: RBAC**

### **1. Roles Padrão por Instituição:**

```typescript
// Roles que serão criadas automaticamente para cada instituição
const DEFAULT_ROLES = [
  {
    key: 'student',
    name: 'Aluno',
    description: 'Estudante matriculado em cursos'
  },
  {
    key: 'teacher',
    name: 'Professor',
    description: 'Docente responsável por turmas'
  },
  {
    key: 'guardian',
    name: 'Responsável',
    description: 'Responsável legal por alunos'
  },
  {
    key: 'admin',
    name: 'Administração',
    description: 'Funcionário administrativo'
  }
];
```

### **2. Permissões por Módulo:**

#### **📚 Módulo Educacional:**
```typescript
const EDUCATIONAL_PERMISSIONS = [
  // Cursos
  'course.view', 'course.create', 'course.update', 'course.delete',

  // Turmas
  'class.view', 'class.create', 'class.update', 'class.delete',

  // Matrículas
  'enrollment.view', 'enrollment.create', 'enrollment.approve', 'enrollment.reject',
  'enrollment.view.own', // Ver apenas suas próprias matrículas

  // Presença
  'attendance.view', 'attendance.create', 'attendance.update',
  'attendance.view.own', // Ver apenas sua própria presença

  // Notas
  'grade.view', 'grade.create', 'grade.update',
  'grade.view.own' // Ver apenas suas próprias notas
];
```

#### **💰 Módulo Financeiro:**
```typescript
const FINANCIAL_PERMISSIONS = [
  // Transações
  'financial.view', 'financial.create', 'financial.update',

  // Faturas
  'invoice.view', 'invoice.create', 'invoice.update',
  'invoice.view.own', // Ver apenas suas próprias faturas

  // Métodos de pagamento
  'payment_method.view', 'payment_method.create', 'payment_method.update'
];
```

### **3. Matriz de Permissões:**

| Permissão | Aluno | Professor | Responsável | Admin |
|-----------|-------|-----------|-------------|-------|
| `course.view` | ✅ | ✅ | ✅ | ✅ |
| `course.create` | ❌ | ❌ | ❌ | ✅ |
| `enrollment.view.own` | ✅ | ❌ | ✅ | ✅ |
| `enrollment.approve` | ❌ | ❌ | ❌ | ✅ |
| `attendance.create` | ❌ | ✅ | ❌ | ✅ |
| `attendance.view.own` | ✅ | ❌ | ✅ | ✅ |
| `grade.create` | ❌ | ✅ | ❌ | ✅ |
| `grade.view.own` | ✅ | ❌ | ✅ | ✅ |
| `financial.view` | ❌ | ❌ | ❌ | ✅ |
| `invoice.view.own` | ✅ | ❌ | ✅ | ✅ |

---

## 🔧 **IMPLEMENTAÇÃO TÉCNICA**

### **1. Guard de Permissões:**
```typescript
@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly roleRepository: RoleRepository
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.get<string[]>(
      'permissions',
      context.getHandler()
    );

    if (!requiredPermissions) return true;

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    const userPermissions = await this.getUserPermissions(user.sub);

    return requiredPermissions.some(permission =>
      userPermissions.includes(permission)
    );
  }
}
```

### **2. Decorator de Permissões:**
```typescript
export const RequirePermissions = (...permissions: string[]) =>
  SetMetadata('permissions', permissions);
```

### **3. Uso nos Controllers:**
```typescript
@Controller('course')
export class CourseController {
  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.view')
  async getCourses() {
    // Todos podem ver cursos
  }

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.create')
  async createCourse() {
    // Apenas admin pode criar
  }
}
```

### **4. Filtros por Contexto:**
```typescript
// Para permissões .own, filtrar dados por usuário
@Get('my-enrollments')
@UseGuards(JwtAuthGuard, PermissionGuard)
@RequirePermissions('enrollment.view.own')
async getMyEnrollments(@CurrentUser() user: CurrentUserData) {
  return this.enrollmentService.findByStudentId(user.sub);
}
```

---

## 🚀 **PRÓXIMOS PASSOS**

### **1. Criar Migration de Seed:**
- Criar roles padrão para cada instituição
- Definir permissões básicas
- Associar permissões às roles

### **2. Implementar Guards:**
- `PermissionGuard` para validar permissões
- `RoleGuard` para validar roles específicas

### **3. Atualizar Controllers:**
- Adicionar decorators de permissão
- Implementar filtros por contexto

### **4. Criar Use Cases:**
- Atribuir role ao usuário
- Verificar permissões do usuário
- Listar recursos por permissão

---

## 💡 **EXEMPLO PRÁTICO DE IMPLEMENTAÇÃO**

### **1. Migration para Seed de Roles:**
```typescript
// CreateDefaultRolesAndPermissions.ts
export class CreateDefaultRolesAndPermissions implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Inserir permissões básicas
    await queryRunner.query(`
      INSERT INTO core.permissions (id, name, key, description, created_by) VALUES
      ('uuid1', 'Ver Cursos', 'course.view', 'Visualizar cursos disponíveis', 'system'),
      ('uuid2', 'Criar Cursos', 'course.create', 'Criar novos cursos', 'system'),
      ('uuid3', 'Ver Matrículas Próprias', 'enrollment.view.own', 'Ver suas próprias matrículas', 'system'),
      ('uuid4', 'Aprovar Matrículas', 'enrollment.approve', 'Aprovar matrículas pendentes', 'system'),
      ('uuid5', 'Registrar Presença', 'attendance.create', 'Registrar presença dos alunos', 'system'),
      ('uuid6', 'Ver Presença Própria', 'attendance.view.own', 'Ver sua própria presença', 'system'),
      ('uuid7', 'Lançar Notas', 'grade.create', 'Lançar notas dos alunos', 'system'),
      ('uuid8', 'Ver Notas Próprias', 'grade.view.own', 'Ver suas próprias notas', 'system'),
      ('uuid9', 'Ver Financeiro', 'financial.view', 'Acessar módulo financeiro', 'system'),
      ('uuid10', 'Ver Faturas Próprias', 'invoice.view.own', 'Ver suas próprias faturas', 'system');
    `);

    // Criar roles padrão (serão replicadas para cada instituição)
    await queryRunner.query(`
      INSERT INTO core.roles (id, name, key, description, institution_id, created_by) VALUES
      ('role1', 'Aluno', 'student', 'Estudante matriculado', NULL, 'system'),
      ('role2', 'Professor', 'teacher', 'Docente responsável por turmas', NULL, 'system'),
      ('role3', 'Responsável', 'guardian', 'Responsável legal por alunos', NULL, 'system'),
      ('role4', 'Administração', 'admin', 'Funcionário administrativo', NULL, 'system');
    `);

    // Associar permissões às roles
    // ALUNO
    await queryRunner.query(`
      INSERT INTO core.role_permissions (role_id, permission_id, created_by) VALUES
      ('role1', 'uuid1', 'system'), -- course.view
      ('role1', 'uuid3', 'system'), -- enrollment.view.own
      ('role1', 'uuid6', 'system'), -- attendance.view.own
      ('role1', 'uuid8', 'system'), -- grade.view.own
      ('role1', 'uuid10', 'system'); -- invoice.view.own
    `);

    // PROFESSOR
    await queryRunner.query(`
      INSERT INTO core.role_permissions (role_id, permission_id, created_by) VALUES
      ('role2', 'uuid1', 'system'), -- course.view
      ('role2', 'uuid5', 'system'), -- attendance.create
      ('role2', 'uuid7', 'system'); -- grade.create
    `);

    // RESPONSÁVEL
    await queryRunner.query(`
      INSERT INTO core.role_permissions (role_id, permission_id, created_by) VALUES
      ('role3', 'uuid1', 'system'), -- course.view
      ('role3', 'uuid3', 'system'), -- enrollment.view.own
      ('role3', 'uuid6', 'system'), -- attendance.view.own
      ('role3', 'uuid8', 'system'), -- grade.view.own
      ('role3', 'uuid10', 'system'); -- invoice.view.own
    `);

    // ADMIN
    await queryRunner.query(`
      INSERT INTO core.role_permissions (role_id, permission_id, created_by) VALUES
      ('role4', 'uuid1', 'system'), -- course.view
      ('role4', 'uuid2', 'system'), -- course.create
      ('role4', 'uuid3', 'system'), -- enrollment.view.own
      ('role4', 'uuid4', 'system'), -- enrollment.approve
      ('role4', 'uuid5', 'system'), -- attendance.create
      ('role4', 'uuid6', 'system'), -- attendance.view.own
      ('role4', 'uuid7', 'system'), -- grade.create
      ('role4', 'uuid8', 'system'), -- grade.view.own
      ('role4', 'uuid9', 'system'), -- financial.view
      ('role4', 'uuid10', 'system'); -- invoice.view.own
    `);
  }
}
```

### **2. Service para Verificar Permissões:**
```typescript
@Injectable()
export class PermissionService {
  constructor(
    private readonly userRepository: UserRepository
  ) {}

  async getUserPermissions(userId: string): Promise<string[]> {
    const query = `
      SELECT DISTINCT p.key
      FROM core.permissions p
      JOIN core.role_permissions rp ON p.id = rp.permission_id
      JOIN core.roles r ON rp.role_id = r.id
      JOIN core.user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = $1
    `;

    const result = await this.userRepository.query(query, [userId]);
    return result.map(row => row.key);
  }

  async hasPermission(userId: string, permission: string): Promise<boolean> {
    const permissions = await this.getUserPermissions(userId);
    return permissions.includes(permission);
  }

  async getUserRole(userId: string): Promise<string | null> {
    const query = `
      SELECT r.key
      FROM core.roles r
      JOIN core.user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = $1
      LIMIT 1
    `;

    const result = await this.userRepository.query(query, [userId]);
    return result[0]?.key || null;
  }
}
```

### **3. Atualizar CurrentUser para incluir Role:**
```typescript
// Atualizar JWT Strategy
async validate(payload: any) {
  const user = await this.userRepository.findById(payload.sub);
  const userRole = await this.permissionService.getUserRole(payload.sub);

  return {
    sub: user!.id,
    email: user!.email,
    name: user!.name,
    institution_id: user!.institution_id,
    role: userRole // 🆕 Adicionar role
  };
}

// Atualizar interface
export interface CurrentUserData {
  sub: string;
  email: string;
  name: string;
  institution_id?: string;
  role?: string; // 🆕 Adicionar role
}
```

### **4. Exemplo de Uso nos Controllers:**
```typescript
@Controller('enrollment')
export class EnrollmentController {
  @Get('my-enrollments')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.view.own')
  async getMyEnrollments(@CurrentUser() user: CurrentUserData) {
    // Alunos veem suas matrículas
    // Responsáveis veem matrículas dos filhos
    // Admin vê todas da instituição

    if (user.role === 'student') {
      return this.enrollmentService.findByStudentId(user.sub);
    }

    if (user.role === 'guardian') {
      return this.enrollmentService.findByGuardianId(user.sub);
    }

    if (user.role === 'admin') {
      return this.enrollmentService.findByInstitutionId(user.institution_id);
    }
  }

  @Put(':id/approve')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.approve')
  async approveEnrollment(@Param('id') id: string) {
    // Apenas admin pode aprovar
    return this.enrollmentService.approve(id);
  }
}
```

---

**🎯 RESULTADO: Sistema flexível que permite controle granular de acesso mantendo a estrutura RBAC existente!**
