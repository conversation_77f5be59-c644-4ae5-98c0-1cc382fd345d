NODE_ENV=local
PORT=3001

DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USER=paraibas
DATABASE_PASSWORD=paraiba123
DATABASE_NAME=edusys
DATABASE_SYNCHRONIZE=false

JWT_SECRET=74YLbq4%c!wU
JWT_EXPIRATION_TIME=1800
JWT_REFRESH_TOKEN_SECRET=7jML9q4-c!s0
JWT_REFRESH_TOKEN_EXPIRATION_TIME=86400

SMTP_HOST=smtp-relay.brevo.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_FROM=<EMAIL>
SMTP_PASSWORD=Mt4Jm0pWFyVGsNRK

FIRST_LOGIN_URL=https://teste.com.br

# ==============================================
# STRIPE CONFIGURATION
# ==============================================
# Chaves do Stripe (obtenha em https://dashboard.stripe.com/apikeys)
STRIPE_SECRET_KEY=sk_test_51...
STRIPE_PUBLISHABLE_KEY=pk_test_51...

# Webhook secret (obtenha em https://dashboard.stripe.com/webhooks)
STRIPE_WEBHOOK_SECRET=whsec_...

# Configurações de pagamento
STRIPE_CURRENCY=BRL
STRIPE_SUCCESS_URL=http://localhost:3000/payment/success
STRIPE_CANCEL_URL=http://localhost:3000/payment/cancel

# ==============================================
# REDIS CONFIGURATION (OPCIONAL)
# ==============================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0