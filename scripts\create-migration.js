const { execSync } = require('child_process');
const path = require('path');

const migrationName = process.argv[2];

if (!migrationName) {
  console.error('❌ Erro: Por favor, forneça um nome para a migration.');
  console.log('✅ Exemplo: yarn create:migration NomeDaMigration');
  process.exit(1);
}

const migrationPath = path.join('database', 'migrations', migrationName);

const command = `yarn typeorm migration:create ${migrationPath}`;

try {
  console.log(`🚀 Executando: ${command}`);
  execSync(command, { stdio: 'inherit' });
  console.log(`✅ Migration '${migrationName}' criada com sucesso!`);
} catch (error) {
  console.error(`❌ <PERSON>alha ao criar a migration.`);
  process.exit(1);
}