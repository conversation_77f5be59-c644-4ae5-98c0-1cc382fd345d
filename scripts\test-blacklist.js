#!/usr/bin/env node

/**
 * Script de Teste Automatizado - Sistema Blacklist Redis
 *
 * Este script testa automaticamente o sistema de blacklist de tokens
 * implementado no EduSys API.
 *
 * Uso: node scripts/test-blacklist.js
 */

const axios = require('axios');
const Redis = require('ioredis');

// Configurações
const API_BASE_URL = process.env.API_URL || 'http://localhost:3000';
const REDIS_HOST = process.env.REDIS_HOST || 'localhost';
const REDIS_PORT = process.env.REDIS_PORT || 6379;

// Cliente Redis para verificações
const redis = new Redis({
  host: REDIS_HOST,
  port: REDIS_PORT,
  retryDelayOnFailover: 100,
  lazyConnect: true
});

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Utilitários
const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const logSuccess = (message) => log(`✅ ${message}`, 'green');
const logError = (message) => log(`❌ ${message}`, 'red');
const logWarning = (message) => log(`⚠️  ${message}`, 'yellow');
const logInfo = (message) => log(`ℹ️  ${message}`, 'blue');
const logHeader = (message) => log(`\n${colors.bold}🧪 ${message}${colors.reset}`, 'blue');

// Dados de teste
const testUser = {
  email: '<EMAIL>',
  password: 'admin123'
};

let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

// Função para fazer requisições HTTP
async function makeRequest(method, endpoint, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500
    };
  }
}

// Função para verificar chave no Redis
async function checkRedisKey(key) {
  try {
    const exists = await redis.get(key);
    const ttl = await redis.ttl(key);
    return { exists: exists !== null, value: exists, ttl };
  } catch (error) {
    logError(`Erro ao verificar Redis: ${error.message}`);
    return { exists: false, value: null, ttl: -1 };
  }
}

// Função para executar teste
async function runTest(testName, testFunction) {
  testResults.total++;
  logInfo(`Executando: ${testName}`);

  try {
    await testFunction();
    testResults.passed++;
    logSuccess(`PASSOU: ${testName}`);
  } catch (error) {
    testResults.failed++;
    testResults.errors.push({ test: testName, error: error.message });
    logError(`FALHOU: ${testName} - ${error.message}`);
  }
}

// Testes específicos
async function testLogin() {
  const result = await makeRequest('POST', '/auth/login', testUser);

  if (!result.success) {
    throw new Error(`Login falhou: ${result.error}`);
  }

  if (!result.data.accessToken || !result.data.refreshToken) {
    throw new Error('Tokens não retornados no login');
  }

  return {
    accessToken: result.data.accessToken,
    refreshToken: result.data.refreshToken,
    user: result.data.user
  };
}

async function testLogoutBlacklist() {
  // Fazer login
  const loginData = await testLogin();

  // Fazer logout
  const logoutResult = await makeRequest('POST', '/auth/logout', null, {
    'Authorization': `Bearer ${loginData.accessToken}`
  });

  if (!logoutResult.success) {
    throw new Error(`Logout falhou: ${logoutResult.error}`);
  }

  // Verificar se token foi para blacklist
  const redisCheck = await checkRedisKey(`blacklist:${loginData.accessToken}`);

  if (!redisCheck.exists) {
    throw new Error('Access token não foi adicionado à blacklist');
  }

  if (redisCheck.ttl <= 0) {
    throw new Error('TTL do token na blacklist é inválido');
  }

  logInfo(`Token na blacklist com TTL: ${redisCheck.ttl}s`);
}

async function testTokenAfterLogout() {
  // Fazer login
  const loginData = await testLogin();

  // Fazer logout
  await makeRequest('POST', '/auth/logout', null, {
    'Authorization': `Bearer ${loginData.accessToken}`
  });

  // Tentar usar token após logout
  const result = await makeRequest('GET', '/user', null, {
    'Authorization': `Bearer ${loginData.accessToken}`
  });

  if (result.success) {
    throw new Error('Token funcionou após logout (deveria falhar)');
  }

  if (result.status !== 401) {
    throw new Error(`Status esperado 401, recebido: ${result.status}`);
  }
}

async function testRefreshTokenReuse() {
  // Fazer login
  const loginData = await testLogin();

  // Fazer primeiro refresh
  const firstRefresh = await makeRequest('POST', '/auth/refresh', {
    refreshToken: loginData.refreshToken
  });

  if (!firstRefresh.success) {
    throw new Error(`Primeiro refresh falhou: ${firstRefresh.error}`);
  }

  // Verificar se refresh token NÃO foi para blacklist
  const redisCheck = await checkRedisKey(`blacklist:${loginData.refreshToken}`);

  if (redisCheck.exists) {
    throw new Error('Refresh token foi adicionado à blacklist (não deveria)');
  }

  // Verificar se retornou o mesmo refresh token
  if (firstRefresh.data.refreshToken !== loginData.refreshToken) {
    throw new Error('Refresh token retornado é diferente do original');
  }

  // Tentar usar refresh token novamente (deve funcionar)
  const secondRefresh = await makeRequest('POST', '/auth/refresh', {
    refreshToken: loginData.refreshToken
  });

  if (!secondRefresh.success) {
    throw new Error('Segundo refresh falhou (deveria funcionar)');
  }

  // Verificar se access tokens são diferentes
  if (firstRefresh.data.accessToken === secondRefresh.data.accessToken) {
    throw new Error('Access tokens são iguais (deveriam ser diferentes)');
  }

  logInfo(`Refresh token reutilizado com sucesso`);
}

async function testRefreshTokenInBlacklist() {
  // Fazer login
  const loginData = await testLogin();

  // Fazer logout (coloca refresh token na blacklist)
  await makeRequest('POST', '/auth/logout', null, {
    'Authorization': `Bearer ${loginData.accessToken}`
  });

  // Tentar fazer refresh com token na blacklist
  const refreshResult = await makeRequest('POST', '/auth/refresh', {
    refreshToken: loginData.refreshToken
  });

  if (refreshResult.success) {
    throw new Error('Refresh com token na blacklist funcionou (deveria falhar)');
  }

  if (refreshResult.status !== 401) {
    throw new Error(`Status esperado 401, recebido: ${refreshResult.status}`);
  }
}

async function testMultipleTokensBlacklist() {
  // Fazer múltiplos logins
  const login1 = await testLogin();
  const login2 = await testLogin();

  // Fazer logout com ambos os tokens
  await makeRequest('POST', '/auth/logout', null, {
    'Authorization': `Bearer ${login1.accessToken}`
  });

  await makeRequest('POST', '/auth/logout', null, {
    'Authorization': `Bearer ${login2.accessToken}`
  });

  // Verificar se ambos estão na blacklist
  const check1 = await checkRedisKey(`blacklist:${login1.accessToken}`);
  const check2 = await checkRedisKey(`blacklist:${login2.accessToken}`);

  if (!check1.exists || !check2.exists) {
    throw new Error('Nem todos os tokens foram adicionados à blacklist');
  }

  // Verificar quantidade de chaves no Redis
  const keys = await redis.keys('blacklist:*');
  if (keys.length < 4) { // 2 access + 2 refresh tokens
    throw new Error(`Esperado pelo menos 4 chaves, encontrado: ${keys.length}`);
  }

  logInfo(`Total de chaves na blacklist: ${keys.length}`);
}

async function testRedisConnection() {
  try {
    const pong = await redis.ping();
    if (pong !== 'PONG') {
      throw new Error('Redis não respondeu corretamente');
    }

    const info = await redis.info('memory');
    logInfo('Conexão Redis OK');
  } catch (error) {
    throw new Error(`Falha na conexão Redis: ${error.message}`);
  }
}

// Função principal
async function runAllTests() {
  logHeader('INICIANDO TESTES DO SISTEMA BLACKLIST');

  logInfo(`API Base URL: ${API_BASE_URL}`);
  logInfo(`Redis: ${REDIS_HOST}:${REDIS_PORT}`);

  // Limpar blacklist antes dos testes
  try {
    const keys = await redis.keys('blacklist:*');
    if (keys.length > 0) {
      await redis.del(...keys);
      logInfo(`Limpou ${keys.length} chaves antigas da blacklist`);
    }
  } catch (error) {
    logWarning('Não foi possível limpar blacklist antiga');
  }

  // Executar testes
  await runTest('Conexão Redis', testRedisConnection);
  await runTest('Login básico', testLogin);
  await runTest('Logout adiciona token à blacklist', testLogoutBlacklist);
  await runTest('Token não funciona após logout', testTokenAfterLogout);
  await runTest('Refresh token pode ser reutilizado', testRefreshTokenReuse);
  await runTest('Refresh token na blacklist falha', testRefreshTokenInBlacklist);
  await runTest('Múltiplos tokens na blacklist', testMultipleTokensBlacklist);

  // Relatório final
  logHeader('RELATÓRIO FINAL');

  logInfo(`Total de testes: ${testResults.total}`);
  logSuccess(`Testes passaram: ${testResults.passed}`);

  if (testResults.failed > 0) {
    logError(`Testes falharam: ${testResults.failed}`);

    logHeader('ERROS ENCONTRADOS:');
    testResults.errors.forEach((error, index) => {
      logError(`${index + 1}. ${error.test}: ${error.error}`);
    });
  } else {
    logSuccess('🎉 TODOS OS TESTES PASSARAM!');
    logSuccess('Sistema de blacklist está funcionando corretamente!');
  }

  // Estatísticas finais do Redis
  try {
    const keys = await redis.keys('blacklist:*');
    logInfo(`Chaves finais na blacklist: ${keys.length}`);

    if (keys.length > 0) {
      logInfo('Exemplos de chaves:');
      for (let i = 0; i < Math.min(3, keys.length); i++) {
        const ttl = await redis.ttl(keys[i]);
        logInfo(`  ${keys[i].substring(0, 50)}... (TTL: ${ttl}s)`);
      }
    }
  } catch (error) {
    logWarning('Não foi possível obter estatísticas finais do Redis');
  }

  // Fechar conexão Redis
  await redis.disconnect();

  // Exit code baseado nos resultados
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Executar testes
if (require.main === module) {
  runAllTests().catch((error) => {
    logError(`Erro fatal: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  testResults
};
