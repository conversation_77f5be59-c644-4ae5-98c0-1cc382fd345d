import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { EnvironmentConfigModule } from './infrastructure/config/environment-config/environment-config.module';
import { ControllersModule } from './infrastructure/controllers/controllers.module';
import { ExceptionsModule } from './infrastructure/exceptions/exceptions.module';
import { LoggerModule } from './infrastructure/logger/logger.module';
import { RepositoriesModule } from './infrastructure/repositories/repositories.module';
import { TwoFactorAuthenticationService } from './infrastructure/services/2fa/two-factor-authentication.service';
import { TwoFactorAutheticationModule } from './infrastructure/services/2fa/two-factor-authetication.module';
import { BcryptModule } from './infrastructure/services/bcrypt/bcrypt.module';
import { EmailModule } from './infrastructure/services/email/email.module';
import { JwtModule as JwtServiceModule } from './infrastructure/services/jwt/jwt.module';
import { UsecasesProxyModule } from './infrastructure/usecases-proxy/usecases-proxy.module';

@Module({
  imports: [
    EmailModule,
    TwoFactorAutheticationModule,
    PassportModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: `${process.env.JWT_EXPIRATION_TIME}s` }
    }),
    LoggerModule,
    ExceptionsModule,
    RepositoriesModule,
    UsecasesProxyModule,
    ControllersModule,
    BcryptModule,
    JwtServiceModule,
    EnvironmentConfigModule
  ],
  providers: [
    TwoFactorAuthenticationService,
    JwtStrategy,
    LocalStrategy,
    JwtRefreshTokenStrategy
  ]
})
export class AppModule {}
