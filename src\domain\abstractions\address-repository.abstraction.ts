import { AddressEntity } from '../entities/address.entity';

export interface IAddressRepository {
  insert(user: Partial<AddressEntity>): Promise<AddressEntity>;
  findAll(): Promise<AddressEntity[]>;
  findById(id: string): Promise<AddressEntity | null>;
  findByZipCode(zip_code: string): Promise<AddressEntity | null>;
  update(id: string, user: AddressEntity): Promise<AddressEntity>;
  delete(id: string): Promise<void>;
}
