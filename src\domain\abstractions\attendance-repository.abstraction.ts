import { AttendanceEntity } from '../entities/attendance.entity';

export abstract class AttendanceRepository {
  abstract create(
    attendanceData: Partial<AttendanceEntity>
  ): Promise<AttendanceEntity>;
  abstract findAll(): Promise<AttendanceEntity[]>;
  abstract findAllByInstitution(institutionId: string): Promise<AttendanceEntity[]>;
  abstract findById(id: string): Promise<AttendanceEntity>;
  abstract findByIdAndInstitution(id: string, institutionId: string): Promise<AttendanceEntity>;
  abstract findByEnrollmentId(
    enrollmentId: string
  ): Promise<AttendanceEntity[]>;
  abstract findByClassDate(classDate: Date): Promise<AttendanceEntity[]>;
  abstract findByEnrollmentAndDate(
    enrollmentId: string,
    classDate: Date
  ): Promise<AttendanceEntity | null>;
  abstract findByStatus(status: string): Promise<AttendanceEntity[]>;
  abstract findByDateRange(
    startDate: Date,
    endDate: Date
  ): Promise<AttendanceEntity[]>;
  abstract findByEnrollmentAndDateRange(
    enrollmentId: string,
    startDate: Date,
    endDate: Date
  ): Promise<AttendanceEntity[]>;
  abstract update(
    id: string,
    attendanceData: Partial<AttendanceEntity>
  ): Promise<AttendanceEntity>;
  abstract delete(id: string): Promise<void>;
  abstract getAttendanceStats(enrollmentId: string): Promise<{
    total: number;
    present: number;
    absent: number;
    late: number;
    excused: number;
    attendanceRate: number;
  }>;
}
