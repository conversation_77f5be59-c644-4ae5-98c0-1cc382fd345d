import { ClassEntity } from '../entities/class.entity';

export abstract class ClassRepository {
  abstract create(classData: Partial<ClassEntity>): Promise<ClassEntity>;
  abstract findAll(): Promise<ClassEntity[]>;
  abstract findAllByInstitution(institutionId: string): Promise<ClassEntity[]>;
  abstract findById(id: string): Promise<ClassEntity>;
  abstract findByIdAndInstitution(id: string, institutionId: string): Promise<ClassEntity>;
  abstract findByCourseId(courseId: string): Promise<ClassEntity[]>;
  abstract findByCourseIdAndInstitution(courseId: string, institutionId: string): Promise<ClassEntity[]>;
  abstract findByStatus(status: string): Promise<ClassEntity[]>;
  abstract findAvailableClasses(): Promise<ClassEntity[]>;
  abstract update(
    id: string,
    classData: Partial<ClassEntity>
  ): Promise<ClassEntity>;
  abstract delete(id: string): Promise<void>;
  abstract incrementStudentCount(id: string): Promise<ClassEntity>;
  abstract decrementStudentCount(id: string): Promise<ClassEntity>;
}
