import { CourseEntity } from '../entities/course.entity';

export abstract class CourseRepository {
  abstract create(course: Partial<CourseEntity>): Promise<CourseEntity>;
  abstract findAll(): Promise<CourseEntity[]>;
  abstract findById(id: string): Promise<CourseEntity>;
  abstract findByInstitutionId(institutionId: string): Promise<CourseEntity[]>;
  abstract findByName(name: string): Promise<CourseEntity>;
  abstract findByStatus(status: string): Promise<CourseEntity[]>;
  abstract update(
    id: string,
    course: Partial<CourseEntity>
  ): Promise<CourseEntity>;
  abstract delete(id: string): Promise<void>;
}
