import { EnrollmentEntity } from '../entities/enrollment.entity';

export abstract class EnrollmentRepository {
  abstract create(
    enrollmentData: Partial<EnrollmentEntity>
  ): Promise<EnrollmentEntity>;
  abstract findAll(): Promise<EnrollmentEntity[]>;
  abstract findById(id: string): Promise<EnrollmentEntity>;
  abstract findByStudentId(studentId: string): Promise<EnrollmentEntity[]>;
  abstract findByClassId(classId: string): Promise<EnrollmentEntity[]>;
  abstract findByInstitutionId(
    institutionId: string
  ): Promise<EnrollmentEntity[]>;
  abstract findByStatus(status: string): Promise<EnrollmentEntity[]>;
  abstract findPendingEnrollments(): Promise<EnrollmentEntity[]>;
  abstract findActiveEnrollments(): Promise<EnrollmentEntity[]>;
  abstract findStudentEnrollmentInClass(
    studentId: string,
    classId: string
  ): Promise<EnrollmentEntity | null>;
  abstract update(
    id: string,
    enrollmentData: Partial<EnrollmentEntity>
  ): Promise<EnrollmentEntity>;
  abstract delete(id: string): Promise<void>;
  abstract approveEnrollment(
    id: string,
    approvedBy: string,
    notes?: string
  ): Promise<EnrollmentEntity>;
  abstract rejectEnrollment(
    id: string,
    rejectionReason: string,
    updatedBy: string
  ): Promise<EnrollmentEntity>;
  abstract cancelEnrollment(
    id: string,
    updatedBy: string,
    notes?: string
  ): Promise<EnrollmentEntity>;
}
