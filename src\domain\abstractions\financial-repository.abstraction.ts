import {
  FinancialTransactionEntity,
  InvoiceEntity,
  PaymentMethodEntity
} from '../entities/payment.entity';

export abstract class FinancialTransactionRepository {
  abstract create(
    transactionData: Partial<FinancialTransactionEntity>
  ): Promise<FinancialTransactionEntity>;
  abstract findAll(): Promise<FinancialTransactionEntity[]>;
  abstract findAllByInstitution(
    institutionId: string
  ): Promise<FinancialTransactionEntity[]>;
  abstract findById(id: string): Promise<FinancialTransactionEntity>;
  abstract findByIdAndInstitution(
    id: string,
    institutionId: string
  ): Promise<FinancialTransactionEntity>;
  abstract findByType(type: string): Promise<FinancialTransactionEntity[]>;
  abstract findByTypeAndInstitution(
    type: string,
    institutionId: string
  ): Promise<FinancialTransactionEntity[]>;
  abstract findByCategory(
    category: string
  ): Promise<FinancialTransactionEntity[]>;
  abstract findByCategoryAndInstitution(
    category: string,
    institutionId: string
  ): Promise<FinancialTransactionEntity[]>;
  abstract findByStatus(status: string): Promise<FinancialTransactionEntity[]>;
  abstract findByStatusAndInstitution(
    status: string,
    institutionId: string
  ): Promise<FinancialTransactionEntity[]>;
  abstract findByReference(
    referenceId: string,
    referenceType: string
  ): Promise<FinancialTransactionEntity[]>;
  abstract findByReferenceAndInstitution(
    referenceId: string,
    referenceType: string,
    institutionId: string
  ): Promise<FinancialTransactionEntity[]>;
  abstract findByDateRange(
    startDate: Date,
    endDate: Date
  ): Promise<FinancialTransactionEntity[]>;
  abstract findByDateRangeAndInstitution(
    startDate: Date,
    endDate: Date,
    institutionId: string
  ): Promise<FinancialTransactionEntity[]>;
  abstract findByStripePaymentIntentId(
    stripePaymentIntentId: string
  ): Promise<FinancialTransactionEntity | null>;
  abstract findByStripeSessionId(
    stripeSessionId: string
  ): Promise<FinancialTransactionEntity | null>;
  abstract update(
    id: string,
    transactionData: Partial<FinancialTransactionEntity>
  ): Promise<FinancialTransactionEntity>;
  abstract delete(id: string): Promise<void>;
  abstract getFinancialSummary(
    institutionId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalIncome: number;
    totalExpense: number;
    balance: number;
    transactionCount: number;
  }>;
}

export abstract class InvoiceRepository {
  abstract create(invoiceData: Partial<InvoiceEntity>): Promise<InvoiceEntity>;
  abstract findAll(): Promise<InvoiceEntity[]>;
  abstract findAllByInstitution(
    institutionId: string
  ): Promise<InvoiceEntity[]>;
  abstract findById(id: string): Promise<InvoiceEntity>;
  abstract findByIdAndInstitution(
    id: string,
    institutionId: string
  ): Promise<InvoiceEntity>;
  abstract findByEnrollmentId(enrollmentId: string): Promise<InvoiceEntity[]>;
  abstract findByEnrollmentIdAndInstitution(
    enrollmentId: string,
    institutionId: string
  ): Promise<InvoiceEntity[]>;
  abstract findByInvoiceNumber(
    invoiceNumber: string
  ): Promise<InvoiceEntity | null>;
  abstract findByStatus(status: string): Promise<InvoiceEntity[]>;
  abstract findByStatusAndInstitution(
    status: string,
    institutionId: string
  ): Promise<InvoiceEntity[]>;
  abstract findOverdueInvoices(): Promise<InvoiceEntity[]>;
  abstract findOverdueInvoicesByInstitution(
    institutionId: string
  ): Promise<InvoiceEntity[]>;
  abstract findByDueDateRange(
    startDate: Date,
    endDate: Date
  ): Promise<InvoiceEntity[]>;
  abstract findByDueDateRangeAndInstitution(
    startDate: Date,
    endDate: Date,
    institutionId: string
  ): Promise<InvoiceEntity[]>;
  abstract findByStripeInvoiceId(
    stripeInvoiceId: string
  ): Promise<InvoiceEntity | null>;
  abstract findByStripePaymentIntentId(
    stripePaymentIntentId: string
  ): Promise<InvoiceEntity | null>;
  abstract update(
    id: string,
    invoiceData: Partial<InvoiceEntity>
  ): Promise<InvoiceEntity>;
  abstract delete(id: string): Promise<void>;
  abstract generateInvoiceNumber(institutionId: string): Promise<string>;
  abstract markAsPaid(
    id: string,
    paymentDate: Date,
    paymentMethodId?: string
  ): Promise<InvoiceEntity>;
  abstract markAsOverdue(id: string): Promise<InvoiceEntity>;
}

export abstract class PaymentMethodRepository {
  abstract create(
    paymentMethodData: Partial<PaymentMethodEntity>
  ): Promise<PaymentMethodEntity>;
  abstract findAll(): Promise<PaymentMethodEntity[]>;
  abstract findAllByInstitution(
    institutionId: string
  ): Promise<PaymentMethodEntity[]>;
  abstract findById(id: string): Promise<PaymentMethodEntity>;
  abstract findByIdAndInstitution(
    id: string,
    institutionId: string
  ): Promise<PaymentMethodEntity>;
  abstract findByType(type: string): Promise<PaymentMethodEntity[]>;
  abstract findByTypeAndInstitution(
    type: string,
    institutionId: string
  ): Promise<PaymentMethodEntity[]>;
  abstract findActiveByInstitution(
    institutionId: string
  ): Promise<PaymentMethodEntity[]>;
  abstract findByStripePaymentMethodId(
    stripePaymentMethodId: string
  ): Promise<PaymentMethodEntity | null>;
  abstract findByStripeCustomerId(
    stripeCustomerId: string
  ): Promise<PaymentMethodEntity[]>;
  abstract findDefaultByInstitution(
    institutionId: string
  ): Promise<PaymentMethodEntity | null>;
  abstract update(
    id: string,
    paymentMethodData: Partial<PaymentMethodEntity>
  ): Promise<PaymentMethodEntity>;
  abstract delete(id: string): Promise<void>;
  abstract setAsDefault(
    id: string,
    institutionId: string
  ): Promise<PaymentMethodEntity>;
  abstract activate(id: string): Promise<PaymentMethodEntity>;
  abstract deactivate(id: string): Promise<PaymentMethodEntity>;
}
