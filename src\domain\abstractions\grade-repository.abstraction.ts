import { GradeEntity } from '../entities/grade.entity';

export abstract class GradeRepository {
  abstract create(gradeData: Partial<GradeEntity>): Promise<GradeEntity>;
  abstract findAll(): Promise<GradeEntity[]>;
  abstract findById(id: string): Promise<GradeEntity>;
  abstract findByEnrollmentId(enrollmentId: string): Promise<GradeEntity[]>;
  abstract findByAssessmentType(assessmentType: string): Promise<GradeEntity[]>;
  abstract findByEnrollmentAndAssessmentType(
    enrollmentId: string,
    assessmentType: string
  ): Promise<GradeEntity[]>;
  abstract findByDateRange(
    startDate: Date,
    endDate: Date
  ): Promise<GradeEntity[]>;
  abstract findByEnrollmentAndDateRange(
    enrollmentId: string,
    startDate: Date,
    endDate: Date
  ): Promise<GradeEntity[]>;
  abstract update(
    id: string,
    gradeData: Partial<GradeEntity>
  ): Promise<GradeEntity>;
  abstract delete(id: string): Promise<void>;
  abstract calculateWeightedAverage(enrollmentId: string): Promise<{
    weightedAverage: number;
    totalWeight: number;
    gradeCount: number;
  }>;
  abstract getGradesByAssessmentType(enrollmentId: string): Promise<
    {
      assessmentType: string;
      grades: GradeEntity[];
      average: number;
      totalWeight: number;
    }[]
  >;
}
