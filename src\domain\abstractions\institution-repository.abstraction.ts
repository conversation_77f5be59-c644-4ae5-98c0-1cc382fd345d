import { InstitutionEntity } from '../entities/institution.entity';

export interface IInstitutionRepository {
  insert(user: Partial<InstitutionEntity>): Promise<InstitutionEntity>;
  findAll(): Promise<InstitutionEntity[]>;
  findById(id: string): Promise<InstitutionEntity | null>;
  findByTaxId(tax_id: string): Promise<InstitutionEntity | null>;
  update(id: string, user: InstitutionEntity): Promise<InstitutionEntity>;
  delete(id: string): Promise<void>;
}
