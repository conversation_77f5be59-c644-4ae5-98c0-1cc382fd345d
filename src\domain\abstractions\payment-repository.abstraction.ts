import { FinancialTransactionEntity, PaymentMethodEntity, SubscriptionEntity } from '../entities/payment.entity';

export abstract class PaymentRepository {
  abstract create(paymentData: Partial<FinancialTransactionEntity>): Promise<FinancialTransactionEntity>;
  abstract findAll(): Promise<FinancialTransactionEntity[]>;
  abstract findAllByInstitution(institutionId: string): Promise<FinancialTransactionEntity[]>;
  abstract findById(id: string): Promise<FinancialTransactionEntity>;
  abstract findByIdAndInstitution(id: string, institutionId: string): Promise<FinancialTransactionEntity>;
  abstract findByStripePaymentIntentId(stripePaymentIntentId: string): Promise<FinancialTransactionEntity | null>;
  abstract findByStripeSessionId(stripeSessionId: string): Promise<FinancialTransactionEntity | null>;
  abstract findByStatus(status: string): Promise<FinancialTransactionEntity[]>;
  abstract findByInstitutionAndStatus(institutionId: string, status: string): Promise<FinancialTransactionEntity[]>;
  abstract update(id: string, paymentData: Partial<FinancialTransactionEntity>): Promise<FinancialTransactionEntity>;
  abstract delete(id: string): Promise<void>;
}

export abstract class SubscriptionRepository {
  abstract create(subscriptionData: Partial<SubscriptionEntity>): Promise<SubscriptionEntity>;
  abstract findAll(): Promise<SubscriptionEntity[]>;
  abstract findAllByInstitution(institutionId: string): Promise<SubscriptionEntity[]>;
  abstract findById(id: string): Promise<SubscriptionEntity>;
  abstract findByIdAndInstitution(id: string, institutionId: string): Promise<SubscriptionEntity>;
  abstract findByStripeSubscriptionId(stripeSubscriptionId: string): Promise<SubscriptionEntity | null>;
  abstract findByStripeCustomerId(stripeCustomerId: string): Promise<SubscriptionEntity[]>;
  abstract findActiveByInstitution(institutionId: string): Promise<SubscriptionEntity | null>;
  abstract findByStatus(status: string): Promise<SubscriptionEntity[]>;
  abstract update(id: string, subscriptionData: Partial<SubscriptionEntity>): Promise<SubscriptionEntity>;
  abstract delete(id: string): Promise<void>;
}

export abstract class PaymentMethodRepository {
  abstract create(paymentMethodData: Partial<PaymentMethodEntity>): Promise<PaymentMethodEntity>;
  abstract findAll(): Promise<PaymentMethodEntity[]>;
  abstract findAllByInstitution(institutionId: string): Promise<PaymentMethodEntity[]>;
  abstract findById(id: string): Promise<PaymentMethodEntity>;
  abstract findByIdAndInstitution(id: string, institutionId: string): Promise<PaymentMethodEntity>;
  abstract findByStripePaymentMethodId(stripePaymentMethodId: string): Promise<PaymentMethodEntity | null>;
  abstract findByStripeCustomerId(stripeCustomerId: string): Promise<PaymentMethodEntity[]>;
  abstract findDefaultByInstitution(institutionId: string): Promise<PaymentMethodEntity | null>;
  abstract update(id: string, paymentMethodData: Partial<PaymentMethodEntity>): Promise<PaymentMethodEntity>;
  abstract delete(id: string): Promise<void>;
}
