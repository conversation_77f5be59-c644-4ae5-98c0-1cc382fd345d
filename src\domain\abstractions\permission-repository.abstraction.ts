import { PermissionEntity } from '../entities/permission.entity';

export interface IPermissionRepository {
  insert(permission: Partial<PermissionEntity>): Promise<PermissionEntity>;
  findAll(): Promise<PermissionEntity[]>;
  findById(id: string): Promise<PermissionEntity | null>;
  findBy<PERSON><PERSON>(key: string): Promise<PermissionEntity | null>;
  update(id: string, user: PermissionEntity): Promise<PermissionEntity>;
  delete(id: string): Promise<void>;
}
