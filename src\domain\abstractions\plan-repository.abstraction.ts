import { PlanEntity } from '../entities/plan.entity';

export interface IPlanRepository {
  insert(plan: Partial<PlanEntity>): Promise<PlanEntity>;
  findAll(): Promise<PlanEntity[]>;
  findById(id: string): Promise<PlanEntity | null>;
  findByName(name: string): Promise<PlanEntity | null>;
  findActive(): Promise<PlanEntity[]>;
  findByBillingPeriod(billingPeriod: string): Promise<PlanEntity[]>;
  update(id: string, plan: PlanEntity): Promise<PlanEntity>;
  delete(id: string): Promise<void>;
}
