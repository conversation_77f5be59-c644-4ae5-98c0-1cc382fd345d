import { RolePermissionEntity } from '../entities/role_permissions.entity';

export interface IRolePermissionRepository {
  insert(
    rolePermission: Partial<RolePermissionEntity>
  ): Promise<RolePermissionEntity>;
  findAll(): Promise<RolePermissionEntity[]>;
  findById(id: string): Promise<RolePermissionEntity | null>;
  update(id: string, user: RolePermissionEntity): Promise<RolePermissionEntity>;
  delete(id: string): Promise<void>;
}
