import { SubscriptionEntity } from '../entities/subscription.entity';

export interface ISubscriptionRepository {
  insert(
    subscription: Partial<SubscriptionEntity>
  ): Promise<SubscriptionEntity>;
  findAll(): Promise<SubscriptionEntity[]>;
  findById(id: string): Promise<SubscriptionEntity | null>;
  findByInstitutionId(institutionId: string): Promise<SubscriptionEntity[]>;
  findActiveByInstitutionId(
    institutionId: string
  ): Promise<SubscriptionEntity | null>;
  findByPlanId(planId: string): Promise<SubscriptionEntity[]>;
  findByStatus(status: string): Promise<SubscriptionEntity[]>;
  findByStripeSubscriptionId(
    stripeSubscriptionId: string
  ): Promise<SubscriptionEntity | null>;
  update(
    id: string,
    subscription: SubscriptionEntity
  ): Promise<SubscriptionEntity>;
  delete(id: string): Promise<void>;
}
