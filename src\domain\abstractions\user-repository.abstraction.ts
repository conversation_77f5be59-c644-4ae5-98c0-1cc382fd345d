import { UserEntity } from '../entities/user.entity';

export interface IUserRepository {
  insert(user: Partial<UserEntity>): Promise<UserEntity>;
  findAll(): Promise<UserEntity[]>;
  findById(id: string): Promise<UserEntity | null>;
  findByEmail(email: string): Promise<UserEntity | null>;
  findByTaxId(tax_id: string): Promise<UserEntity | null>;
  update(id: string, user: UserEntity): Promise<UserEntity>;
  delete(id: string): Promise<void>;
  query(query: string, parameters?: any[]): Promise<any>;
}
