export class AddressEntity {
  constructor(address: AddressEntity) {
    this.id = address.id;
    this.street = address.street;
    this.number = address.number;
    this.complement = address.complement;
    this.neighborhood = address.neighborhood;
    this.city = address.city;
    this.state = address.state;
    this.zip_code = address.zip_code;
    this.created_at = address.created_at;
    this.updated_at = address.updated_at;
  }

  id: string;

  street: string;

  number: string;

  complement: string;

  neighborhood: string; // Bairro

  city: string;

  state: string;

  zip_code: string;

  created_at: Date;

  updated_at: Date;
}
