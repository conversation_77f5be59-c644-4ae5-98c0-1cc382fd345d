export interface ClassSchedule {
  day_of_week: number; // 0-6 (do<PERSON><PERSON> a <PERSON>o)
  start_time: string; // HH:mm
  end_time: string; // HH:mm
}

export class ClassEntity {
  id: string;
  course_id: string;
  institution_id: string;
  name: string;
  description?: string;
  max_students: number;
  current_students: number;
  status: 'open' | 'closed' | 'in_progress' | 'finished';
  start_date?: Date;
  end_date?: Date;
  schedule?: ClassSchedule[];
  created_by: string;
  updated_by?: string;
  created_at: Date;
  updated_at?: Date;
}
