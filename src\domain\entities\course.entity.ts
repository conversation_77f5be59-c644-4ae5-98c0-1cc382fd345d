export class CourseEntity {
  constructor(course: CourseEntity) {
    this.id = course.id;
    this.name = course.name;
    this.description = course.description;
    this.code = course.code;
    this.credits = course.credits;
    this.duration_hours = course.duration_hours;
    this.status = course.status;
    this.institution_id = course.institution_id;
    this.created_by = course.created_by;
    this.updated_by = course.updated_by;
    this.created_at = course.created_at;
    this.updated_at = course.updated_at;
  }

  id: string;
  name: string;
  description: string;
  code: string;
  credits: number;
  duration_hours: number;
  status: 'active' | 'inactive' | 'archived';
  institution_id: string;
  created_by: string;
  updated_by?: string;
  created_at: Date;
  updated_at?: Date;
}
