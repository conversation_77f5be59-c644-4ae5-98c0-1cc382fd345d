export class CourseEntity {
  constructor(course: CourseEntity) {
    this.id = course.id;
    this.institution_id = course.institution_id;
    this.name = course.name;
    this.description = course.description;
    this.duration_months = course.duration_months;
    this.price = course.price;
    this.max_students = course.max_students;
    this.status = course.status;
    this.start_date = course.start_date;
    this.end_date = course.end_date;
    this.created_by = course.created_by;
    this.updated_by = course.updated_by;
    this.created_at = course.created_at;
    this.updated_at = course.updated_at;
  }

  id: string;
  institution_id: string;
  name: string;
  description?: string;
  duration_months: number;
  price: number;
  max_students: number;
  status: 'active' | 'inactive' | 'completed' | 'cancelled';
  start_date?: Date;
  end_date?: Date;
  created_by: string;
  updated_by?: string;
  created_at: Date;
  updated_at?: Date;
}
