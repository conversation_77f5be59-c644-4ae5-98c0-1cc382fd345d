export class InstitutionEntity {
  constructor(institution: InstitutionEntity) {
    this.id = institution.id;
    this.name = institution.name;
    this.legal_name = institution.legal_name;
    this.phone_number = institution.phone_number;
    this.tax_id = institution.tax_id;
    this.incorporation_date = institution.incorporation_date;
    this.email = institution.email;
    this.website = institution.website;
    this.actived = institution.actived;
    this.status = institution.status;
    this.plan_id = institution.plan_id;
    this.main_institution_id = institution.main_institution_id;
    this.address_id = institution.address_id;
    this.address_id = institution.address_id;
    this.created_by = institution.created_by;
    this.updated_by = institution.updated_by;
    this.created_at = institution.created_at;
    this.updated_at = institution.updated_at;
  }

  id: string;

  name: string; // Nome Fantasia

  legal_name: string; // Razão Social

  phone_number: string;

  tax_id: string; // CNPJ

  incorporation_date: Date; // Data de Fundação

  address_id: string;

  plan_id: string;

  main_institution_id?: string;

  status: string;

  email: string;

  website: string;

  actived: boolean;

  created_by: string;

  updated_by: string;

  created_at: Date;

  updated_at: Date;
}
