export class FinancialTransactionEntity {
  id: string;
  institution_id: string;
  type: 'income' | 'expense';
  category: 'enrollment_fee' | 'monthly_fee' | 'material' | 'salary' | 'infrastructure' | 'other';
  amount: number;
  description: string;
  reference_id?: string;
  reference_type?: string;
  payment_method_id?: string;
  status: 'pending' | 'completed' | 'cancelled';
  transaction_date: Date;
  due_date?: Date;
  paid_date?: Date;
  stripe_payment_intent_id?: string;
  stripe_session_id?: string;
  payment_type: 'one_time' | 'subscription' | 'refund';
  currency: string;
  metadata?: Record<string, any>;
  created_by: string;
  updated_by?: string;
  created_at: Date;
  updated_at?: Date;
}

export class InvoiceEntity {
  id: string;
  enrollment_id: string;
  institution_id: string;
  invoice_number: string;
  amount: number;
  due_date: Date;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  payment_date?: Date;
  payment_method_id?: string;
  notes?: string;
  stripe_invoice_id?: string;
  stripe_payment_intent_id?: string;
  currency: string;
  metadata?: Record<string, any>;
  created_by: string;
  updated_by?: string;
  created_at: Date;
  updated_at?: Date;
}

export class SubscriptionEntity {
  id: string;
  institution_id: string;
  plan_id: string;
  stripe_subscription_id: string;
  stripe_customer_id: string;
  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';
  current_period_start: Date;
  current_period_end: Date;
  trial_start?: Date;
  trial_end?: Date;
  canceled_at?: Date;
  ended_at?: Date;
  metadata?: Record<string, any>;
  created_by: string;
  updated_by?: string;
  created_at: Date;
  updated_at?: Date;
}

export class PaymentMethodEntity {
  id: string;
  institution_id: string;
  name: string;
  type: 'card' | 'boleto' | 'pix' | 'bank_transfer' | 'cash' | 'check';
  is_active: boolean;
  stripe_payment_method_id?: string;
  stripe_customer_id?: string;
  card_brand?: string;
  card_last4?: string;
  card_exp_month?: number;
  card_exp_year?: number;
  is_default: boolean;
  created_by: string;
  updated_by?: string;
  created_at: Date;
  updated_at?: Date;
}
