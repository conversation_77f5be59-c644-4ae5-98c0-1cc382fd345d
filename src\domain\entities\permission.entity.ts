export class PermissionEntity {
  constructor(permission: PermissionEntity) {
    this.id = permission.id;
    this.name = permission.name;
    this.key = permission.key;
    this.description = permission.description;
    this.created_by = permission.created_by;
    this.updated_by = permission.updated_by;
    this.created_at = permission.created_at;
    this.updated_at = permission.updated_at;
  }

  id: string;

  name: string;

  key: string;

  description: string;

  created_by: string;

  updated_by: string;

  created_at: Date;

  updated_at: Date;
}
