export class PlanEntity {
  constructor(plan: PlanEntity) {
    this.id = plan.id;
    this.name = plan.name;
    this.description = plan.description;
    this.price = plan.price;
    this.currency = plan.currency;
    this.billing_period = plan.billing_period;
    this.max_users = plan.max_users;
    this.max_institutions = plan.max_institutions;
    this.features = plan.features;
    this.stripe_price_id = plan.stripe_price_id;
    this.is_active = plan.is_active;
    this.created_by = plan.created_by;
    this.updated_by = plan.updated_by;
    this.created_at = plan.created_at;
    this.updated_at = plan.updated_at;
  }

  id: string;

  name: string;

  description: string;

  price: number;

  currency: string;

  billing_period: string; // monthly, yearly

  max_users: number;

  max_institutions: number;

  features: Record<string, any>; // JSON object with plan features

  stripe_price_id?: string;

  is_active: boolean;

  created_by: string;

  updated_by: string;

  created_at: Date;

  updated_at: Date;
}
