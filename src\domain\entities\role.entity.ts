export class RoleEntity {
  constructor(role: RoleEntity) {
    this.id = role.id;
    this.name = role.name;
    this.key = role.key;
    this.description = role.description;
    this.created_by = role.created_by;
    this.updated_by = role.updated_by;
    this.created_at = role.created_at;
    this.updated_at = role.updated_at;
  }

  id: string;

  name: string;

  key: string;

  description: string;

  institution_id: string;

  created_by: string;

  updated_by: string;

  created_at: Date;

  updated_at: Date;
}
