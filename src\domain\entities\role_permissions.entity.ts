export class RolePermissionEntity {
  constructor(rolePermission: RolePermissionEntity) {
    this.id = rolePermission.id;
    this.role_id = rolePermission.role_id;
    this.permission_id = rolePermission.permission_id;
    this.created_by = rolePermission.created_by;
    this.updated_by = rolePermission.updated_by;
    this.created_at = rolePermission.created_at;
    this.updated_at = rolePermission.updated_at;
  }

  id: string;

  role_id: string;

  permission_id: string;

  created_by: string;

  updated_by: string;

  created_at: Date;

  updated_at: Date;
}
