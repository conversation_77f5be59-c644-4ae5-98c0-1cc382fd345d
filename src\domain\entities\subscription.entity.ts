export class SubscriptionEntity {
  constructor(subscription: SubscriptionEntity) {
    this.id = subscription.id;
    this.institution_id = subscription.institution_id;
    this.plan_id = subscription.plan_id;
    this.status = subscription.status;
    this.stripe_subscription_id = subscription.stripe_subscription_id;
    this.stripe_customer_id = subscription.stripe_customer_id;
    this.current_period_start = subscription.current_period_start;
    this.current_period_end = subscription.current_period_end;
    this.trial_start = subscription.trial_start;
    this.trial_end = subscription.trial_end;
    this.canceled_at = subscription.canceled_at;
    this.ended_at = subscription.ended_at;
    this.metadata = subscription.metadata;
    this.created_by = subscription.created_by;
    this.updated_by = subscription.updated_by;
    this.created_at = subscription.created_at;
    this.updated_at = subscription.updated_at;
  }

  id: string;

  institution_id: string;

  plan_id: string;

  status: string; // active, inactive, canceled, expired, trial

  stripe_subscription_id?: string;

  stripe_customer_id?: string;

  current_period_start?: Date;

  current_period_end?: Date;

  trial_start?: Date;

  trial_end?: Date;

  canceled_at?: Date;

  ended_at?: Date;

  metadata: Record<string, any>; // JSON object for additional data

  created_by: string;

  updated_by: string;

  created_at: Date;

  updated_at: Date;
}
