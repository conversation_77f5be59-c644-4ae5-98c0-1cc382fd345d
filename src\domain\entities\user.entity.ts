export class UserEntity {
  constructor(user: UserEntity) {
    this.id = user.id;
    this.name = user.name;
    this.email = user.email;
    this.password_hash = user.password_hash;
    this.phone_number = user.phone_number;
    this.tax_id = user.tax_id;
    this.birth_date = user.birth_date;
    this.institution_id = user.institution_id;
    this.is_enabled_2fa_app = user.is_enabled_2fa_app;
    this.is_email_verified = user.is_email_verified;
    this.is_phone_verified = user.is_phone_verified;
    this.secret_key_2fa = user.secret_key_2fa;
    this.actived = user.actived;
    this.created_by = user.created_by;
    this.updated_by = user.updated_by;
    this.created_at = user.created_at;
    this.updated_at = user.updated_at;
  }

  id: string;

  name: string;

  email: string;

  password_hash: string;

  phone_number: string;

  tax_id: string;

  birth_date: Date;

  institution_id?: string;

  is_enabled_2fa_app: boolean;

  is_email_verified: boolean;

  is_phone_verified: boolean;

  secret_key_2fa: string;

  actived: boolean;

  created_by: string;

  updated_by: string;

  created_at: Date;

  updated_at: Date;
}
