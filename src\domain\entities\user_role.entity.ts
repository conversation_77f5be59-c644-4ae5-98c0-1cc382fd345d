export class UserRoleEntity {
  constructor(userRole: UserRoleEntity) {
    this.id = userRole.id;
    this.role_id = userRole.role_id;
    this.user_id = userRole.user_id;
    this.created_by = userRole.created_by;
    this.updated_by = userRole.updated_by;
    this.created_at = userRole.created_at;
    this.updated_at = userRole.updated_at;
  }

  id: string;

  role_id: string;

  user_id: string;

  created_by: string;

  updated_by: string;

  created_at: Date;

  updated_at: Date;
}
