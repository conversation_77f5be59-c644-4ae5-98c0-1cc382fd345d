export interface IJwtServicePayload {
  sub: string;
  email: string;
  name: string;
  institution_id?: string;
}

export interface IJwtServiceRefreshPayload {
  sub: string;
  email: string;
  type: 'refresh';
}

export interface IJwtServiceChangePasswordTokenPayload {
  sub: string;
  email: string;
  name: string;
}

export interface IJwtServiceFirstAccessTokenPayload {
  sub: string;
  email: string;
  name: string;
  institution_id?: string;
}

export interface IJwtService {
  checkToken(token: string): Promise<any>;
  createToken(
    payload: IJwtServicePayload | IJwtServiceRefreshPayload,
    secret: string,
    expiresIn: string
  ): string;

  createFirstAccessToken(payload: IJwtServiceFirstAccessTokenPayload): string;
}
