export interface IJwtServicePayload {
  username: string;
}

export interface IJwtServiceChangePasswordTokenPayload {
  sub: string;
  email: string;
  name: string;
}

export interface IJwtServiceFirstAccessTokenPayload {
  sub: string;
  email: string;
  name: string;
  institution_id?: string;
}

export interface IJwtService {
  checkToken(token: string): Promise<any>;
  createToken(
    payload: IJwtServicePayload,
    secret: string,
    expiresIn: string
  ): string;

  createFirstAccessToken(
    payload: IJwtServiceFirstAccessTokenPayload,
    secret: string,
    expiresIn: string
  ): string;
}
