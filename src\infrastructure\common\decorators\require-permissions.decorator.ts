import { SetMetadata } from '@nestjs/common';

/**
 * Decorator para definir permissões requeridas em um endpoint
 *
 * @param permissions - Array de permissões necessárias para acessar o endpoint
 *
 * @example
 * ```typescript
 * @Get()
 * @UseGuards(JwtAuthGuard, PermissionGuard)
 * @RequirePermissions('course.view')
 * async getCourses() {
 *   // Apenas usuários com permissão 'course.view' podem acessar
 * }
 *
 * @Post()
 * @UseGuards(JwtAuthGuard, PermissionGuard)
 * @RequirePermissions('course.create', 'course.manage')
 * async createCourse() {
 *   // Usuário precisa ter 'course.create' OU 'course.manage'
 * }
 * ```
 */
export const RequirePermissions = (...permissions: string[]) =>
  SetMetadata('permissions', permissions);

/**
 * Decorator para definir roles requeridas em um endpoint
 *
 * @param roles - Array de roles necessárias para acessar o endpoint
 *
 * @example
 * ```typescript
 * @Get()
 * @UseGuards(JwtAuthGuard, PermissionGuard)
 * @RequireRoles('admin', 'teacher')
 * async getAdminData() {
 *   // Apenas usuários com role 'admin' OU 'teacher' podem acessar
 * }
 * ```
 */
export const RequireRoles = (...roles: string[]) => SetMetadata('roles', roles);

/**
 * Decorator para definir que o endpoint requer autenticação mas sem permissões específicas
 *
 * @example
 * ```typescript
 * @Get()
 * @UseGuards(JwtAuthGuard)
 * @RequireAuth()
 * async getProfile() {
 *   // Qualquer usuário autenticado pode acessar
 * }
 * ```
 */
export const RequireAuth = () => SetMetadata('auth-only', true);
