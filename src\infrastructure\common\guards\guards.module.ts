import { Module } from '@nestjs/common';
import { ExceptionsModule } from '../../exceptions/exceptions.module';
import { LoggerModule } from '../../logger/logger.module';
import { PermissionServiceModule } from '../../services/permission/permission.module';
import { PermissionGuard } from './permission.guard';

@Module({
  imports: [PermissionServiceModule, LoggerModule, ExceptionsModule],
  providers: [PermissionGuard],
  exports: [PermissionGuard]
})
export class GuardsModule {}
