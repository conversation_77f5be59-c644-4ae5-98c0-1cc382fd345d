import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ExceptionsService } from '../../exceptions/exceptions.service';
import { LoggerService } from '../../logger/logger.service';
import { PermissionService } from '../../services/permission/permission.service';

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly permissionService: PermissionService,
    private readonly logger: LoggerService,
    private readonly exceptionService: ExceptionsService
  ) {}

  private readonly logContextName: string = 'PERMISSION_GUARD';

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.get<string[]>(
      'permissions',
      context.getHandler()
    );

    const requiredRoles = this.reflector.get<string[]>(
      'roles',
      context.getHandler()
    );

    const authOnly = this.reflector.get<boolean>(
      'auth-only',
      context.getHandler()
    );

    // Se não há permissões, roles ou auth-only definidos, liberar acesso
    if (!requiredPermissions && !requiredRoles && !authOnly) {
      this.logger.log(
        this.logContextName,
        'Nenhuma permissão/role requerida - acesso liberado'
      );
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      this.logger.warn(
        this.logContextName,
        'Usuário não autenticado tentando acessar endpoint protegido'
      );
      this.exceptionService.UnauthorizedException({
        message: 'User not authenticated'
      });
      return false;
    }

    this.logger.log(
      this.logContextName,
      `Verificando acesso para usuário: ${user.sub}`
    );

    // Se é apenas auth-only, usuário autenticado pode acessar
    if (authOnly && !requiredPermissions && !requiredRoles) {
      this.logger.log(
        this.logContextName,
        `Acesso liberado - endpoint requer apenas autenticação`
      );
      return true;
    }

    // Verificar permissões se definidas
    if (requiredPermissions && requiredPermissions.length > 0) {
      this.logger.log(
        this.logContextName,
        `Verificando permissões: ${requiredPermissions.join(', ')}`
      );

      const hasPermission = await this.permissionService.hasAnyPermission(
        user.sub,
        requiredPermissions
      );

      if (hasPermission) {
        this.logger.log(
          this.logContextName,
          `Acesso liberado - usuário tem permissão necessária`
        );
        return true;
      }
    }

    // Verificar roles se definidas
    if (requiredRoles && requiredRoles.length > 0) {
      this.logger.log(
        this.logContextName,
        `Verificando roles: ${requiredRoles.join(', ')}`
      );

      const userRoles = await this.permissionService.getUserRoles(user.sub);
      const hasRole = requiredRoles.some(role => userRoles.includes(role));

      if (hasRole) {
        this.logger.log(
          this.logContextName,
          `Acesso liberado - usuário tem role necessária`
        );
        return true;
      }
    }

    // Se chegou até aqui, usuário não tem permissão
    this.logger.warn(
      this.logContextName,
      `Acesso negado para usuário ${user.sub} - permissões insuficientes`
    );

    this.exceptionService.forbiddenException({
      message: 'Insufficient permissions to access this resource'
    });

    return false;
  }
}
