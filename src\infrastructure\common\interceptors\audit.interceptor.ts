import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class AuditInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Se há um usuário autenticado, adicionar informações de auditoria ao body
    if (user && request.body) {
      const method = request.method;

      if (method === 'POST') {
        // Para criação, definir created_by
        request.body.created_by = user.sub;
        request.body.created_at = new Date();
      } else if (method === 'PUT' || method === 'PATCH') {
        // Para atualização, definir updated_by
        request.body.updated_by = user.sub;
        request.body.updated_at = new Date();
      }
    }

    return next.handle().pipe(
      map(data => {
        // Aqui podemos processar a resposta se necessário
        return data;
      })
    );
  }
}
