import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ExceptionsService } from '../../exceptions/exceptions.service';
import { LoggerService } from '../../logger/logger.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    // @Inject(UserUsecasesProxy.POST_USER_USECASE_PROXY)
    private readonly logger: LoggerService,
    private readonly exceptionService: ExceptionsService
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        (request: Request) => {
          return request.cookies.accessToken;
        }
      ]),
      secretOrKey: process.env.JWT_SECRET || 'secret'
    });
    if (!process.env.JWT_SECRET) {
      throw new Error('JWT_SECRET is not defined in environment variables');
    }
  }

  async validate(payload: any) {
    // const user = await this.loginUsecaseProxy
    //   .getInstance()
    //   .validateUserForJWTStragtegy(payload.username);
    // if (!user) {
    //   this.logger.warn('JwtStrategy', `User not found`);
    //   this.exceptionService.UnauthorizedException({
    //     message: 'User not found'
    //   });
    // }
    // return user;
    return true;
  }
}
