import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ExceptionsService } from '../../exceptions/exceptions.service';
import { LoggerService } from '../../logger/logger.service';
import { UserRepository } from '../../repositories/user-repository';
import { PermissionService } from '../../services/permission/permission.service';
import { TokenBlacklistService } from '../../services/token-blacklist/token-blacklist.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly permissionService: PermissionService,
    private readonly logger: LoggerService,
    private readonly exceptionService: ExceptionsService,
    private readonly tokenBlacklistService: TokenBlacklistService
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        (request: Request) => {
          return (
            request.cookies?.accessToken ||
            ExtractJwt.fromAuthHeaderAsBearerToken()(request)
          );
        }
      ]),
      secretOrKey: process.env.JWT_SECRET || 'secret',
      passReqToCallback: true // Permite acesso ao request no método validate
    });
    if (!process.env.JWT_SECRET) {
      throw new Error('JWT_SECRET is not defined in environment variables');
    }
  }

  async validate(request: Request, payload: any) {
    try {
      // Extrair o token do request
      const token =
        request.cookies?.accessToken ||
        request.headers.authorization?.replace('Bearer ', '');

      // Verificar se o token está na blacklist
      if (token) {
        const isBlacklisted =
          await this.tokenBlacklistService.isBlacklisted(token);
        if (isBlacklisted) {
          this.logger.warn(
            'JwtStrategy',
            `Token blacklisted for user: ${payload.sub}`
          );
          this.exceptionService.UnauthorizedException({
            message: 'Token has been invalidated'
          });
        }
      }

      // Verificar se o usuário ainda existe e está ativo
      const user = await this.userRepository.findById(payload.sub);
      if (!user) {
        this.logger.warn('JwtStrategy', `User not found: ${payload.sub}`);
        this.exceptionService.UnauthorizedException({
          message: 'User not found'
        });
      }

      if (!user!.actived) {
        this.logger.warn('JwtStrategy', `User inactive: ${payload.sub}`);
        this.exceptionService.UnauthorizedException({
          message: 'User inactive'
        });
      }

      // Buscar role do usuário
      const userRole = await this.permissionService.getUserRole(user!.id);

      // Retornar dados do usuário que serão anexados ao request
      return {
        sub: user!.id,
        email: user!.email,
        name: user!.name,
        institution_id: user!.institution_id,
        role: userRole
      };
    } catch (error) {
      this.logger.error('JwtStrategy', 'Error validating token', error);
      this.exceptionService.UnauthorizedException({
        message: 'Invalid token'
      });
    }
  }
}
