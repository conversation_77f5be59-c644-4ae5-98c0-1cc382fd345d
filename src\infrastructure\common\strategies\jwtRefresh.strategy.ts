import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { Strategy as JwtStrategy } from 'passport-jwt';
import { EnvironmentConfigService } from '../../config/environment-config/environment-config.service';
import { ExceptionsService } from '../../exceptions/exceptions.service';
import { LoggerService } from '../../logger/logger.service';

@Injectable()
export class JwtRefreshTokenStrategy extends PassportStrategy(
  JwtStrategy,
  'jwt-refresh-token'
) {
  constructor(
    private readonly configService: EnvironmentConfigService,
    private readonly logger: LoggerService,
    private readonly exceptionService: ExceptionsService
  ) {
    super({
      jwtFromRequest: (request: Request) => {
        return request.cookies.refreshToken;
      },
      secretOrKey: configService.getJwtSecret() || 'secret'
    });
  }

  async validate(
    request: Request,
    payload: {
      username: string;
      userId: string;
    }
  ) {
    const refreshToken = request.cookies?.refreshToken;
    // const user = this.loginUsecaseProxy
    //   .getInstance()
    //   .getUserIfRefreshTokenMatches(refreshToken, payload.username);
    // if (!user) {
    //   this.logger.warn('JwtStrategy', `User not found or hash not correct`);
    //   this.exceptionService.UnauthorizedException({
    //     message: 'User not found or hash not correct'
    //   });
    // }
    // return user;
    return true;
  }
}
