import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { ExceptionsService } from '../../exceptions/exceptions.service';
import { LoggerService } from '../../logger/logger.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(
    // @Inject(UsecasesProxyModule.LOGIN_USECASES_PROXY)
    private readonly logger: LoggerService,
    private readonly exceptionService: ExceptionsService
  ) {
    super();
  }

  async validate(email: string, password: string) {
    if (!email || !password) {
      this.logger.warn(
        'LocalStrategy',
        `email or password is missing, BadRequestException`
      );
      this.exceptionService.UnauthorizedException();
    }
    // const user = await this.loginUsecaseProxy
    //   .getInstance()
    //   .validateUserForLocalStragtegy(email, password);
    // if (!user) {
    //   this.logger.warn('LocalStrategy', `Invalid email or password`);
    //   this.exceptionService.UnauthorizedException({
    //     message: 'Invalid email or password.'
    //   });
    // }
    // return user;
    return true;
  }
}
