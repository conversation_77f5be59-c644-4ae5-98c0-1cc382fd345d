import { Modu<PERSON> } from '@nestjs/common';
import { ExceptionsModule } from '../../exceptions/exceptions.module';
import { LoggerModule } from '../../logger/logger.module';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { JwtStrategy } from './jwt.strategy';
import { JwtRefreshTokenStrategy } from './jwtRefresh.strategy';
import { LocalStrategy } from './local.strategy';

@Module({
  imports: [RepositoriesModule, LoggerModule, ExceptionsModule],
  providers: [JwtStrategy, LocalStrategy, JwtRefreshTokenStrategy],
  exports: [JwtStrategy, LocalStrategy, JwtRefreshTokenStrategy]
})
export class StrategiesModule {}
