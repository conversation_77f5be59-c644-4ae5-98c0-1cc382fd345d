import { Modu<PERSON> } from '@nestjs/common';
import { EnvironmentConfigModule } from '../../config/environment-config/environment-config.module';
import { ExceptionsModule } from '../../exceptions/exceptions.module';
import { LoggerModule } from '../../logger/logger.module';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { PermissionServiceModule } from '../../services/permission/permission.module';
import { JwtStrategy } from './jwt.strategy';
import { JwtRefreshTokenStrategy } from './jwtRefresh.strategy';
import { LocalStrategy } from './local.strategy';

@Module({
  imports: [
    RepositoriesModule,
    PermissionServiceModule,
    LoggerModule,
    ExceptionsModule,
    EnvironmentConfigModule
  ],
  providers: [JwtStrategy, LocalStrategy, JwtRefreshTokenStrategy],
  exports: [JwtStrategy, LocalStrategy, JwtRefreshTokenStrategy]
})
export class StrategiesModule {}
