import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IDatabaseConfig } from '../../../domain/types/config/database-config';
import { IJWTConfig } from '../../../domain/types/config/jwt-config';

@Injectable()
export class EnvironmentConfigService implements IDatabaseConfig, IJWTConfig {
  constructor(private configService: ConfigService) {}

  getJwtSecret(): string | undefined {
    return this.configService.get<string>('JWT_SECRET');
  }

  getJwtExpirationTime(): string | undefined {
    return this.configService.get<string>('JWT_EXPIRATION_TIME');
  }

  getJwtRefreshSecret(): string | undefined {
    return this.configService.get<string>('JWT_REFRESH_TOKEN_SECRET');
  }

  getJwtRefreshExpirationTime(): string | undefined {
    return this.configService.get<string>('JWT_REFRESH_TOKEN_EXPIRATION_TIME');
  }

  getDatabaseHost(): string | undefined {
    return this.configService.get<string>('DATABASE_HOST');
  }

  getDatabasePort(): number {
    return this.configService.get<number>('DATABASE_PORT') || 5432;
  }

  getDatabaseUser(): string | undefined {
    return this.configService.get<string>('DATABASE_USER');
  }

  getDatabasePassword(): string | undefined {
    return this.configService.get<string>('DATABASE_PASSWORD');
  }

  getDatabaseName(): string | undefined {
    return this.configService.get<string>('DATABASE_NAME');
  }

  getDatabaseSync(): boolean {
    return this.configService.get<boolean>('DATABASE_SYNCHRONIZE') || false;
  }
}
