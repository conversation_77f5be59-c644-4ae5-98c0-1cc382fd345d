import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

@Injectable()
export class RedisConfigService {
  private readonly redisClient: Redis;

  constructor(private configService: ConfigService) {
    this.redisClient = new Redis({
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD'),
      db: this.configService.get<number>('REDIS_DB', 0),
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });

    this.redisClient.on('connect', () => {
      console.log('✅ Redis connected successfully');
    });

    this.redisClient.on('error', (error) => {
      console.error('❌ Redis connection error:', error);
    });
  }

  getClient(): Redis {
    return this.redisClient;
  }

  async disconnect(): Promise<void> {
    await this.redisClient.disconnect();
  }
}
