import { Injectable } from '@nestjs/common';
import Stripe from 'stripe';

@Injectable()
export class StripeConfig {
  private stripe: Stripe;

  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-06-30.basil',
      typescript: true,
    });
  }

  getStripeInstance(): Stripe {
    return this.stripe;
  }

  getPublishableKey(): string {
    return process.env.STRIPE_PUBLISHABLE_KEY!;
  }

  getWebhookSecret(): string {
    return process.env.STRIPE_WEBHOOK_SECRET!;
  }

  getConfig() {
    return {
      secretKey: process.env.STRIPE_SECRET_KEY,
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
      currency: process.env.STRIPE_CURRENCY || 'brl',
      successUrl: process.env.STRIPE_SUCCESS_URL || 'http://localhost:3000/success',
      cancelUrl: process.env.STRIPE_CANCEL_URL || 'http://localhost:3000/cancel',
    };
  }
}
