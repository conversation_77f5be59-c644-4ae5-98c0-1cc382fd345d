import * as dotenv from 'dotenv';
import { DataSource, DataSourceOptions } from 'typeorm';

if (process.env.NODE_ENV === 'local') {
  dotenv.config({ path: './env/.env.local' });
}

const config: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DATABASE_HOST,
  port: parseInt(process.env.DATABASE_PORT || '5432'),
  username: process.env.DATABASE_USER,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  entities: [__dirname + './../../**/*.entity{.ts,.js}'],
  synchronize: false,
  schema: process.env.DATABASE_SCHEMA,
  migrationsRun: true,
  migrationsTableName: `migrations_${process.env.DATABASE_NAME}`,
  migrations: ['database/migrations/**/*{.ts,.js}']
  // ssl: {
  //   rejectUnauthorized: false
  // }
};

console.log(config);

export default new DataSource(config);
