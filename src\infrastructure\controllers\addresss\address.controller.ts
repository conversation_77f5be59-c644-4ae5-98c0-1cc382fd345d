import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { AddressUsecasesProxy } from '../../../infrastructure/usecases-proxy/address/address-usecases-proxy.constants';
import { UseCaseProxy } from '../../../infrastructure/usecases-proxy/usecases-proxy';
import { CreateAddressUseCase } from '../../../usecases/address/create-address.usecase';
import { DeleteAddressUseCase } from '../../../usecases/address/delete-address.usecase';
import { GetAddressUseCase } from '../../../usecases/address/get-address.usecase';
import { GetAddressesUseCase } from '../../../usecases/address/get-addresses.usecase';
import { UpdateAddressUseCase } from '../../../usecases/address/update-address.usecase';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { CreateAddressDto, UpdateAddressDto } from './address.dto';
import { AddressPresenter } from './address.presenter';

@Controller('address')
@ApiTags('Address')
@ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden.' })
@ApiResponse({
  status: HttpStatus.INTERNAL_SERVER_ERROR,
  description: 'Internal Server Error.'
})
@ApiExtraModels(AddressPresenter)
export class AddressController {
  constructor(
    @Inject(AddressUsecasesProxy.POST_ADDRESS_USECASE_PROXY)
    private readonly postAddressUsecaseProxy: UseCaseProxy<CreateAddressUseCase>,
    @Inject(AddressUsecasesProxy.GET_ADDRESSES_USECASE_PROXY)
    private readonly getAddressesUsecaseProxy: UseCaseProxy<GetAddressesUseCase>,
    @Inject(AddressUsecasesProxy.GET_ADDRESS_USECASE_PROXY)
    private readonly getAddressUsecaseProxy: UseCaseProxy<GetAddressUseCase>,
    @Inject(AddressUsecasesProxy.UPDATE_ADDRESS_USECASE_PROXY)
    private readonly updateAddressUsecaseProxy: UseCaseProxy<UpdateAddressUseCase>,
    @Inject(AddressUsecasesProxy.DELETE_ADDRESS_USECASE_PROXY)
    private readonly deleteAddressUsecaseProxy: UseCaseProxy<DeleteAddressUseCase>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: AddressPresenter,
    description: 'Address created'
  })
  async createAddress(
    @Body() body: CreateAddressDto
  ): Promise<AddressPresenter> {
    const address = await this.postAddressUsecaseProxy
      .getInstance()
      .execute(body);
    return new AddressPresenter(address);
  }

  @Put(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: AddressPresenter,
    description: 'Address updated'
  })
  async updateAddress(
    @Param('id') id: string,
    @Body() body: UpdateAddressDto
  ): Promise<AddressPresenter> {
    const user = await this.updateAddressUsecaseProxy
      .getInstance()
      .execute(id, body);
    return new AddressPresenter(user);
  }

  @Delete(':id')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Address deleted'
  })
  async deleteAddress(@Param('id') id: string): Promise<void> {
    await this.deleteAddressUsecaseProxy.getInstance().execute(id);
  }

  @Get(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: AddressPresenter,
    description: 'Address returned'
  })
  async getAddress(@Param('id') id: string): Promise<AddressPresenter> {
    const address = await this.getAddressUsecaseProxy.getInstance().execute(id);
    return address;
  }

  @Get()
  @ApiResponse({
    status: HttpStatus.OK,
    type: AddressPresenter,
    description: 'Addresses returned'
  })
  async getAddresses(): Promise<AddressPresenter[]> {
    const addresses = await this.getAddressesUsecaseProxy
      .getInstance()
      .execute();
    return addresses;
  }
}
