import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateAddressDto {
  @ApiProperty()
  @IsNotEmpty()
  street: string;

  @ApiProperty()
  @IsOptional()
  number: string;

  @ApiProperty()
  @IsNotEmpty()
  complement: string;

  @ApiProperty()
  @IsNotEmpty()
  neighborhood: string; // <PERSON>rro

  @ApiProperty()
  @IsNotEmpty()
  city: string;

  @ApiProperty()
  @IsNotEmpty()
  state: string;

  @ApiProperty()
  @IsNotEmpty()
  zip_code: string;

  created_at: Date;

  updated_at: Date;
}

export class UpdateAddressDto {
  @ApiProperty()
  @IsNotEmpty()
  id: string;

  @ApiProperty()
  @IsNotEmpty()
  street: string;

  @ApiProperty()
  @IsNotEmpty()
  number: string;

  @ApiProperty()
  @IsNotEmpty()
  complement: string;

  @ApiProperty()
  @IsNotEmpty()
  neighborhood: string; // <PERSON>rro

  @ApiProperty()
  @IsNotEmpty()
  city: string;

  @ApiProperty()
  @IsNotEmpty()
  state: string;

  @ApiProperty()
  @IsNotEmpty()
  zip_code: string;

  created_at: Date;

  updated_at: Date;
}
