import { ApiProperty } from '@nestjs/swagger';
import { AddressEntity } from '../../../domain/entities/address.entity';

export class AddressPresenter {
  @ApiProperty()
  id: string;

  @ApiProperty()
  street: string;

  @ApiProperty()
  number: string;

  @ApiProperty()
  complement: string;

  @ApiProperty()
  neighborhood: string; // Bairro

  @ApiProperty()
  city: string;

  @ApiProperty()
  state: string;

  @ApiProperty()
  zip_code: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;

  constructor(address: AddressEntity) {
    this.id = address.id;
    this.street = address.street;
    this.number = address.number;
    this.complement = address.complement;
    this.neighborhood = address.neighborhood;
    this.city = address.city;
    this.state = address.state;
    this.zip_code = address.zip_code;
    this.created_at = address.created_at;
    this.updated_at = address.updated_at;
  }
}
