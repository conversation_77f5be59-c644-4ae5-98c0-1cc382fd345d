import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  UseInterceptors
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../../common/guards/permission.guard';
import { RequirePermissions } from '../../common/decorators/require-permissions.decorator';
import {
  CurrentUser,
  CurrentUserData
} from '../../common/decorators/current-user.decorator';
import { AuditInterceptor } from '../../common/interceptors/audit.interceptor';
import { AttendanceUsecasesProxy } from '../../usecases-proxy/attendance/attendance-usecases-proxy.constants';
import { UseCaseProxy } from '../../usecases-proxy/usecases-proxy';
import { CreateAttendanceUseCase } from '../../../usecases/attendance/create-attendance.usecase';
import { DeleteAttendanceUseCase } from '../../../usecases/attendance/delete-attendance.usecase';
import { GetAttendanceUseCase } from '../../../usecases/attendance/get-attendance.usecase';
import { GetAttendancesByEnrollmentUseCase } from '../../../usecases/attendance/get-attendances-by-enrollment.usecase';
import { GetAttendancesByDateUseCase } from '../../../usecases/attendance/get-attendances-by-date.usecase';
import { GetAttendancesUseCase } from '../../../usecases/attendance/get-attendances.usecase';
import { GetAttendanceStatsUseCase } from '../../../usecases/attendance/get-attendance-stats.usecase';
import { UpdateAttendanceUseCase } from '../../../usecases/attendance/update-attendance.usecase';
import {
  CreateAttendanceDto,
  UpdateAttendanceDto,
  GetAttendancesByDateDto
} from './attendance.dto';
import {
  AttendancePresenter,
  AttendanceStatsPresenter
} from './attendance.presenter';

@Controller('attendance')
@ApiTags('Attendance')
@ApiExtraModels(AttendancePresenter, AttendanceStatsPresenter)
export class AttendanceController {
  constructor(
    @Inject(AttendanceUsecasesProxy.GET_ATTENDANCE_USECASE_PROXY)
    private readonly getAttendanceUsecaseProxy: UseCaseProxy<GetAttendanceUseCase>,
    @Inject(AttendanceUsecasesProxy.GET_ATTENDANCES_USECASE_PROXY)
    private readonly getAttendancesUsecaseProxy: UseCaseProxy<GetAttendancesUseCase>,
    @Inject(AttendanceUsecasesProxy.GET_ATTENDANCES_BY_ENROLLMENT_USECASE_PROXY)
    private readonly getAttendancesByEnrollmentUsecaseProxy: UseCaseProxy<GetAttendancesByEnrollmentUseCase>,
    @Inject(AttendanceUsecasesProxy.GET_ATTENDANCES_BY_DATE_USECASE_PROXY)
    private readonly getAttendancesByDateUsecaseProxy: UseCaseProxy<GetAttendancesByDateUseCase>,
    @Inject(AttendanceUsecasesProxy.GET_ATTENDANCE_STATS_USECASE_PROXY)
    private readonly getAttendanceStatsUsecaseProxy: UseCaseProxy<GetAttendanceStatsUseCase>,
    @Inject(AttendanceUsecasesProxy.POST_ATTENDANCE_USECASE_PROXY)
    private readonly postAttendanceUsecaseProxy: UseCaseProxy<CreateAttendanceUseCase>,
    @Inject(AttendanceUsecasesProxy.PUT_ATTENDANCE_USECASE_PROXY)
    private readonly updateAttendanceUsecaseProxy: UseCaseProxy<UpdateAttendanceUseCase>,
    @Inject(AttendanceUsecasesProxy.DELETE_ATTENDANCE_USECASE_PROXY)
    private readonly deleteAttendanceUsecaseProxy: UseCaseProxy<DeleteAttendanceUseCase>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.create')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: AttendancePresenter,
    description: 'Attendance record created'
  })
  async createAttendance(
    @Body() body: CreateAttendanceDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<AttendancePresenter> {
    const attendanceData = {
      ...body,
      created_by: user.sub
    };
    const attendance = await this.postAttendanceUsecaseProxy
      .getInstance()
      .execute(attendanceData);
    return new AttendancePresenter(attendance);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.update')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: AttendancePresenter,
    description: 'Attendance record updated'
  })
  async updateAttendance(
    @Param('id') id: string,
    @Body() body: UpdateAttendanceDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<AttendancePresenter> {
    const updateData = {
      ...body,
      updated_by: user.sub
    };
    const attendance = await this.updateAttendanceUsecaseProxy
      .getInstance()
      .execute(id, updateData);
    return new AttendancePresenter(attendance);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.delete')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Attendance record deleted'
  })
  async deleteAttendance(@Param('id') id: string): Promise<void> {
    await this.deleteAttendanceUsecaseProxy.getInstance().execute(id);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: AttendancePresenter,
    description: 'Attendance record returned'
  })
  async getAttendance(@Param('id') id: string): Promise<AttendancePresenter> {
    const attendance = await this.getAttendanceUsecaseProxy
      .getInstance()
      .execute(id);
    return new AttendancePresenter(attendance);
  }

  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [AttendancePresenter],
    description: 'Attendance records returned'
  })
  async getAttendances(): Promise<AttendancePresenter[]> {
    const attendances = await this.getAttendancesUsecaseProxy
      .getInstance()
      .execute();
    return attendances.map(attendance => new AttendancePresenter(attendance));
  }

  @Get('enrollment/:enrollmentId')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [AttendancePresenter],
    description: 'Enrollment attendance records returned'
  })
  async getAttendancesByEnrollment(
    @Param('enrollmentId') enrollmentId: string
  ): Promise<AttendancePresenter[]> {
    const attendances = await this.getAttendancesByEnrollmentUsecaseProxy
      .getInstance()
      .execute(enrollmentId);
    return attendances.map(attendance => new AttendancePresenter(attendance));
  }

  @Get('date/:date')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [AttendancePresenter],
    description: 'Date attendance records returned'
  })
  async getAttendancesByDate(
    @Param('date') date: string
  ): Promise<AttendancePresenter[]> {
    const classDate = new Date(date);
    const attendances = await this.getAttendancesByDateUsecaseProxy
      .getInstance()
      .execute(classDate);
    return attendances.map(attendance => new AttendancePresenter(attendance));
  }

  @Get('stats/:enrollmentId')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('attendance.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: AttendanceStatsPresenter,
    description: 'Attendance statistics returned'
  })
  async getAttendanceStats(
    @Param('enrollmentId') enrollmentId: string
  ): Promise<AttendanceStatsPresenter> {
    const stats = await this.getAttendanceStatsUsecaseProxy
      .getInstance()
      .execute(enrollmentId);
    return new AttendanceStatsPresenter(stats);
  }
}
