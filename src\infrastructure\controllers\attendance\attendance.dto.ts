import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID
} from 'class-validator';

export class CreateAttendanceDto {
  @ApiProperty({ description: 'Enrollment ID', example: 'uuid' })
  @IsNotEmpty()
  @IsUUID()
  enrollment_id: string;

  @ApiPropertyOptional({ description: 'Institution ID (auto-filled from user)', example: 'uuid' })
  @IsOptional()
  @IsUUID()
  institution_id?: string;

  @ApiProperty({
    description: 'Class date',
    example: '2024-01-15'
  })
  @IsNotEmpty()
  @IsDateString()
  class_date: Date;

  @ApiPropertyOptional({
    description: 'Attendance status',
    enum: ['present', 'absent', 'late', 'excused'],
    example: 'present'
  })
  @IsOptional()
  @IsEnum(['present', 'absent', 'late', 'excused'])
  status?: 'present' | 'absent' | 'late' | 'excused';

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Student arrived 10 minutes late'
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({ description: 'Recorded by user ID', example: 'uuid' })
  @IsNotEmpty()
  @IsUUID()
  recorded_by: string;
}

export class UpdateAttendanceDto {
  @ApiPropertyOptional({
    description: 'Attendance status',
    enum: ['present', 'absent', 'late', 'excused'],
    example: 'late'
  })
  @IsOptional()
  @IsEnum(['present', 'absent', 'late', 'excused'])
  status?: 'present' | 'absent' | 'late' | 'excused';

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Student had medical appointment'
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class GetAttendancesByDateDto {
  @ApiProperty({
    description: 'Class date',
    example: '2024-01-15'
  })
  @IsNotEmpty()
  @IsDateString()
  class_date: Date;
}
