import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AttendanceEntity } from '../../../domain/entities/attendance.entity';

export class AttendancePresenter {
  @ApiProperty({ description: 'Attendance ID', example: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Enrollment ID', example: 'uuid' })
  enrollment_id: string;

  @ApiProperty({ description: 'Institution ID', example: 'uuid' })
  institution_id: string;

  @ApiProperty({
    description: 'Class date',
    example: '2024-01-15'
  })
  class_date: Date;

  @ApiProperty({
    description: 'Attendance status',
    enum: ['present', 'absent', 'late', 'excused'],
    example: 'present'
  })
  status: 'present' | 'absent' | 'late' | 'excused';

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Student arrived 10 minutes late'
  })
  notes?: string;

  @ApiProperty({ description: 'Recorded by user ID', example: 'uuid' })
  recorded_by: string;

  @ApiProperty({ description: 'Created by user ID', example: 'uuid' })
  created_by: string;

  @ApiPropertyOptional({ description: 'Updated by user ID', example: 'uuid' })
  updated_by?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z'
  })
  created_at: Date;

  @ApiPropertyOptional({
    description: 'Update date',
    example: '2024-01-02T00:00:00.000Z'
  })
  updated_at?: Date;

  constructor(attendance: AttendanceEntity) {
    this.id = attendance.id;
    this.enrollment_id = attendance.enrollment_id;
    this.institution_id = attendance.institution_id;
    this.class_date = attendance.class_date;
    this.status = attendance.status;
    this.notes = attendance.notes;
    this.recorded_by = attendance.recorded_by;
    this.created_by = attendance.created_by;
    this.updated_by = attendance.updated_by;
    this.created_at = attendance.created_at;
    this.updated_at = attendance.updated_at;
  }
}

export class AttendanceStatsPresenter {
  @ApiProperty({ description: 'Total attendance records', example: 20 })
  total: number;

  @ApiProperty({ description: 'Present count', example: 18 })
  present: number;

  @ApiProperty({ description: 'Absent count', example: 1 })
  absent: number;

  @ApiProperty({ description: 'Late count', example: 1 })
  late: number;

  @ApiProperty({ description: 'Excused count', example: 0 })
  excused: number;

  @ApiProperty({ description: 'Attendance rate percentage', example: 95.0 })
  attendanceRate: number;

  constructor(stats: {
    total: number;
    present: number;
    absent: number;
    late: number;
    excused: number;
    attendanceRate: number;
  }) {
    this.total = stats.total;
    this.present = stats.present;
    this.absent = stats.absent;
    this.late = stats.late;
    this.excused = stats.excused;
    this.attendanceRate = stats.attendanceRate;
  }
}
