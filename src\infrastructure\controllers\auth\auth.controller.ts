import {
  Body,
  Controller,
  HttpStatus,
  Inject,
  Post,
  Req,
  Res,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { LoginUseCase } from '../../../usecases/auth/login.usecase';
import { LogoutUseCase } from '../../../usecases/auth/logout.usecase';
import { RefreshTokenUseCase } from '../../../usecases/auth/refresh-token.usecase';
import { SetPasswordUseCase } from '../../../usecases/auth/set-password.usecase';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { AuthUsecasesProxy } from '../../usecases-proxy/auth/auth-usecases-proxy.constants';
import { UseCaseProxy } from '../../usecases-proxy/usecases-proxy';
import { UserPresenter } from '../user/user.presenter';
import { LoginDto, RefreshTokenDto, SetPasswordDto } from './auth.dto';
import {
  LoginPresenter,
  LogoutPresenter,
  RefreshTokenPresenter
} from './auth.presenter';

@Controller('auth')
@ApiTags('Authentication')
@ApiResponse({ status: 403, description: 'Forbidden.' })
@ApiResponse({ status: 500, description: 'Internal Server Error.' })
@ApiExtraModels(
  LoginPresenter,
  RefreshTokenPresenter,
  LogoutPresenter,
  UserPresenter
)
export class AuthController {
  constructor(
    @Inject(AuthUsecasesProxy.LOGIN_USECASE_PROXY)
    private readonly loginUsecaseProxy: UseCaseProxy<LoginUseCase>,
    @Inject(AuthUsecasesProxy.LOGOUT_USECASE_PROXY)
    private readonly logoutUsecaseProxy: UseCaseProxy<LogoutUseCase>,
    @Inject(AuthUsecasesProxy.REFRESH_TOKEN_USECASE_PROXY)
    private readonly refreshTokenUsecaseProxy: UseCaseProxy<RefreshTokenUseCase>,
    @Inject(AuthUsecasesProxy.SET_PASSWORD_USECASE_PROXY)
    private readonly setPasswordUsecaseProxy: UseCaseProxy<SetPasswordUseCase>
  ) {}

  @Post('login')
  @ApiResponse({
    status: HttpStatus.OK,
    type: LoginPresenter,
    description: 'User logged in successfully'
  })
  async login(
    @Body() loginDto: LoginDto,
    @Res({ passthrough: true }) response: Response
  ): Promise<LoginPresenter> {
    const result = await this.loginUsecaseProxy.getInstance().execute(loginDto);

    // Definir cookies httpOnly para os tokens
    response.cookie('accessToken', result.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 15 * 60 * 1000 // 15 minutos
    });

    response.cookie('refreshToken', result.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000 // 7 dias
    });

    return new LoginPresenter(result);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: LogoutPresenter,
    description: 'User logged out successfully'
  })
  async logout(
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response
  ): Promise<LogoutPresenter> {
    const user = request.user as any;

    await this.logoutUsecaseProxy.getInstance().execute(user.sub);

    // Limpar cookies
    response.clearCookie('accessToken');
    response.clearCookie('refreshToken');

    return new LogoutPresenter();
  }

  @Post('refresh')
  @ApiResponse({
    status: HttpStatus.OK,
    type: RefreshTokenPresenter,
    description: 'Token refreshed successfully'
  })
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
    @Res({ passthrough: true }) response: Response
  ): Promise<RefreshTokenPresenter> {
    const result = await this.refreshTokenUsecaseProxy
      .getInstance()
      .execute(refreshTokenDto.refreshToken);

    // Atualizar cookies com novos tokens
    response.cookie('accessToken', result.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 15 * 60 * 1000 // 15 minutos
    });

    response.cookie('refreshToken', result.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000 // 7 dias
    });

    return new RefreshTokenPresenter(result);
  }

  @Post('set-password')
  @ApiResponse({
    status: HttpStatus.OK,
    type: UserPresenter,
    description: 'Password set successfully'
  })
  async setPassword(
    @Body() setPasswordDto: SetPasswordDto
  ): Promise<UserPresenter> {
    // Validar se as senhas coincidem
    if (setPasswordDto.password !== setPasswordDto.confirmPassword) {
      throw new Error('As senhas não coincidem');
    }

    const user = await this.setPasswordUsecaseProxy
      .getInstance()
      .execute(setPasswordDto);

    return new UserPresenter(user);
  }
}
