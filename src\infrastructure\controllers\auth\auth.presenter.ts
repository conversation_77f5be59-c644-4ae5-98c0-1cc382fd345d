import { ApiProperty } from '@nestjs/swagger';
import { UserEntity } from '../../../domain/entities/user.entity';

export class LoginPresenter {
  @ApiProperty()
  user: {
    id: string;
    name: string;
    email: string;
    institution_id?: string;
    is_email_verified: boolean;
    is_phone_verified: boolean;
    actived: boolean;
  };

  @ApiProperty()
  accessToken: string;

  @ApiProperty()
  refreshToken: string;

  constructor(data: {
    user: UserEntity;
    accessToken: string;
    refreshToken: string;
  }) {
    this.user = {
      id: data.user.id,
      name: data.user.name,
      email: data.user.email,
      institution_id: data.user.institution_id,
      is_email_verified: data.user.is_email_verified,
      is_phone_verified: data.user.is_phone_verified,
      actived: data.user.actived
    };
    this.accessToken = data.accessToken;
    this.refreshToken = data.refreshToken;
  }
}

export class RefreshTokenPresenter {
  @ApiProperty()
  user: {
    id: string;
    name: string;
    email: string;
    institution_id?: string;
  };

  @ApiProperty()
  accessToken: string;

  @ApiProperty()
  refreshToken: string;

  constructor(data: {
    user: UserEntity;
    accessToken: string;
    refreshToken: string;
  }) {
    this.user = {
      id: data.user.id,
      name: data.user.name,
      email: data.user.email,
      institution_id: data.user.institution_id
    };
    this.accessToken = data.accessToken;
    this.refreshToken = data.refreshToken;
  }
}

export class LogoutPresenter {
  @ApiProperty()
  message: string;

  constructor() {
    this.message = 'Logout realizado com sucesso';
  }
}
