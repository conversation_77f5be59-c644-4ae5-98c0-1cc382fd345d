import { ApiProperty } from '@nestjs/swagger';
import { InstitutionEntity } from '../../../domain/entities/institution.entity';
import { PlanEntity } from '../../../domain/entities/plan.entity';
import { SubscriptionEntity } from '../../../domain/entities/subscription.entity';
import { UserEntity } from '../../../domain/entities/user.entity';

export class LoginPresenter {
  @ApiProperty()
  user: {
    id: string;
    name: string;
    email: string;
    institution_id?: string;
    is_email_verified: boolean;
    is_phone_verified: boolean;
    actived: boolean;
  };

  @ApiProperty({ required: false })
  institution?: {
    id: string;
    name: string;
    legal_name: string;
    status: string;
    actived: boolean;
  };

  @ApiProperty({ required: false })
  subscription?: {
    id: string;
    status: string;
    current_period_start?: Date;
    current_period_end?: Date;
    trial_end?: Date;
  };

  @ApiProperty({ required: false })
  plan?: {
    id: string;
    name: string;
    description: string;
    price: number;
    currency: string;
    billing_period: string;
    max_users: number;
    max_institutions: number;
    features: Record<string, any>;
    is_active: boolean;
  };

  @ApiProperty()
  accessToken: string;

  @ApiProperty()
  refreshToken: string;

  constructor(data: {
    user: UserEntity;
    institution?: InstitutionEntity;
    subscription?: SubscriptionEntity;
    plan?: PlanEntity;
    accessToken: string;
    refreshToken: string;
  }) {
    this.user = {
      id: data.user.id,
      name: data.user.name,
      email: data.user.email,
      institution_id: data.user.institution_id,
      is_email_verified: data.user.is_email_verified,
      is_phone_verified: data.user.is_phone_verified,
      actived: data.user.actived
    };

    this.institution = data.institution
      ? {
          id: data.institution.id,
          name: data.institution.name,
          legal_name: data.institution.legal_name,
          status: data.institution.status,
          actived: data.institution.actived
        }
      : undefined;

    this.subscription = data.subscription
      ? {
          id: data.subscription.id,
          status: data.subscription.status,
          current_period_start: data.subscription.current_period_start,
          current_period_end: data.subscription.current_period_end,
          trial_end: data.subscription.trial_end
        }
      : undefined;

    this.plan = data.plan
      ? {
          id: data.plan.id,
          name: data.plan.name,
          description: data.plan.description,
          price: data.plan.price,
          currency: data.plan.currency,
          billing_period: data.plan.billing_period,
          max_users: data.plan.max_users,
          max_institutions: data.plan.max_institutions,
          features: data.plan.features,
          is_active: data.plan.is_active
        }
      : undefined;

    this.accessToken = data.accessToken;
    this.refreshToken = data.refreshToken;
  }
}

export class RefreshTokenPresenter {
  @ApiProperty()
  user: {
    id: string;
    name: string;
    email: string;
    institution_id?: string;
  };

  @ApiProperty()
  accessToken: string;

  @ApiProperty()
  refreshToken: string;

  constructor(data: {
    user: UserEntity;
    accessToken: string;
    refreshToken: string;
  }) {
    this.user = {
      id: data.user.id,
      name: data.user.name,
      email: data.user.email,
      institution_id: data.user.institution_id
    };
    this.accessToken = data.accessToken;
    this.refreshToken = data.refreshToken;
  }
}

export class LogoutPresenter {
  @ApiProperty()
  message: string;

  constructor() {
    this.message = 'Logout realizado com sucesso';
  }
}
