import {
    Body,
    Controller,
    Delete,
    Get,
    HttpStatus,
    Inject,
    Param,
    Post,
    Put,
    UseGuards,
    UseInterceptors
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiExtraModels,
    ApiResponse,
    ApiTags
} from '@nestjs/swagger';
import { CreateClassUseCase } from '../../../usecases/class/create-class.usecase';
import { DeleteClassUseCase } from '../../../usecases/class/delete-class.usecase';
import { GetAvailableClassesUseCase } from '../../../usecases/class/get-available-classes.usecase';
import { GetClassUseCase } from '../../../usecases/class/get-class.usecase';
import { GetClassesByCourseUseCase } from '../../../usecases/class/get-classes-by-course.usecase';
import { GetClassesUseCase } from '../../../usecases/class/get-classes.usecase';
import { UpdateClassUseCase } from '../../../usecases/class/update-class.usecase';
import {
    CurrentUser,
    CurrentUserData
} from '../../common/decorators/current-user.decorator';
import { RequirePermissions } from '../../common/decorators/require-permissions.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../../common/guards/permission.guard';
import { AuditInterceptor } from '../../common/interceptors/audit.interceptor';
import { ClassUsecasesProxy } from '../../usecases-proxy/class/class-usecases-proxy.constants';
import { UseCaseProxy } from '../../usecases-proxy/usecases-proxy';
import { CreateClassDto, UpdateClassDto } from './class.dto';
import { ClassPresenter } from './class.presenter';

@Controller('class')
@ApiTags('classes')
@ApiExtraModels(ClassPresenter)
export class ClassController {
  constructor(
    @Inject(ClassUsecasesProxy.GET_CLASS_USECASE_PROXY)
    private readonly getClassUsecaseProxy: UseCaseProxy<GetClassUseCase>,
    @Inject(ClassUsecasesProxy.GET_CLASSES_USECASE_PROXY)
    private readonly getClassesUsecaseProxy: UseCaseProxy<GetClassesUseCase>,
    @Inject(ClassUsecasesProxy.GET_CLASSES_BY_COURSE_USECASE_PROXY)
    private readonly getClassesByCourseUsecaseProxy: UseCaseProxy<GetClassesByCourseUseCase>,
    @Inject(ClassUsecasesProxy.GET_AVAILABLE_CLASSES_USECASE_PROXY)
    private readonly getAvailableClassesUsecaseProxy: UseCaseProxy<GetAvailableClassesUseCase>,
    @Inject(ClassUsecasesProxy.POST_CLASS_USECASE_PROXY)
    private readonly postClassUsecaseProxy: UseCaseProxy<CreateClassUseCase>,
    @Inject(ClassUsecasesProxy.PUT_CLASS_USECASE_PROXY)
    private readonly updateClassUsecaseProxy: UseCaseProxy<UpdateClassUseCase>,
    @Inject(ClassUsecasesProxy.DELETE_CLASS_USECASE_PROXY)
    private readonly deleteClassUsecaseProxy: UseCaseProxy<DeleteClassUseCase>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('class.create')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: ClassPresenter,
    description: 'Class created'
  })
  async createClass(
    @Body() body: CreateClassDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<ClassPresenter> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    const classData = {
      ...body,
      institution_id: user.institution_id, // Auto-fill from user
      created_by: user.sub
    };
    const classEntity = await this.postClassUsecaseProxy
      .getInstance()
      .execute(classData);
    return new ClassPresenter(classEntity);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('class.update')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: ClassPresenter,
    description: 'Class updated'
  })
  async updateClass(
    @Param('id') id: string,
    @Body() body: UpdateClassDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<ClassPresenter> {
    const updateData = {
      ...body,
      updated_by: user.sub
    };
    const classEntity = await this.updateClassUsecaseProxy
      .getInstance()
      .execute(id, updateData);
    return new ClassPresenter(classEntity);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('class.delete')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Class deleted'
  })
  async deleteClass(@Param('id') id: string): Promise<void> {
    await this.deleteClassUsecaseProxy.getInstance().execute(id);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('class.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: ClassPresenter,
    description: 'Class returned'
  })
  async getClass(
    @Param('id') id: string,
    @CurrentUser() user: CurrentUserData
  ): Promise<ClassPresenter> {
    const classEntity = await this.getClassUsecaseProxy
      .getInstance()
      .execute(id);

    // Verify multi-tenant access
    if (!user.institution_id || classEntity.institution_id !== user.institution_id) {
      throw new Error('Access denied: Class not found in your institution');
    }

    return new ClassPresenter(classEntity);
  }

  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('class.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [ClassPresenter],
    description: 'Classes returned'
  })
  async getClasses(@CurrentUser() user: CurrentUserData): Promise<ClassPresenter[]> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    // Use multi-tenant method to filter by institution
    const classes = await this.getClassesUsecaseProxy.getInstance().execute();
    // Filter by institution_id in the use case or repository level
    return classes
      .filter(classEntity => classEntity.institution_id === user.institution_id)
      .map(classEntity => new ClassPresenter(classEntity));
  }

  @Get('course/:courseId')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('class.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [ClassPresenter],
    description: 'Course classes returned'
  })
  async getClassesByCourse(
    @Param('courseId') courseId: string
  ): Promise<ClassPresenter[]> {
    const classes = await this.getClassesByCourseUsecaseProxy
      .getInstance()
      .execute(courseId);
    return classes.map(classEntity => new ClassPresenter(classEntity));
  }

  @Get('available/list')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('class.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [ClassPresenter],
    description: 'Available classes returned'
  })
  async getAvailableClasses(): Promise<ClassPresenter[]> {
    const classes = await this.getAvailableClassesUsecaseProxy
      .getInstance()
      .execute();
    return classes.map(classEntity => new ClassPresenter(classEntity));
  }
}
