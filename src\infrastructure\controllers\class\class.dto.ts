import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  Min,
  ValidateNested
} from 'class-validator';

export class ClassScheduleDto {
  @ApiProperty({
    description: 'Day of week (0=Sunday, 1=Monday, ..., 6=Saturday)',
    example: 1,
    minimum: 0,
    maximum: 6
  })
  @IsNumber()
  @Min(0)
  day_of_week: number;

  @ApiProperty({
    description: 'Start time in HH:mm format',
    example: '08:00'
  })
  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'Start time must be in HH:mm format'
  })
  start_time: string;

  @ApiProperty({
    description: 'End time in HH:mm format',
    example: '10:00'
  })
  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'End time must be in HH:mm format'
  })
  end_time: string;
}

export class CreateClassDto {
  @ApiProperty({ description: 'Course ID', example: 'uuid' })
  @IsNotEmpty()
  @IsUUID()
  course_id: string;

  @ApiPropertyOptional({
    description: 'Institution ID (auto-filled from user)',
    example: 'uuid'
  })
  @IsOptional()
  @IsUUID()
  institution_id?: string;

  @ApiProperty({
    description: 'Class name',
    example: 'Turma A - Matemática Básica'
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Class description',
    example: 'Turma matutina de matemática básica'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Maximum number of students', example: 25 })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  max_students: number;

  @ApiPropertyOptional({
    description: 'Class status',
    enum: ['open', 'closed', 'in_progress', 'finished'],
    example: 'open'
  })
  @IsOptional()
  @IsEnum(['open', 'closed', 'in_progress', 'finished'])
  status?: 'open' | 'closed' | 'in_progress' | 'finished';

  @ApiPropertyOptional({
    description: 'Class start date',
    example: '2024-02-01T08:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  start_date?: Date;

  @ApiPropertyOptional({
    description: 'Class end date',
    example: '2024-08-01T18:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  end_date?: Date;

  @ApiPropertyOptional({
    description: 'Class schedule (days and times)',
    type: [ClassScheduleDto]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClassScheduleDto)
  schedule?: ClassScheduleDto[];
}

export class UpdateClassDto {
  @ApiPropertyOptional({
    description: 'Class name',
    example: 'Turma B - Matemática Avançada'
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Class description',
    example: 'Turma vespertina de matemática avançada'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Maximum number of students',
    example: 30
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  max_students?: number;

  @ApiPropertyOptional({
    description: 'Class status',
    enum: ['open', 'closed', 'in_progress', 'finished'],
    example: 'in_progress'
  })
  @IsOptional()
  @IsEnum(['open', 'closed', 'in_progress', 'finished'])
  status?: 'open' | 'closed' | 'in_progress' | 'finished';

  @ApiPropertyOptional({
    description: 'Class start date',
    example: '2024-03-01T08:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  start_date?: Date;

  @ApiPropertyOptional({
    description: 'Class end date',
    example: '2024-09-01T18:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  end_date?: Date;

  @ApiPropertyOptional({
    description: 'Class schedule (days and times)',
    type: [ClassScheduleDto]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClassScheduleDto)
  schedule?: ClassScheduleDto[];
}
