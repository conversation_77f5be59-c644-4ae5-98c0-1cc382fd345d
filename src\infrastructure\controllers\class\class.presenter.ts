import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ClassEntity,
  ClassSchedule
} from '../../../domain/entities/class.entity';

export class ClassPresenter {
  @ApiProperty({ description: 'Class ID', example: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Course ID', example: 'uuid' })
  course_id: string;

  @ApiProperty({
    description: 'Class name',
    example: 'Turma A - Matemática Básica'
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Class description',
    example: 'Turma matutina de matemática básica'
  })
  description?: string;

  @ApiProperty({ description: 'Maximum number of students', example: 25 })
  max_students: number;

  @ApiProperty({
    description: 'Current number of enrolled students',
    example: 18
  })
  current_students: number;

  @ApiProperty({
    description: 'Class status',
    enum: ['open', 'closed', 'in_progress', 'finished'],
    example: 'open'
  })
  status: 'open' | 'closed' | 'in_progress' | 'finished';

  @ApiPropertyOptional({
    description: 'Class start date',
    example: '2024-02-01T08:00:00.000Z'
  })
  start_date?: Date;

  @ApiPropertyOptional({
    description: 'Class end date',
    example: '2024-08-01T18:00:00.000Z'
  })
  end_date?: Date;

  @ApiPropertyOptional({
    description: 'Class schedule (days and times)',
    example: [
      {
        day_of_week: 1,
        start_time: '08:00',
        end_time: '10:00'
      },
      {
        day_of_week: 3,
        start_time: '08:00',
        end_time: '10:00'
      }
    ]
  })
  schedule?: ClassSchedule[];

  @ApiProperty({ description: 'Created by user ID', example: 'uuid' })
  created_by: string;

  @ApiPropertyOptional({ description: 'Updated by user ID', example: 'uuid' })
  updated_by?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z'
  })
  created_at: Date;

  @ApiPropertyOptional({
    description: 'Update date',
    example: '2024-01-02T00:00:00.000Z'
  })
  updated_at?: Date;

  constructor(classEntity: ClassEntity) {
    this.id = classEntity.id;
    this.course_id = classEntity.course_id;
    this.name = classEntity.name;
    this.description = classEntity.description;
    this.max_students = classEntity.max_students;
    this.current_students = classEntity.current_students;
    this.status = classEntity.status;
    this.start_date = classEntity.start_date;
    this.end_date = classEntity.end_date;
    this.schedule = classEntity.schedule;
    this.created_by = classEntity.created_by;
    this.updated_by = classEntity.updated_by;
    this.created_at = classEntity.created_at;
    this.updated_at = classEntity.updated_at;
  }
}
