import { Modu<PERSON> } from '@nestjs/common';
import { UsecasesProxyModule } from '../usecases-proxy/usecases-proxy.module';
import { AddressController } from './addresss/address.controller';
import { InstitutionController } from './institution/institution.controller';
import { PermissionController } from './permission/permission.controller';
import { RolePermissionController } from './role-permission/role-permission.controller';
import { RoleController } from './role/role.controller';
import { UserRoleController } from './user-role/user-role.controller';
import { UserController } from './user/user.controller';

@Module({
  imports: [UsecasesProxyModule],
  controllers: [
    <PERSON>r<PERSON>ontroll<PERSON>,
    InstitutionController,
    AddressController,
    PermissionController,
    RoleController,
    RolePermissionController,
    UserRoleController
  ]
})
export class ControllersModule {}
