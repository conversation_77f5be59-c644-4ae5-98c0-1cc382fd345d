import { Module } from '@nestjs/common';
import { GuardsModule } from '../common/guards/guards.module';
import { ExceptionsModule } from '../exceptions/exceptions.module';
import { LoggerModule } from '../logger/logger.module';
import { PermissionServiceModule } from '../services/permission/permission.module';
import { UsecasesProxyModule } from '../usecases-proxy/usecases-proxy.module';
import { AddressController } from './addresss/address.controller';
import { AttendanceController } from './attendance/attendance.controller';
import { AuthController } from './auth/auth.controller';
import { ClassController } from './class/class.controller';
import { CourseController } from './course/course.controller';
import { EnrollmentController } from './enrollment/enrollment.controller';
import { FinancialModule } from './financial/financial.module';
import { GradeController } from './grade/grade.controller';
import { InstitutionController } from './institution/institution.controller';
import { PermissionController } from './permission/permission.controller';
import { PlanController } from './plan/plan.controller';
import { RolePermissionController } from './role-permission/role-permission.controller';
import { RoleController } from './role/role.controller';
import { SubscriptionController } from './subscription/subscription.controller';
import { UserRoleController } from './user-role/user-role.controller';
import { UserController } from './user/user.controller';

@Module({
  imports: [
    UsecasesProxyModule,
    GuardsModule,
    PermissionServiceModule,
    LoggerModule,
    ExceptionsModule,
    FinancialModule
  ],
  controllers: [
    UserController,
    AuthController,
    ClassController,
    CourseController,
    EnrollmentController,
    AttendanceController,
    GradeController,
    InstitutionController,
    AddressController,
    PermissionController,
    RoleController,
    RolePermissionController,
    UserRoleController,
    PlanController,
    SubscriptionController
  ]
})
export class ControllersModule {}
