import { Modu<PERSON> } from '@nestjs/common';
import { GuardsModule } from '../common/guards/guards.module';
import { UsecasesProxyModule } from '../usecases-proxy/usecases-proxy.module';
import { AddressController } from './addresss/address.controller';
import { Auth<PERSON><PERSON>roller } from './auth/auth.controller';
import { CourseController } from './course/course.controller';
import { InstitutionController } from './institution/institution.controller';
import { PermissionController } from './permission/permission.controller';
import { PlanController } from './plan/plan.controller';
import { RolePermissionController } from './role-permission/role-permission.controller';
import { RoleController } from './role/role.controller';
import { SubscriptionController } from './subscription/subscription.controller';
import { UserRoleController } from './user-role/user-role.controller';
import { UserController } from './user/user.controller';

@Module({
  imports: [UsecasesProxyModule, GuardsModule],
  controllers: [
    User<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    InstitutionController,
    AddressController,
    PermissionController,
    RoleController,
    RolePermissionController,
    UserRoleController,
    PlanController,
    SubscriptionController
  ]
})
export class ControllersModule {}
