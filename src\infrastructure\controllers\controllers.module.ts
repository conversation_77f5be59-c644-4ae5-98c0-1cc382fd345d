import { Modu<PERSON> } from '@nestjs/common';
import { UsecasesProxyModule } from '../usecases-proxy/usecases-proxy.module';
import { AddressController } from './addresss/address.controller';
import { InstitutionController } from './institution/institution.controller';
import { PermissionController } from './permission/permission.controller';
import { RoleController } from './role/role.controller';
import { UserController } from './user/user.controller';

@Module({
  imports: [UsecasesProxyModule],
  controllers: [
    User<PERSON>ontroller,
    InstitutionController,
    AddressController,
    PermissionController,
    RoleController
  ]
})
export class ControllersModule {}
