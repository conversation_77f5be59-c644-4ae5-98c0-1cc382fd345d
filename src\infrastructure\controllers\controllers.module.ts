import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { UsecasesProxyModule } from '../usecases-proxy/usecases-proxy.module';
import { AddressController } from './addresss/address.controller';
import { AuthController } from './auth/auth.controller';
import { InstitutionController } from './institution/institution.controller';
import { PermissionController } from './permission/permission.controller';
import { PlanController } from './plan/plan.controller';
import { RolePermissionController } from './role-permission/role-permission.controller';
import { RoleController } from './role/role.controller';
import { SubscriptionController } from './subscription/subscription.controller';
import { UserRoleController } from './user-role/user-role.controller';
import { UserController } from './user/user.controller';

@Module({
  imports: [UsecasesProxyModule],
  controllers: [
    User<PERSON>ontroll<PERSON>,
    AuthController,
    InstitutionController,
    AddressController,
    Permission<PERSON>ontroller,
    RoleController,
    RolePermissionController,
    <PERSON>r<PERSON><PERSON><PERSON><PERSON>roller,
    Plan<PERSON><PERSON>roller,
    SubscriptionController
  ]
})
export class ControllersModule {}
