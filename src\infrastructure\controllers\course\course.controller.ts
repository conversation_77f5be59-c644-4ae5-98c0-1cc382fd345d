import {
    Body,
    Controller,
    Delete,
    Get,
    HttpStatus,
    Inject,
    Param,
    Post,
    Put,
    UseGuards,
    UseInterceptors
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiExtraModels,
    ApiResponse,
    ApiTags
} from '@nestjs/swagger';
import { CreateCourseUseCase } from '../../../usecases/course/create-course.usecase';
import { DeleteCourseUseCase } from '../../../usecases/course/delete-course.usecase';
import { GetCourseUseCase } from '../../../usecases/course/get-course.usecase';
import { GetCoursesByInstitutionUseCase } from '../../../usecases/course/get-courses-by-institution.usecase';
import { GetCoursesUseCase } from '../../../usecases/course/get-courses.usecase';
import { UpdateCourseUseCase } from '../../../usecases/course/update-course.usecase';
import { CurrentUser, CurrentUserData } from '../../common/decorators/current-user.decorator';
import { RequirePermissions } from '../../common/decorators/require-permissions.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../../common/guards/permission.guard';
import { AuditInterceptor } from '../../common/interceptors/audit.interceptor';
import { CourseUsecasesProxy } from '../../usecases-proxy/course/course-usecases-proxy.constants';
import { UseCaseProxy } from '../../usecases-proxy/usecases-proxy';
import { CreateCourseDto, UpdateCourseDto } from './course.dto';
import { CoursePresenter } from './course.presenter';

@Controller('course')
@ApiTags('courses')
@ApiExtraModels(CoursePresenter)
export class CourseController {
  constructor(
    @Inject(CourseUsecasesProxy.GET_COURSE_USECASE_PROXY)
    private readonly getCourseUsecaseProxy: UseCaseProxy<GetCourseUseCase>,
    @Inject(CourseUsecasesProxy.GET_COURSES_USECASE_PROXY)
    private readonly getCoursesUsecaseProxy: UseCaseProxy<GetCoursesUseCase>,
    @Inject(CourseUsecasesProxy.GET_COURSES_BY_INSTITUTION_USECASE_PROXY)
    private readonly getCoursesByInstitutionUsecaseProxy: UseCaseProxy<GetCoursesByInstitutionUseCase>,
    @Inject(CourseUsecasesProxy.POST_COURSE_USECASE_PROXY)
    private readonly postCourseUsecaseProxy: UseCaseProxy<CreateCourseUseCase>,
    @Inject(CourseUsecasesProxy.PUT_COURSE_USECASE_PROXY)
    private readonly updateCourseUsecaseProxy: UseCaseProxy<UpdateCourseUseCase>,
    @Inject(CourseUsecasesProxy.DELETE_COURSE_USECASE_PROXY)
    private readonly deleteCourseUsecaseProxy: UseCaseProxy<DeleteCourseUseCase>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.create')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: CoursePresenter,
    description: 'Course created'
  })
  async createCourse(
    @Body() body: CreateCourseDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<CoursePresenter> {
    const courseData = {
      ...body,
      created_by: user.sub
    };
    const course = await this.postCourseUsecaseProxy.getInstance().execute(courseData);
    return new CoursePresenter(course);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.update')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: CoursePresenter,
    description: 'Course updated'
  })
  async updateCourse(
    @Param('id') id: string,
    @Body() body: UpdateCourseDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<CoursePresenter> {
    const updateData = {
      ...body,
      updated_by: user.sub
    };
    const course = await this.updateCourseUsecaseProxy
      .getInstance()
      .execute(id, updateData);
    return new CoursePresenter(course);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.delete')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Course deleted'
  })
  async deleteCourse(@Param('id') id: string): Promise<void> {
    await this.deleteCourseUsecaseProxy.getInstance().execute(id);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: CoursePresenter,
    description: 'Course returned'
  })
  async getCourse(@Param('id') id: string): Promise<CoursePresenter> {
    const course = await this.getCourseUsecaseProxy.getInstance().execute(id);
    return new CoursePresenter(course);
  }

  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [CoursePresenter],
    description: 'Courses returned'
  })
  async getCourses(): Promise<CoursePresenter[]> {
    const courses = await this.getCoursesUsecaseProxy.getInstance().execute();
    return courses.map(course => new CoursePresenter(course));
  }

  @Get('institution/:institutionId')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('course.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [CoursePresenter],
    description: 'Institution courses returned'
  })
  async getCoursesByInstitution(
    @Param('institutionId') institutionId: string
  ): Promise<CoursePresenter[]> {
    const courses = await this.getCoursesByInstitutionUsecaseProxy
      .getInstance()
      .execute(institutionId);
    return courses.map(course => new CoursePresenter(course));
  }
}
