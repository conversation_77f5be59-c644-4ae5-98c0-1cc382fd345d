import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Min,
  IsDateString
} from 'class-validator';

export class CreateCourseDto {
  @ApiProperty({ description: 'Institution ID', example: 'uuid' })
  @IsNotEmpty()
  @IsUUID()
  institution_id: string;

  @ApiProperty({ description: 'Course name', example: 'Matemática Básica' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Course description',
    example: 'Curso de matemática básica para ensino fundamental'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Course duration in months', example: 6 })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  duration_months: number;

  @ApiProperty({ description: 'Course price', example: 299.99 })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty({ description: 'Maximum number of students', example: 30 })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  max_students: number;

  @ApiPropertyOptional({
    description: 'Course status',
    enum: ['active', 'inactive', 'completed', 'cancelled'],
    example: 'active'
  })
  @IsOptional()
  @IsEnum(['active', 'inactive', 'completed', 'cancelled'])
  status?: 'active' | 'inactive' | 'completed' | 'cancelled';

  @ApiPropertyOptional({
    description: 'Course start date',
    example: '2024-01-15T00:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  start_date?: Date;

  @ApiPropertyOptional({
    description: 'Course end date',
    example: '2024-07-15T00:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  end_date?: Date;
}

export class UpdateCourseDto {
  @ApiPropertyOptional({
    description: 'Course name',
    example: 'Matemática Avançada'
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Course description',
    example: 'Curso de matemática avançada'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Course duration in months', example: 8 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  duration_months?: number;

  @ApiPropertyOptional({ description: 'Course price', example: 399.99 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  price?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of students',
    example: 25
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  max_students?: number;

  @ApiPropertyOptional({
    description: 'Course status',
    enum: ['active', 'inactive', 'completed', 'cancelled'],
    example: 'inactive'
  })
  @IsOptional()
  @IsEnum(['active', 'inactive', 'completed', 'cancelled'])
  status?: 'active' | 'inactive' | 'completed' | 'cancelled';

  @ApiPropertyOptional({
    description: 'Course start date',
    example: '2024-02-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  start_date?: Date;

  @ApiPropertyOptional({
    description: 'Course end date',
    example: '2024-10-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  end_date?: Date;
}
