import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Min
} from 'class-validator';

export class CreateCourseDto {
  @ApiProperty({ description: 'Course name', example: 'Matemática Básica' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Course description',
    example: 'Curso de matemática básica para ensino fundamental'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Course code', example: 'MAT001' })
  @IsNotEmpty()
  @IsString()
  code: string;

  @ApiProperty({ description: 'Course credits', example: 4 })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  credits: number;

  @ApiProperty({ description: 'Course duration in hours', example: 60 })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  duration_hours: number;

  @ApiPropertyOptional({
    description: 'Course status',
    enum: ['active', 'inactive', 'archived'],
    example: 'active'
  })
  @IsOptional()
  @IsEnum(['active', 'inactive', 'archived'])
  status?: 'active' | 'inactive' | 'archived';

  @ApiProperty({ description: 'Institution ID', example: 'uuid' })
  @IsNotEmpty()
  @IsUUID()
  institution_id: string;
}

export class UpdateCourseDto {
  @ApiPropertyOptional({
    description: 'Course name',
    example: 'Matemática Avançada'
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Course description',
    example: 'Curso de matemática avançada'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Course code', example: 'MAT002' })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiPropertyOptional({ description: 'Course credits', example: 6 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  credits?: number;

  @ApiPropertyOptional({ description: 'Course duration in hours', example: 80 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  duration_hours?: number;

  @ApiPropertyOptional({
    description: 'Course status',
    enum: ['active', 'inactive', 'archived'],
    example: 'inactive'
  })
  @IsOptional()
  @IsEnum(['active', 'inactive', 'archived'])
  status?: 'active' | 'inactive' | 'archived';
}
