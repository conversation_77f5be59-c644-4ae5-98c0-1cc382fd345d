import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Course } from '../../../domain/entities/course.entity';

export class CoursePresenter {
  @ApiProperty({ description: 'Course ID', example: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Course name', example: 'Matemática Básica' })
  name: string;

  @ApiPropertyOptional({ description: 'Course description', example: 'Curso de matemática básica para ensino fundamental' })
  description: string;

  @ApiProperty({ description: 'Course code', example: 'MAT001' })
  code: string;

  @ApiProperty({ description: 'Course credits', example: 4 })
  credits: number;

  @ApiProperty({ description: 'Course duration in hours', example: 60 })
  duration_hours: number;

  @ApiProperty({ 
    description: 'Course status', 
    enum: ['active', 'inactive', 'archived'],
    example: 'active'
  })
  status: 'active' | 'inactive' | 'archived';

  @ApiProperty({ description: 'Institution ID', example: 'uuid' })
  institution_id: string;

  @ApiProperty({ description: 'Created by user ID', example: 'uuid' })
  created_by: string;

  @ApiPropertyOptional({ description: 'Updated by user ID', example: 'uuid' })
  updated_by?: string;

  @ApiProperty({ description: 'Creation date', example: '2024-01-01T00:00:00.000Z' })
  created_at: Date;

  @ApiPropertyOptional({ description: 'Update date', example: '2024-01-02T00:00:00.000Z' })
  updated_at?: Date;

  constructor(course: Course) {
    this.id = course.id;
    this.name = course.name;
    this.description = course.description;
    this.code = course.code;
    this.credits = course.credits;
    this.duration_hours = course.duration_hours;
    this.status = course.status;
    this.institution_id = course.institution_id;
    this.created_by = course.created_by;
    this.updated_by = course.updated_by;
    this.created_at = course.created_at;
    this.updated_at = course.updated_at;
  }
}
