import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CourseEntity } from '../../../domain/entities/course.entity';

export class CoursePresenter {
  @ApiProperty({ description: 'Course ID', example: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Institution ID', example: 'uuid' })
  institution_id: string;

  @ApiProperty({ description: 'Course name', example: 'Matemática Básica' })
  name: string;

  @ApiPropertyOptional({
    description: 'Course description',
    example: 'Curso de matemática básica para ensino fundamental'
  })
  description?: string;

  @ApiProperty({ description: 'Course duration in months', example: 6 })
  duration_months: number;

  @ApiProperty({ description: 'Course price', example: 299.99 })
  price: number;

  @ApiProperty({ description: 'Maximum number of students', example: 30 })
  max_students: number;

  @ApiProperty({
    description: 'Course status',
    enum: ['active', 'inactive', 'completed', 'cancelled'],
    example: 'active'
  })
  status: 'active' | 'inactive' | 'completed' | 'cancelled';

  @ApiPropertyOptional({
    description: 'Course start date',
    example: '2024-01-15T00:00:00.000Z'
  })
  start_date?: Date;

  @ApiPropertyOptional({
    description: 'Course end date',
    example: '2024-07-15T00:00:00.000Z'
  })
  end_date?: Date;

  @ApiProperty({ description: 'Created by user ID', example: 'uuid' })
  created_by: string;

  @ApiPropertyOptional({ description: 'Updated by user ID', example: 'uuid' })
  updated_by?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z'
  })
  created_at: Date;

  @ApiPropertyOptional({
    description: 'Update date',
    example: '2024-01-02T00:00:00.000Z'
  })
  updated_at?: Date;

  constructor(course: CourseEntity) {
    this.id = course.id;
    this.institution_id = course.institution_id;
    this.name = course.name;
    this.description = course.description;
    this.duration_months = course.duration_months;
    this.price = course.price;
    this.max_students = course.max_students;
    this.status = course.status;
    this.start_date = course.start_date;
    this.end_date = course.end_date;
    this.created_by = course.created_by;
    this.updated_by = course.updated_by;
    this.created_at = course.created_at;
    this.updated_at = course.updated_at;
  }
}
