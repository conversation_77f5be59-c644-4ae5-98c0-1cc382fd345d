import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  UseGuards,
  UseInterceptors
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { ApproveEnrollmentUseCase } from '../../../usecases/enrollment/approve-enrollment.usecase';
import { CancelEnrollmentUseCase } from '../../../usecases/enrollment/cancel-enrollment.usecase';
import { CreateEnrollmentUseCase } from '../../../usecases/enrollment/create-enrollment.usecase';
import { DeleteEnrollmentUseCase } from '../../../usecases/enrollment/delete-enrollment.usecase';
import { GetEnrollmentUseCase } from '../../../usecases/enrollment/get-enrollment.usecase';
import { GetEnrollmentsByClassUseCase } from '../../../usecases/enrollment/get-enrollments-by-class.usecase';
import { GetEnrollmentsByStudentUseCase } from '../../../usecases/enrollment/get-enrollments-by-student.usecase';
import { GetEnrollmentsUseCase } from '../../../usecases/enrollment/get-enrollments.usecase';
import { GetPendingEnrollmentsUseCase } from '../../../usecases/enrollment/get-pending-enrollments.usecase';
import { RejectEnrollmentUseCase } from '../../../usecases/enrollment/reject-enrollment.usecase';
import {
  CurrentUser,
  CurrentUserData
} from '../../common/decorators/current-user.decorator';
import { RequirePermissions } from '../../common/decorators/require-permissions.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../../common/guards/permission.guard';
import { AuditInterceptor } from '../../common/interceptors/audit.interceptor';
import { EnrollmentUsecasesProxy } from '../../usecases-proxy/enrollment/enrollment-usecases-proxy.constants';
import { UseCaseProxy } from '../../usecases-proxy/usecases-proxy';
import {
  ApproveEnrollmentDto,
  CancelEnrollmentDto,
  CreateEnrollmentDto,
  RejectEnrollmentDto
} from './enrollment.dto';
import { EnrollmentPresenter } from './enrollment.presenter';

@Controller('enrollment')
@ApiTags('Enrollment')
@ApiExtraModels(EnrollmentPresenter)
export class EnrollmentController {
  constructor(
    @Inject(EnrollmentUsecasesProxy.GET_ENROLLMENT_USECASE_PROXY)
    private readonly getEnrollmentUsecaseProxy: UseCaseProxy<GetEnrollmentUseCase>,
    @Inject(EnrollmentUsecasesProxy.GET_ENROLLMENTS_USECASE_PROXY)
    private readonly getEnrollmentsUsecaseProxy: UseCaseProxy<GetEnrollmentsUseCase>,
    @Inject(EnrollmentUsecasesProxy.GET_ENROLLMENTS_BY_STUDENT_USECASE_PROXY)
    private readonly getEnrollmentsByStudentUsecaseProxy: UseCaseProxy<GetEnrollmentsByStudentUseCase>,
    @Inject(EnrollmentUsecasesProxy.GET_ENROLLMENTS_BY_CLASS_USECASE_PROXY)
    private readonly getEnrollmentsByClassUsecaseProxy: UseCaseProxy<GetEnrollmentsByClassUseCase>,
    @Inject(EnrollmentUsecasesProxy.GET_PENDING_ENROLLMENTS_USECASE_PROXY)
    private readonly getPendingEnrollmentsUsecaseProxy: UseCaseProxy<GetPendingEnrollmentsUseCase>,
    @Inject(EnrollmentUsecasesProxy.POST_ENROLLMENT_USECASE_PROXY)
    private readonly postEnrollmentUsecaseProxy: UseCaseProxy<CreateEnrollmentUseCase>,
    @Inject(EnrollmentUsecasesProxy.DELETE_ENROLLMENT_USECASE_PROXY)
    private readonly deleteEnrollmentUsecaseProxy: UseCaseProxy<DeleteEnrollmentUseCase>,
    @Inject(EnrollmentUsecasesProxy.APPROVE_ENROLLMENT_USECASE_PROXY)
    private readonly approveEnrollmentUsecaseProxy: UseCaseProxy<ApproveEnrollmentUseCase>,
    @Inject(EnrollmentUsecasesProxy.REJECT_ENROLLMENT_USECASE_PROXY)
    private readonly rejectEnrollmentUsecaseProxy: UseCaseProxy<RejectEnrollmentUseCase>,
    @Inject(EnrollmentUsecasesProxy.CANCEL_ENROLLMENT_USECASE_PROXY)
    private readonly cancelEnrollmentUsecaseProxy: UseCaseProxy<CancelEnrollmentUseCase>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.create')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: EnrollmentPresenter,
    description: 'Enrollment created'
  })
  async createEnrollment(
    @Body() body: CreateEnrollmentDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<EnrollmentPresenter> {
    const enrollmentData = {
      ...body,
      created_by: user.sub
    };
    const enrollment = await this.postEnrollmentUsecaseProxy
      .getInstance()
      .execute(enrollmentData);
    return new EnrollmentPresenter(enrollment);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.delete')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Enrollment deleted'
  })
  async deleteEnrollment(@Param('id') id: string): Promise<void> {
    await this.deleteEnrollmentUsecaseProxy.getInstance().execute(id);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: EnrollmentPresenter,
    description: 'Enrollment returned'
  })
  async getEnrollment(@Param('id') id: string): Promise<EnrollmentPresenter> {
    const enrollment = await this.getEnrollmentUsecaseProxy
      .getInstance()
      .execute(id);
    return new EnrollmentPresenter(enrollment);
  }

  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [EnrollmentPresenter],
    description: 'Enrollments returned'
  })
  async getEnrollments(): Promise<EnrollmentPresenter[]> {
    const enrollments = await this.getEnrollmentsUsecaseProxy
      .getInstance()
      .execute();
    return enrollments.map(enrollment => new EnrollmentPresenter(enrollment));
  }

  @Get('student/:studentId')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [EnrollmentPresenter],
    description: 'Student enrollments returned'
  })
  async getEnrollmentsByStudent(
    @Param('studentId') studentId: string
  ): Promise<EnrollmentPresenter[]> {
    const enrollments = await this.getEnrollmentsByStudentUsecaseProxy
      .getInstance()
      .execute(studentId);
    return enrollments.map(enrollment => new EnrollmentPresenter(enrollment));
  }

  @Get('class/:classId')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [EnrollmentPresenter],
    description: 'Class enrollments returned'
  })
  async getEnrollmentsByClass(
    @Param('classId') classId: string
  ): Promise<EnrollmentPresenter[]> {
    const enrollments = await this.getEnrollmentsByClassUsecaseProxy
      .getInstance()
      .execute(classId);
    return enrollments.map(enrollment => new EnrollmentPresenter(enrollment));
  }

  @Get('pending/list')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [EnrollmentPresenter],
    description: 'Pending enrollments returned'
  })
  async getPendingEnrollments(): Promise<EnrollmentPresenter[]> {
    const enrollments = await this.getPendingEnrollmentsUsecaseProxy
      .getInstance()
      .execute();
    return enrollments.map(enrollment => new EnrollmentPresenter(enrollment));
  }

  @Put(':id/approve')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.approve')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: EnrollmentPresenter,
    description: 'Enrollment approved'
  })
  async approveEnrollment(
    @Param('id') id: string,
    @Body() body: ApproveEnrollmentDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<EnrollmentPresenter> {
    const approvalData = {
      id,
      approved_by: user.sub,
      notes: body.notes
    };
    const enrollment = await this.approveEnrollmentUsecaseProxy
      .getInstance()
      .execute(approvalData);
    return new EnrollmentPresenter(enrollment);
  }

  @Put(':id/reject')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.reject')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: EnrollmentPresenter,
    description: 'Enrollment rejected'
  })
  async rejectEnrollment(
    @Param('id') id: string,
    @Body() body: RejectEnrollmentDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<EnrollmentPresenter> {
    const rejectionData = {
      id,
      rejection_reason: body.rejection_reason,
      updated_by: user.sub
    };
    const enrollment = await this.rejectEnrollmentUsecaseProxy
      .getInstance()
      .execute(rejectionData);
    return new EnrollmentPresenter(enrollment);
  }

  @Put(':id/cancel')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('enrollment.cancel')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: EnrollmentPresenter,
    description: 'Enrollment cancelled'
  })
  async cancelEnrollment(
    @Param('id') id: string,
    @Body() body: CancelEnrollmentDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<EnrollmentPresenter> {
    const cancellationData = {
      id,
      updated_by: user.sub,
      notes: body.notes
    };
    const enrollment = await this.cancelEnrollmentUsecaseProxy
      .getInstance()
      .execute(cancellationData);
    return new EnrollmentPresenter(enrollment);
  }
}
