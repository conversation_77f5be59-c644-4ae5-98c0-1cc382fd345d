import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID
} from 'class-validator';

export class CreateEnrollmentDto {
  @ApiProperty({ description: 'Student ID', example: 'uuid' })
  @IsNotEmpty()
  @IsUUID()
  student_id: string;

  @ApiProperty({ description: 'Class ID', example: 'uuid' })
  @IsNotEmpty()
  @IsUUID()
  class_id: string;

  @ApiProperty({ description: 'Institution ID', example: 'uuid' })
  @IsNotEmpty()
  @IsUUID()
  institution_id: string;

  @ApiPropertyOptional({
    description: 'Enrollment status',
    enum: ['pending', 'approved', 'rejected', 'cancelled', 'completed'],
    example: 'pending'
  })
  @IsOptional()
  @IsEnum(['pending', 'approved', 'rejected', 'cancelled', 'completed'])
  status?: 'pending' | 'approved' | 'rejected' | 'cancelled' | 'completed';

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Student has special requirements'
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class ApproveEnrollmentDto {
  @ApiPropertyOptional({
    description: 'Approval notes',
    example: 'Student meets all requirements'
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class RejectEnrollmentDto {
  @ApiProperty({
    description: 'Reason for rejection',
    example: 'Student does not meet prerequisites'
  })
  @IsNotEmpty()
  @IsString()
  rejection_reason: string;
}

export class CancelEnrollmentDto {
  @ApiPropertyOptional({
    description: 'Cancellation notes',
    example: 'Student requested cancellation'
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
