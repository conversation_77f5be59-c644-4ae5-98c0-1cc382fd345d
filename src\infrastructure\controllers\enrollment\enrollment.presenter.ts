import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EnrollmentEntity } from '../../../domain/entities/enrollment.entity';

export class EnrollmentPresenter {
  @ApiProperty({ description: 'Enrollment ID', example: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Student ID', example: 'uuid' })
  student_id: string;

  @ApiProperty({ description: 'Class ID', example: 'uuid' })
  class_id: string;

  @ApiProperty({ description: 'Institution ID', example: 'uuid' })
  institution_id: string;

  @ApiProperty({
    description: 'Enrollment status',
    enum: ['pending', 'approved', 'rejected', 'cancelled', 'completed'],
    example: 'pending'
  })
  status: 'pending' | 'approved' | 'rejected' | 'cancelled' | 'completed';

  @ApiProperty({
    description: 'Enrollment date',
    example: '2024-01-15T10:00:00.000Z'
  })
  enrollment_date: Date;

  @ApiPropertyOptional({
    description: 'Approval date',
    example: '2024-01-16T14:30:00.000Z'
  })
  approval_date?: Date;

  @ApiPropertyOptional({
    description: 'Approved by user ID',
    example: 'uuid'
  })
  approved_by?: string;

  @ApiPropertyOptional({
    description: 'Rejection reason',
    example: 'Student does not meet prerequisites'
  })
  rejection_reason?: string;

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Student has special requirements'
  })
  notes?: string;

  @ApiPropertyOptional({ description: 'Created by user ID', example: 'uuid' })
  created_by?: string;

  @ApiPropertyOptional({ description: 'Updated by user ID', example: 'uuid' })
  updated_by?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z'
  })
  created_at: Date;

  @ApiPropertyOptional({
    description: 'Update date',
    example: '2024-01-02T00:00:00.000Z'
  })
  updated_at?: Date;

  constructor(enrollment: EnrollmentEntity) {
    this.id = enrollment.id;
    this.student_id = enrollment.student_id;
    this.class_id = enrollment.class_id;
    this.institution_id = enrollment.institution_id;
    this.status = enrollment.status;
    this.enrollment_date = enrollment.enrollment_date;
    this.approval_date = enrollment.approval_date;
    this.approved_by = enrollment.approved_by;
    this.rejection_reason = enrollment.rejection_reason;
    this.notes = enrollment.notes;
    this.created_by = enrollment.created_by;
    this.updated_by = enrollment.updated_by;
    this.created_at = enrollment.created_at;
    this.updated_at = enrollment.updated_at;
  }
}
