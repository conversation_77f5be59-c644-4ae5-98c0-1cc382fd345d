import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiQuery,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { CreateFinancialTransactionUseCase } from '../../../usecases/financial/create-financial-transaction.usecase';
import { CreateInvoiceUseCase } from '../../../usecases/financial/create-invoice.usecase';
import { GetFinancialSummaryUseCase } from '../../../usecases/financial/get-financial-summary.usecase';
import { GetFinancialTransactionsUseCase } from '../../../usecases/financial/get-financial-transactions.usecase';
import { ProcessInvoicePaymentUseCase } from '../../../usecases/financial/process-invoice-payment.usecase';
import { UpdateFinancialTransactionUseCase } from '../../../usecases/financial/update-financial-transaction.usecase';
import {
  CurrentUser,
  CurrentUserData
} from '../../common/decorators/current-user.decorator';
import { RequirePermissions } from '../../common/decorators/require-permissions.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../../common/guards/permission.guard';
import { FinancialUsecasesProxy } from '../../usecases-proxy/financial/financial-usecases-proxy.constants';
import { UseCaseProxy } from '../../usecases-proxy/usecases-proxy';
import {
  CreateFinancialTransactionDto,
  CreateInvoiceDto,
  ProcessInvoicePaymentDto,
  UpdateFinancialTransactionDto
} from './financial.dto';
import {
  FinancialSummaryPresenter,
  FinancialTransactionPresenter,
  InvoicePresenter,
  PaymentMethodPresenter
} from './financial.presenter';

@Controller('financial')
@ApiTags('Financial')
@ApiExtraModels(
  FinancialTransactionPresenter,
  InvoicePresenter,
  PaymentMethodPresenter,
  FinancialSummaryPresenter
)
export class FinancialController {
  constructor(
    @Inject(FinancialUsecasesProxy.CREATE_FINANCIAL_TRANSACTION_USECASE_PROXY)
    private readonly createFinancialTransactionUsecaseProxy: UseCaseProxy<CreateFinancialTransactionUseCase>,
    @Inject(FinancialUsecasesProxy.GET_FINANCIAL_TRANSACTIONS_USECASE_PROXY)
    private readonly getFinancialTransactionsUsecaseProxy: UseCaseProxy<GetFinancialTransactionsUseCase>,
    @Inject(FinancialUsecasesProxy.UPDATE_FINANCIAL_TRANSACTION_USECASE_PROXY)
    private readonly updateFinancialTransactionUsecaseProxy: UseCaseProxy<UpdateFinancialTransactionUseCase>,
    @Inject(FinancialUsecasesProxy.GET_FINANCIAL_SUMMARY_USECASE_PROXY)
    private readonly getFinancialSummaryUsecaseProxy: UseCaseProxy<GetFinancialSummaryUseCase>,
    @Inject(FinancialUsecasesProxy.CREATE_INVOICE_USECASE_PROXY)
    private readonly createInvoiceUsecaseProxy: UseCaseProxy<CreateInvoiceUseCase>,
    @Inject(FinancialUsecasesProxy.PROCESS_INVOICE_PAYMENT_USECASE_PROXY)
    private readonly processInvoicePaymentUsecaseProxy: UseCaseProxy<ProcessInvoicePaymentUseCase>
  ) {}

  // ==================== FINANCIAL TRANSACTIONS ====================

  @Post('transactions')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('financial.create')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: FinancialTransactionPresenter,
    description: 'Financial transaction created successfully'
  })
  async createTransaction(
    @Body() body: CreateFinancialTransactionDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<FinancialTransactionPresenter> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    const transaction = await this.createFinancialTransactionUsecaseProxy
      .getInstance()
      .execute({
        ...body,
        institution_id: user.institution_id,
        transaction_date: new Date(body.transaction_date),
        due_date: body.due_date ? new Date(body.due_date) : undefined,
        created_by: user.sub
      });

    return new FinancialTransactionPresenter(transaction);
  }

  @Get('transactions')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('financial.read')
  @ApiBearerAuth('JWT-auth')
  @ApiQuery({ name: 'type', required: false, enum: ['income', 'expense'] })
  @ApiQuery({ name: 'category', required: false })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['pending', 'completed', 'cancelled']
  })
  @ApiQuery({ name: 'start_date', required: false, type: String })
  @ApiQuery({ name: 'end_date', required: false, type: String })
  @ApiQuery({ name: 'reference_id', required: false })
  @ApiQuery({ name: 'reference_type', required: false })
  @ApiResponse({
    status: HttpStatus.OK,
    type: [FinancialTransactionPresenter],
    description: 'Financial transactions retrieved successfully'
  })
  async getTransactions(
    @CurrentUser() user: CurrentUserData,
    @Query('type') type?: 'income' | 'expense',
    @Query('category') category?: string,
    @Query('status') status?: string,
    @Query('start_date') startDate?: string,
    @Query('end_date') endDate?: string,
    @Query('reference_id') referenceId?: string,
    @Query('reference_type') referenceType?: string
  ): Promise<FinancialTransactionPresenter[]> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    const transactions = await this.getFinancialTransactionsUsecaseProxy
      .getInstance()
      .execute({
        institution_id: user.institution_id,
        type,
        category,
        status,
        start_date: startDate ? new Date(startDate) : undefined,
        end_date: endDate ? new Date(endDate) : undefined,
        reference_id: referenceId,
        reference_type: referenceType
      });

    return transactions.map(
      transaction => new FinancialTransactionPresenter(transaction)
    );
  }

  @Get('transactions/:id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('financial.read')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: FinancialTransactionPresenter,
    description: 'Financial transaction retrieved successfully'
  })
  async getTransactionById(
    @Param('id') id: string,
    @CurrentUser() user: CurrentUserData
  ): Promise<FinancialTransactionPresenter> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    // Implementar use case para buscar por ID
    // Por simplicidade, usando o repositório diretamente aqui
    throw new Error('Not implemented yet');
  }

  @Put('transactions/:id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('financial.update')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: FinancialTransactionPresenter,
    description: 'Financial transaction updated successfully'
  })
  async updateTransaction(
    @Param('id') id: string,
    @Body() body: UpdateFinancialTransactionDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<FinancialTransactionPresenter> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    const transaction = await this.updateFinancialTransactionUsecaseProxy
      .getInstance()
      .execute({
        id,
        institution_id: user.institution_id,
        ...body,
        transaction_date: body.transaction_date
          ? new Date(body.transaction_date)
          : undefined,
        due_date: body.due_date ? new Date(body.due_date) : undefined,
        paid_date: body.paid_date ? new Date(body.paid_date) : undefined,
        updated_by: user.sub
      });

    return new FinancialTransactionPresenter(transaction);
  }

  @Get('summary')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('financial.read')
  @ApiBearerAuth('JWT-auth')
  @ApiQuery({ name: 'start_date', required: false, type: String })
  @ApiQuery({ name: 'end_date', required: false, type: String })
  @ApiResponse({
    status: HttpStatus.OK,
    type: FinancialSummaryPresenter,
    description: 'Financial summary retrieved successfully'
  })
  async getFinancialSummary(
    @CurrentUser() user: CurrentUserData,
    @Query('start_date') startDate?: string,
    @Query('end_date') endDate?: string
  ): Promise<FinancialSummaryPresenter> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    const summary = await this.getFinancialSummaryUsecaseProxy
      .getInstance()
      .execute({
        institution_id: user.institution_id,
        start_date: startDate ? new Date(startDate) : undefined,
        end_date: endDate ? new Date(endDate) : undefined
      });

    return new FinancialSummaryPresenter(summary);
  }

  // ==================== INVOICES ====================

  @Post('invoices')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('financial.create')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: InvoicePresenter,
    description: 'Invoice created successfully'
  })
  async createInvoice(
    @Body() body: CreateInvoiceDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<InvoicePresenter> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    const invoice = await this.createInvoiceUsecaseProxy.getInstance().execute({
      ...body,
      institution_id: user.institution_id,
      due_date: new Date(body.due_date),
      created_by: user.sub
    });

    return new InvoicePresenter(invoice);
  }

  @Post('invoices/:id/payment')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('financial.update')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: InvoicePresenter,
    description: 'Invoice payment processed successfully'
  })
  async processInvoicePayment(
    @Param('id') id: string,
    @Body() body: ProcessInvoicePaymentDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<InvoicePresenter> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    const invoice = await this.processInvoicePaymentUsecaseProxy
      .getInstance()
      .execute({
        invoice_id: id,
        institution_id: user.institution_id,
        ...body,
        payment_date: body.payment_date
          ? new Date(body.payment_date)
          : undefined,
        processed_by: user.sub
      });

    return new InvoicePresenter(invoice);
  }
}
