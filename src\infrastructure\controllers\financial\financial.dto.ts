import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  IsObject,
  IsPositive,
  Min
} from 'class-validator';

export class CreateFinancialTransactionDto {
  @ApiProperty({
    description: 'Transaction type',
    enum: ['income', 'expense'],
    example: 'income'
  })
  @IsNotEmpty()
  @IsEnum(['income', 'expense'])
  type: 'income' | 'expense';

  @ApiProperty({
    description: 'Transaction category',
    enum: [
      'enrollment_fee',
      'monthly_fee',
      'material',
      'salary',
      'infrastructure',
      'other'
    ],
    example: 'monthly_fee'
  })
  @IsNotEmpty()
  @IsEnum([
    'enrollment_fee',
    'monthly_fee',
    'material',
    'salary',
    'infrastructure',
    'other'
  ])
  category:
    | 'enrollment_fee'
    | 'monthly_fee'
    | 'material'
    | 'salary'
    | 'infrastructure'
    | 'other';

  @ApiProperty({
    description: 'Transaction amount',
    example: 299.99,
    minimum: 0.01
  })
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  amount: number;

  @ApiProperty({
    description: 'Transaction description',
    example: 'Monthly fee payment for January 2024'
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiPropertyOptional({
    description: 'Reference ID (e.g., enrollment ID)',
    example: 'uuid'
  })
  @IsOptional()
  @IsUUID()
  reference_id?: string;

  @ApiPropertyOptional({
    description: 'Reference type (e.g., enrollment, course)',
    example: 'enrollment'
  })
  @IsOptional()
  @IsString()
  reference_type?: string;

  @ApiPropertyOptional({
    description: 'Payment method ID',
    example: 'uuid'
  })
  @IsOptional()
  @IsUUID()
  payment_method_id?: string;

  @ApiProperty({
    description: 'Transaction date',
    example: '2024-01-15T10:30:00.000Z'
  })
  @IsNotEmpty()
  @IsDateString()
  transaction_date: string;

  @ApiPropertyOptional({
    description: 'Due date for the transaction',
    example: '2024-01-31T23:59:59.000Z'
  })
  @IsOptional()
  @IsDateString()
  due_date?: string;

  @ApiPropertyOptional({
    description: 'Currency code',
    example: 'BRL',
    default: 'BRL'
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { student_id: 'uuid', course_id: 'uuid' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class UpdateFinancialTransactionDto {
  @ApiPropertyOptional({
    description: 'Transaction type',
    enum: ['income', 'expense'],
    example: 'income'
  })
  @IsOptional()
  @IsEnum(['income', 'expense'])
  type?: 'income' | 'expense';

  @ApiPropertyOptional({
    description: 'Transaction category',
    enum: [
      'enrollment_fee',
      'monthly_fee',
      'material',
      'salary',
      'infrastructure',
      'other'
    ],
    example: 'monthly_fee'
  })
  @IsOptional()
  @IsEnum([
    'enrollment_fee',
    'monthly_fee',
    'material',
    'salary',
    'infrastructure',
    'other'
  ])
  category?:
    | 'enrollment_fee'
    | 'monthly_fee'
    | 'material'
    | 'salary'
    | 'infrastructure'
    | 'other';

  @ApiPropertyOptional({
    description: 'Transaction amount',
    example: 299.99,
    minimum: 0.01
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  amount?: number;

  @ApiPropertyOptional({
    description: 'Transaction description',
    example: 'Monthly fee payment for January 2024'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Reference ID',
    example: 'uuid'
  })
  @IsOptional()
  @IsUUID()
  reference_id?: string;

  @ApiPropertyOptional({
    description: 'Reference type',
    example: 'enrollment'
  })
  @IsOptional()
  @IsString()
  reference_type?: string;

  @ApiPropertyOptional({
    description: 'Payment method ID',
    example: 'uuid'
  })
  @IsOptional()
  @IsUUID()
  payment_method_id?: string;

  @ApiPropertyOptional({
    description: 'Transaction status',
    enum: ['pending', 'completed', 'cancelled'],
    example: 'completed'
  })
  @IsOptional()
  @IsEnum(['pending', 'completed', 'cancelled'])
  status?: 'pending' | 'completed' | 'cancelled';

  @ApiPropertyOptional({
    description: 'Transaction date',
    example: '2024-01-15T10:30:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  transaction_date?: string;

  @ApiPropertyOptional({
    description: 'Due date',
    example: '2024-01-31T23:59:59.000Z'
  })
  @IsOptional()
  @IsDateString()
  due_date?: string;

  @ApiPropertyOptional({
    description: 'Payment date',
    example: '2024-01-15T10:30:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  paid_date?: string;

  @ApiPropertyOptional({
    description: 'Currency code',
    example: 'BRL'
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata'
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class CreateInvoiceDto {
  @ApiProperty({
    description: 'Enrollment ID',
    example: 'uuid'
  })
  @IsNotEmpty()
  @IsUUID()
  enrollment_id: string;

  @ApiProperty({
    description: 'Invoice amount',
    example: 299.99,
    minimum: 0.01
  })
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  amount: number;

  @ApiProperty({
    description: 'Due date for payment',
    example: '2024-01-31T23:59:59.000Z'
  })
  @IsNotEmpty()
  @IsDateString()
  due_date: string;

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Monthly fee for January 2024'
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Currency code',
    example: 'BRL',
    default: 'BRL'
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { course_id: 'uuid', student_id: 'uuid' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class ProcessInvoicePaymentDto {
  @ApiPropertyOptional({
    description: 'Payment method ID',
    example: 'uuid'
  })
  @IsOptional()
  @IsUUID()
  payment_method_id?: string;

  @ApiPropertyOptional({
    description: 'Payment date',
    example: '2024-01-15T10:30:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  payment_date?: string;

  @ApiPropertyOptional({
    description: 'Stripe Payment Intent ID',
    example: 'pi_1234567890'
  })
  @IsOptional()
  @IsString()
  stripe_payment_intent_id?: string;

  @ApiPropertyOptional({
    description: 'Payment notes',
    example: 'Paid via credit card'
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class CreatePaymentMethodDto {
  @ApiProperty({
    description: 'Payment method name',
    example: 'Credit Card Visa *1234'
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Payment method type',
    enum: ['card', 'boleto', 'pix', 'bank_transfer', 'cash', 'check'],
    example: 'card'
  })
  @IsNotEmpty()
  @IsEnum(['card', 'boleto', 'pix', 'bank_transfer', 'cash', 'check'])
  type: 'card' | 'boleto' | 'pix' | 'bank_transfer' | 'cash' | 'check';

  @ApiPropertyOptional({
    description: 'Stripe Payment Method ID',
    example: 'pm_1234567890'
  })
  @IsOptional()
  @IsString()
  stripe_payment_method_id?: string;

  @ApiPropertyOptional({
    description: 'Stripe Customer ID',
    example: 'cus_1234567890'
  })
  @IsOptional()
  @IsString()
  stripe_customer_id?: string;

  @ApiPropertyOptional({
    description: 'Card brand (for card type)',
    example: 'visa'
  })
  @IsOptional()
  @IsString()
  card_brand?: string;

  @ApiPropertyOptional({
    description: 'Last 4 digits of card',
    example: '1234'
  })
  @IsOptional()
  @IsString()
  card_last4?: string;

  @ApiPropertyOptional({
    description: 'Card expiration month',
    example: 12,
    minimum: 1,
    maximum: 12
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  card_exp_month?: number;

  @ApiPropertyOptional({
    description: 'Card expiration year',
    example: 2025,
    minimum: 2024
  })
  @IsOptional()
  @IsNumber()
  @Min(2024)
  card_exp_year?: number;
}
