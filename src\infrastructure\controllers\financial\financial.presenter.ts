import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { FinancialTransactionEntity, InvoiceEntity, PaymentMethodEntity } from '../../../domain/entities/payment.entity';

export class FinancialTransactionPresenter {
  @ApiProperty({ description: 'Transaction ID', example: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Institution ID', example: 'uuid' })
  institution_id: string;

  @ApiProperty({
    description: 'Transaction type',
    enum: ['income', 'expense'],
    example: 'income'
  })
  type: string;

  @ApiProperty({
    description: 'Transaction category',
    example: 'monthly_fee'
  })
  category: string;

  @ApiProperty({ description: 'Transaction amount', example: 299.99 })
  amount: number;

  @ApiProperty({ description: 'Transaction description', example: 'Monthly fee payment' })
  description: string;

  @ApiPropertyOptional({ description: 'Reference ID', example: 'uuid' })
  reference_id?: string;

  @ApiPropertyOptional({ description: 'Reference type', example: 'enrollment' })
  reference_type?: string;

  @ApiPropertyOptional({ description: 'Payment method ID', example: 'uuid' })
  payment_method_id?: string;

  @ApiProperty({
    description: 'Transaction status',
    enum: ['pending', 'completed', 'cancelled'],
    example: 'completed'
  })
  status: string;

  @ApiProperty({ description: 'Transaction date', example: '2024-01-15T10:30:00.000Z' })
  transaction_date: Date;

  @ApiPropertyOptional({ description: 'Due date', example: '2024-01-31T23:59:59.000Z' })
  due_date?: Date;

  @ApiPropertyOptional({ description: 'Payment date', example: '2024-01-15T10:30:00.000Z' })
  paid_date?: Date;

  @ApiPropertyOptional({ description: 'Stripe Payment Intent ID', example: 'pi_1234567890' })
  stripe_payment_intent_id?: string;

  @ApiPropertyOptional({ description: 'Stripe Session ID', example: 'cs_1234567890' })
  stripe_session_id?: string;

  @ApiProperty({
    description: 'Payment type',
    enum: ['one_time', 'subscription', 'refund'],
    example: 'one_time'
  })
  payment_type: string;

  @ApiProperty({ description: 'Currency', example: 'BRL' })
  currency: string;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'Created by user ID', example: 'uuid' })
  created_by: string;

  @ApiProperty({ description: 'Creation date', example: '2024-01-01T00:00:00.000Z' })
  created_at: Date;

  @ApiPropertyOptional({ description: 'Update date', example: '2024-01-02T00:00:00.000Z' })
  updated_at?: Date;

  constructor(transaction: FinancialTransactionEntity) {
    this.id = transaction.id;
    this.institution_id = transaction.institution_id;
    this.type = transaction.type;
    this.category = transaction.category;
    this.amount = transaction.amount;
    this.description = transaction.description;
    this.reference_id = transaction.reference_id;
    this.reference_type = transaction.reference_type;
    this.payment_method_id = transaction.payment_method_id;
    this.status = transaction.status;
    this.transaction_date = transaction.transaction_date;
    this.due_date = transaction.due_date;
    this.paid_date = transaction.paid_date;
    this.stripe_payment_intent_id = transaction.stripe_payment_intent_id;
    this.stripe_session_id = transaction.stripe_session_id;
    this.payment_type = transaction.payment_type;
    this.currency = transaction.currency;
    this.metadata = transaction.metadata;
    this.created_by = transaction.created_by;
    this.created_at = transaction.created_at;
    this.updated_at = transaction.updated_at;
  }
}

export class InvoicePresenter {
  @ApiProperty({ description: 'Invoice ID', example: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Enrollment ID', example: 'uuid' })
  enrollment_id: string;

  @ApiProperty({ description: 'Institution ID', example: 'uuid' })
  institution_id: string;

  @ApiProperty({ description: 'Invoice number', example: '202401001' })
  invoice_number: string;

  @ApiProperty({ description: 'Invoice amount', example: 299.99 })
  amount: number;

  @ApiProperty({ description: 'Due date', example: '2024-01-31T23:59:59.000Z' })
  due_date: Date;

  @ApiProperty({
    description: 'Invoice status',
    enum: ['pending', 'paid', 'overdue', 'cancelled'],
    example: 'pending'
  })
  status: string;

  @ApiPropertyOptional({ description: 'Payment date', example: '2024-01-15T10:30:00.000Z' })
  payment_date?: Date;

  @ApiPropertyOptional({ description: 'Payment method ID', example: 'uuid' })
  payment_method_id?: string;

  @ApiPropertyOptional({ description: 'Additional notes', example: 'Monthly fee for January' })
  notes?: string;

  @ApiPropertyOptional({ description: 'Stripe Invoice ID', example: 'in_1234567890' })
  stripe_invoice_id?: string;

  @ApiPropertyOptional({ description: 'Stripe Payment Intent ID', example: 'pi_1234567890' })
  stripe_payment_intent_id?: string;

  @ApiProperty({ description: 'Currency', example: 'BRL' })
  currency: string;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'Created by user ID', example: 'uuid' })
  created_by: string;

  @ApiProperty({ description: 'Creation date', example: '2024-01-01T00:00:00.000Z' })
  created_at: Date;

  @ApiPropertyOptional({ description: 'Update date', example: '2024-01-02T00:00:00.000Z' })
  updated_at?: Date;

  constructor(invoice: InvoiceEntity) {
    this.id = invoice.id;
    this.enrollment_id = invoice.enrollment_id;
    this.institution_id = invoice.institution_id;
    this.invoice_number = invoice.invoice_number;
    this.amount = invoice.amount;
    this.due_date = invoice.due_date;
    this.status = invoice.status;
    this.payment_date = invoice.payment_date;
    this.payment_method_id = invoice.payment_method_id;
    this.notes = invoice.notes;
    this.stripe_invoice_id = invoice.stripe_invoice_id;
    this.stripe_payment_intent_id = invoice.stripe_payment_intent_id;
    this.currency = invoice.currency;
    this.metadata = invoice.metadata;
    this.created_by = invoice.created_by;
    this.created_at = invoice.created_at;
    this.updated_at = invoice.updated_at;
  }
}

export class PaymentMethodPresenter {
  @ApiProperty({ description: 'Payment method ID', example: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Institution ID', example: 'uuid' })
  institution_id: string;

  @ApiProperty({ description: 'Payment method name', example: 'Credit Card Visa *1234' })
  name: string;

  @ApiProperty({
    description: 'Payment method type',
    enum: ['card', 'boleto', 'pix', 'bank_transfer', 'cash', 'check'],
    example: 'card'
  })
  type: string;

  @ApiProperty({ description: 'Is active', example: true })
  is_active: boolean;

  @ApiPropertyOptional({ description: 'Stripe Payment Method ID', example: 'pm_1234567890' })
  stripe_payment_method_id?: string;

  @ApiPropertyOptional({ description: 'Stripe Customer ID', example: 'cus_1234567890' })
  stripe_customer_id?: string;

  @ApiPropertyOptional({ description: 'Card brand', example: 'visa' })
  card_brand?: string;

  @ApiPropertyOptional({ description: 'Last 4 digits', example: '1234' })
  card_last4?: string;

  @ApiPropertyOptional({ description: 'Expiration month', example: 12 })
  card_exp_month?: number;

  @ApiPropertyOptional({ description: 'Expiration year', example: 2025 })
  card_exp_year?: number;

  @ApiProperty({ description: 'Is default method', example: false })
  is_default: boolean;

  @ApiProperty({ description: 'Created by user ID', example: 'uuid' })
  created_by: string;

  @ApiProperty({ description: 'Creation date', example: '2024-01-01T00:00:00.000Z' })
  created_at: Date;

  @ApiPropertyOptional({ description: 'Update date', example: '2024-01-02T00:00:00.000Z' })
  updated_at?: Date;

  constructor(paymentMethod: PaymentMethodEntity) {
    this.id = paymentMethod.id;
    this.institution_id = paymentMethod.institution_id;
    this.name = paymentMethod.name;
    this.type = paymentMethod.type;
    this.is_active = paymentMethod.is_active;
    this.stripe_payment_method_id = paymentMethod.stripe_payment_method_id;
    this.stripe_customer_id = paymentMethod.stripe_customer_id;
    this.card_brand = paymentMethod.card_brand;
    this.card_last4 = paymentMethod.card_last4;
    this.card_exp_month = paymentMethod.card_exp_month;
    this.card_exp_year = paymentMethod.card_exp_year;
    this.is_default = paymentMethod.is_default;
    this.created_by = paymentMethod.created_by;
    this.created_at = paymentMethod.created_at;
    this.updated_at = paymentMethod.updated_at;
  }
}

export class FinancialSummaryPresenter {
  @ApiProperty({ description: 'Total income', example: 15000.00 })
  totalIncome: number;

  @ApiProperty({ description: 'Total expense', example: 8000.00 })
  totalExpense: number;

  @ApiProperty({ description: 'Balance (income - expense)', example: 7000.00 })
  balance: number;

  @ApiProperty({ description: 'Total transaction count', example: 150 })
  transactionCount: number;

  @ApiProperty({ description: 'Income by category', example: { monthly_fee: 12000, enrollment_fee: 3000 } })
  incomeByCategory: Record<string, number>;

  @ApiProperty({ description: 'Expense by category', example: { salary: 5000, infrastructure: 3000 } })
  expenseByCategory: Record<string, number>;

  @ApiProperty({
    description: 'Monthly trend data',
    example: [
      { month: '2024-01', income: 5000, expense: 2000, balance: 3000 },
      { month: '2024-02', income: 5500, expense: 2500, balance: 3000 }
    ]
  })
  monthlyTrend: Array<{
    month: string;
    income: number;
    expense: number;
    balance: number;
  }>;

  constructor(data: {
    totalIncome: number;
    totalExpense: number;
    balance: number;
    transactionCount: number;
    incomeByCategory: Record<string, number>;
    expenseByCategory: Record<string, number>;
    monthlyTrend: Array<{
      month: string;
      income: number;
      expense: number;
      balance: number;
    }>;
  }) {
    this.totalIncome = data.totalIncome;
    this.totalExpense = data.totalExpense;
    this.balance = data.balance;
    this.transactionCount = data.transactionCount;
    this.incomeByCategory = data.incomeByCategory;
    this.expenseByCategory = data.expenseByCategory;
    this.monthlyTrend = data.monthlyTrend;
  }
}
