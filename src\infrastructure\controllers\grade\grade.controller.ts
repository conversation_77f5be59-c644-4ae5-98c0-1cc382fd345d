import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  UseGuards,
  UseInterceptors
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../../common/guards/permission.guard';
import { RequirePermissions } from '../../common/decorators/require-permissions.decorator';
import {
  CurrentUser,
  CurrentUserData
} from '../../common/decorators/current-user.decorator';
import { AuditInterceptor } from '../../common/interceptors/audit.interceptor';
import { GradeUsecasesProxy } from '../../usecases-proxy/grade/grade-usecases-proxy.constants';
import { UseCaseProxy } from '../../usecases-proxy/usecases-proxy';
import { CreateGradeUseCase } from '../../../usecases/grade/create-grade.usecase';
import { DeleteGradeUseCase } from '../../../usecases/grade/delete-grade.usecase';
import { GetGradeUseCase } from '../../../usecases/grade/get-grade.usecase';
import { GetGradesByEnrollmentUseCase } from '../../../usecases/grade/get-grades-by-enrollment.usecase';
import { GetGradesByAssessmentTypeUseCase } from '../../../usecases/grade/get-grades-by-assessment-type.usecase';
import { GetGradesUseCase } from '../../../usecases/grade/get-grades.usecase';
import { CalculateWeightedAverageUseCase } from '../../../usecases/grade/calculate-weighted-average.usecase';
import { UpdateGradeUseCase } from '../../../usecases/grade/update-grade.usecase';
import { CreateGradeDto, UpdateGradeDto } from './grade.dto';
import {
  GradePresenter,
  WeightedAveragePresenter,
  GradesByAssessmentTypePresenter
} from './grade.presenter';

@Controller('grade')
@ApiTags('Grade')
@ApiExtraModels(GradePresenter, WeightedAveragePresenter, GradesByAssessmentTypePresenter)
export class GradeController {
  constructor(
    @Inject(GradeUsecasesProxy.GET_GRADE_USECASE_PROXY)
    private readonly getGradeUsecaseProxy: UseCaseProxy<GetGradeUseCase>,
    @Inject(GradeUsecasesProxy.GET_GRADES_USECASE_PROXY)
    private readonly getGradesUsecaseProxy: UseCaseProxy<GetGradesUseCase>,
    @Inject(GradeUsecasesProxy.GET_GRADES_BY_ENROLLMENT_USECASE_PROXY)
    private readonly getGradesByEnrollmentUsecaseProxy: UseCaseProxy<GetGradesByEnrollmentUseCase>,
    @Inject(GradeUsecasesProxy.GET_GRADES_BY_ASSESSMENT_TYPE_USECASE_PROXY)
    private readonly getGradesByAssessmentTypeUsecaseProxy: UseCaseProxy<GetGradesByAssessmentTypeUseCase>,
    @Inject(GradeUsecasesProxy.CALCULATE_WEIGHTED_AVERAGE_USECASE_PROXY)
    private readonly calculateWeightedAverageUsecaseProxy: UseCaseProxy<CalculateWeightedAverageUseCase>,
    @Inject(GradeUsecasesProxy.POST_GRADE_USECASE_PROXY)
    private readonly postGradeUsecaseProxy: UseCaseProxy<CreateGradeUseCase>,
    @Inject(GradeUsecasesProxy.PUT_GRADE_USECASE_PROXY)
    private readonly updateGradeUsecaseProxy: UseCaseProxy<UpdateGradeUseCase>,
    @Inject(GradeUsecasesProxy.DELETE_GRADE_USECASE_PROXY)
    private readonly deleteGradeUsecaseProxy: UseCaseProxy<DeleteGradeUseCase>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.create')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: GradePresenter,
    description: 'Grade created'
  })
  async createGrade(
    @Body() body: CreateGradeDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<GradePresenter> {
    const gradeData = {
      ...body,
      created_by: user.sub
    };
    const grade = await this.postGradeUsecaseProxy
      .getInstance()
      .execute(gradeData);
    return new GradePresenter(grade);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.update')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: GradePresenter,
    description: 'Grade updated'
  })
  async updateGrade(
    @Param('id') id: string,
    @Body() body: UpdateGradeDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<GradePresenter> {
    const updateData = {
      ...body,
      updated_by: user.sub
    };
    const grade = await this.updateGradeUsecaseProxy
      .getInstance()
      .execute(id, updateData);
    return new GradePresenter(grade);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.delete')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Grade deleted'
  })
  async deleteGrade(@Param('id') id: string): Promise<void> {
    await this.deleteGradeUsecaseProxy.getInstance().execute(id);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: GradePresenter,
    description: 'Grade returned'
  })
  async getGrade(@Param('id') id: string): Promise<GradePresenter> {
    const grade = await this.getGradeUsecaseProxy.getInstance().execute(id);
    return new GradePresenter(grade);
  }

  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [GradePresenter],
    description: 'Grades returned'
  })
  async getGrades(): Promise<GradePresenter[]> {
    const grades = await this.getGradesUsecaseProxy.getInstance().execute();
    return grades.map(grade => new GradePresenter(grade));
  }

  @Get('enrollment/:enrollmentId')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [GradePresenter],
    description: 'Enrollment grades returned'
  })
  async getGradesByEnrollment(
    @Param('enrollmentId') enrollmentId: string
  ): Promise<GradePresenter[]> {
    const grades = await this.getGradesByEnrollmentUsecaseProxy
      .getInstance()
      .execute(enrollmentId);
    return grades.map(grade => new GradePresenter(grade));
  }

  @Get('enrollment/:enrollmentId/by-assessment-type')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [GradesByAssessmentTypePresenter],
    description: 'Grades grouped by assessment type returned'
  })
  async getGradesByAssessmentType(
    @Param('enrollmentId') enrollmentId: string
  ): Promise<GradesByAssessmentTypePresenter[]> {
    const gradesByType = await this.getGradesByAssessmentTypeUsecaseProxy
      .getInstance()
      .execute(enrollmentId);
    return gradesByType.map(
      gradeType => new GradesByAssessmentTypePresenter(gradeType)
    );
  }

  @Get('enrollment/:enrollmentId/weighted-average')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: WeightedAveragePresenter,
    description: 'Weighted average returned'
  })
  async getWeightedAverage(
    @Param('enrollmentId') enrollmentId: string
  ): Promise<WeightedAveragePresenter> {
    const average = await this.calculateWeightedAverageUsecaseProxy
      .getInstance()
      .execute(enrollmentId);
    return new WeightedAveragePresenter(average);
  }
}
