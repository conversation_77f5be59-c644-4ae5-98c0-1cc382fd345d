import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  UseGuards,
  UseInterceptors
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { CalculateWeightedAverageUseCase } from '../../../usecases/grade/calculate-weighted-average.usecase';
import { CreateGradeUseCase } from '../../../usecases/grade/create-grade.usecase';
import { DeleteGradeUseCase } from '../../../usecases/grade/delete-grade.usecase';
import { GetGradeUseCase } from '../../../usecases/grade/get-grade.usecase';
import { GetGradesByAssessmentTypeUseCase } from '../../../usecases/grade/get-grades-by-assessment-type.usecase';
import { GetGradesByEnrollmentUseCase } from '../../../usecases/grade/get-grades-by-enrollment.usecase';
import { GetGradesUseCase } from '../../../usecases/grade/get-grades.usecase';
import { UpdateGradeUseCase } from '../../../usecases/grade/update-grade.usecase';
import {
  CurrentUser,
  CurrentUserData
} from '../../common/decorators/current-user.decorator';
import { RequirePermissions } from '../../common/decorators/require-permissions.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../../common/guards/permission.guard';
import { AuditInterceptor } from '../../common/interceptors/audit.interceptor';
import { GradeUsecasesProxy } from '../../usecases-proxy/grade/grade-usecases-proxy.constants';
import { UseCaseProxy } from '../../usecases-proxy/usecases-proxy';
import { CreateGradeDto, UpdateGradeDto } from './grade.dto';
import {
  GradePresenter,
  GradesByAssessmentTypePresenter,
  WeightedAveragePresenter
} from './grade.presenter';

@Controller('grade')
@ApiTags('Grade')
@ApiExtraModels(
  GradePresenter,
  WeightedAveragePresenter,
  GradesByAssessmentTypePresenter
)
export class GradeController {
  constructor(
    @Inject(GradeUsecasesProxy.GET_GRADE_USECASE_PROXY)
    private readonly getGradeUsecaseProxy: UseCaseProxy<GetGradeUseCase>,
    @Inject(GradeUsecasesProxy.GET_GRADES_USECASE_PROXY)
    private readonly getGradesUsecaseProxy: UseCaseProxy<GetGradesUseCase>,
    @Inject(GradeUsecasesProxy.GET_GRADES_BY_ENROLLMENT_USECASE_PROXY)
    private readonly getGradesByEnrollmentUsecaseProxy: UseCaseProxy<GetGradesByEnrollmentUseCase>,
    @Inject(GradeUsecasesProxy.GET_GRADES_BY_ASSESSMENT_TYPE_USECASE_PROXY)
    private readonly getGradesByAssessmentTypeUsecaseProxy: UseCaseProxy<GetGradesByAssessmentTypeUseCase>,
    @Inject(GradeUsecasesProxy.CALCULATE_WEIGHTED_AVERAGE_USECASE_PROXY)
    private readonly calculateWeightedAverageUsecaseProxy: UseCaseProxy<CalculateWeightedAverageUseCase>,
    @Inject(GradeUsecasesProxy.POST_GRADE_USECASE_PROXY)
    private readonly postGradeUsecaseProxy: UseCaseProxy<CreateGradeUseCase>,
    @Inject(GradeUsecasesProxy.PUT_GRADE_USECASE_PROXY)
    private readonly updateGradeUsecaseProxy: UseCaseProxy<UpdateGradeUseCase>,
    @Inject(GradeUsecasesProxy.DELETE_GRADE_USECASE_PROXY)
    private readonly deleteGradeUsecaseProxy: UseCaseProxy<DeleteGradeUseCase>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.create')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: GradePresenter,
    description: 'Grade created'
  })
  async createGrade(
    @Body() body: CreateGradeDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<GradePresenter> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    const gradeData = {
      ...body,
      institution_id: user.institution_id, // Auto-fill from user
      created_by: user.sub
    };
    const grade = await this.postGradeUsecaseProxy
      .getInstance()
      .execute(gradeData);
    return new GradePresenter(grade);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.update')
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: GradePresenter,
    description: 'Grade updated'
  })
  async updateGrade(
    @Param('id') id: string,
    @Body() body: UpdateGradeDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<GradePresenter> {
    const updateData = {
      ...body,
      updated_by: user.sub
    };
    const grade = await this.updateGradeUsecaseProxy
      .getInstance()
      .execute(id, updateData);
    return new GradePresenter(grade);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.delete')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Grade deleted'
  })
  async deleteGrade(@Param('id') id: string): Promise<void> {
    await this.deleteGradeUsecaseProxy.getInstance().execute(id);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: GradePresenter,
    description: 'Grade returned'
  })
  async getGrade(
    @Param('id') id: string,
    @CurrentUser() user: CurrentUserData
  ): Promise<GradePresenter> {
    const grade = await this.getGradeUsecaseProxy.getInstance().execute(id);

    // Verify multi-tenant access
    if (!user.institution_id || grade.institution_id !== user.institution_id) {
      throw new Error('Access denied: Grade not found in your institution');
    }

    return new GradePresenter(grade);
  }

  @Get()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [GradePresenter],
    description: 'Grades returned'
  })
  async getGrades(
    @CurrentUser() user: CurrentUserData
  ): Promise<GradePresenter[]> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    const grades = await this.getGradesUsecaseProxy.getInstance().execute();
    // Filter by institution_id for multi-tenancy
    return grades
      .filter(grade => grade.institution_id === user.institution_id)
      .map(grade => new GradePresenter(grade));
  }

  @Get('enrollment/:enrollmentId')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [GradePresenter],
    description: 'Enrollment grades returned'
  })
  async getGradesByEnrollment(
    @Param('enrollmentId') enrollmentId: string
  ): Promise<GradePresenter[]> {
    const grades = await this.getGradesByEnrollmentUsecaseProxy
      .getInstance()
      .execute(enrollmentId);
    return grades.map(grade => new GradePresenter(grade));
  }

  @Get('enrollment/:enrollmentId/by-assessment-type')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [GradesByAssessmentTypePresenter],
    description: 'Grades grouped by assessment type returned'
  })
  async getGradesByAssessmentType(
    @Param('enrollmentId') enrollmentId: string
  ): Promise<GradesByAssessmentTypePresenter[]> {
    const gradesByType = await this.getGradesByAssessmentTypeUsecaseProxy
      .getInstance()
      .execute(enrollmentId);
    return gradesByType.map(
      gradeType => new GradesByAssessmentTypePresenter(gradeType)
    );
  }

  @Get('enrollment/:enrollmentId/weighted-average')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('grade.view')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: WeightedAveragePresenter,
    description: 'Weighted average returned'
  })
  async getWeightedAverage(
    @Param('enrollmentId') enrollmentId: string
  ): Promise<WeightedAveragePresenter> {
    const average = await this.calculateWeightedAverageUsecaseProxy
      .getInstance()
      .execute(enrollmentId);
    return new WeightedAveragePresenter(average);
  }
}
