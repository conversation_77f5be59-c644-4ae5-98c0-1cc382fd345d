import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  <PERSON>,
  <PERSON>
} from 'class-validator';

export class CreateGradeDto {
  @ApiProperty({ description: 'Enrollment ID', example: 'uuid' })
  @IsNotEmpty()
  @IsUUID()
  enrollment_id: string;

  @ApiPropertyOptional({
    description: 'Institution ID (auto-filled from user)',
    example: 'uuid'
  })
  @IsOptional()
  @IsUUID()
  institution_id?: string;

  @ApiProperty({
    description: 'Assessment type',
    example: 'exam'
  })
  @IsNotEmpty()
  @IsString()
  assessment_type: string;

  @ApiProperty({
    description: 'Assessment name',
    example: 'Final Exam - Mathematics'
  })
  @IsNotEmpty()
  @IsString()
  assessment_name: string;

  @ApiProperty({
    description: 'Grade obtained',
    example: 85.5
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  grade: number;

  @ApiProperty({
    description: 'Maximum possible grade',
    example: 100
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0.01)
  max_grade: number;

  @ApiPropertyOptional({
    description: 'Weight of the assessment',
    example: 1.0,
    default: 1.0
  })
  @IsOptional()
  @IsNumber()
  @Min(0.01)
  @Max(10)
  weight?: number;

  @ApiProperty({
    description: 'Assessment date',
    example: '2024-01-15'
  })
  @IsNotEmpty()
  @IsDateString()
  assessment_date: Date;

  @ApiProperty({ description: 'Recorded by user ID', example: 'uuid' })
  @IsNotEmpty()
  @IsUUID()
  recorded_by: string;
}

export class UpdateGradeDto {
  @ApiPropertyOptional({
    description: 'Assessment type',
    example: 'quiz'
  })
  @IsOptional()
  @IsString()
  assessment_type?: string;

  @ApiPropertyOptional({
    description: 'Assessment name',
    example: 'Quiz 1 - Algebra'
  })
  @IsOptional()
  @IsString()
  assessment_name?: string;

  @ApiPropertyOptional({
    description: 'Grade obtained',
    example: 90.0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  grade?: number;

  @ApiPropertyOptional({
    description: 'Maximum possible grade',
    example: 100
  })
  @IsOptional()
  @IsNumber()
  @Min(0.01)
  max_grade?: number;

  @ApiPropertyOptional({
    description: 'Weight of the assessment',
    example: 0.5
  })
  @IsOptional()
  @IsNumber()
  @Min(0.01)
  @Max(10)
  weight?: number;

  @ApiPropertyOptional({
    description: 'Assessment date',
    example: '2024-01-20'
  })
  @IsOptional()
  @IsDateString()
  assessment_date?: Date;
}
