import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { GradeEntity } from '../../../domain/entities/grade.entity';

export class GradePresenter {
  @ApiProperty({ description: 'Grade ID', example: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Enrollment ID', example: 'uuid' })
  enrollment_id: string;

  @ApiProperty({
    description: 'Assessment type',
    example: 'exam'
  })
  assessment_type: string;

  @ApiProperty({
    description: 'Assessment name',
    example: 'Final Exam - Mathematics'
  })
  assessment_name: string;

  @ApiProperty({
    description: 'Grade obtained',
    example: 85.5
  })
  grade: number;

  @ApiProperty({
    description: 'Maximum possible grade',
    example: 100
  })
  max_grade: number;

  @ApiProperty({
    description: 'Weight of the assessment',
    example: 1.0
  })
  weight: number;

  @ApiProperty({
    description: 'Assessment date',
    example: '2024-01-15'
  })
  assessment_date: Date;

  @ApiProperty({ description: 'Recorded by user ID', example: 'uuid' })
  recorded_by: string;

  @ApiProperty({ description: 'Created by user ID', example: 'uuid' })
  created_by: string;

  @ApiPropertyOptional({ description: 'Updated by user ID', example: 'uuid' })
  updated_by?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z'
  })
  created_at: Date;

  @ApiPropertyOptional({
    description: 'Update date',
    example: '2024-01-02T00:00:00.000Z'
  })
  updated_at?: Date;

  constructor(grade: GradeEntity) {
    this.id = grade.id;
    this.enrollment_id = grade.enrollment_id;
    this.assessment_type = grade.assessment_type;
    this.assessment_name = grade.assessment_name;
    this.grade = grade.grade;
    this.max_grade = grade.max_grade;
    this.weight = grade.weight;
    this.assessment_date = grade.assessment_date;
    this.recorded_by = grade.recorded_by;
    this.created_by = grade.created_by;
    this.updated_by = grade.updated_by;
    this.created_at = grade.created_at;
    this.updated_at = grade.updated_at;
  }
}

export class WeightedAveragePresenter {
  @ApiProperty({ description: 'Weighted average grade', example: 87.5 })
  weightedAverage: number;

  @ApiProperty({ description: 'Total weight', example: 5.0 })
  totalWeight: number;

  @ApiProperty({ description: 'Number of grades', example: 5 })
  gradeCount: number;

  constructor(data: {
    weightedAverage: number;
    totalWeight: number;
    gradeCount: number;
  }) {
    this.weightedAverage = data.weightedAverage;
    this.totalWeight = data.totalWeight;
    this.gradeCount = data.gradeCount;
  }
}

export class GradesByAssessmentTypePresenter {
  @ApiProperty({ description: 'Assessment type', example: 'exam' })
  assessmentType: string;

  @ApiProperty({ description: 'Grades for this assessment type', type: [GradePresenter] })
  grades: GradePresenter[];

  @ApiProperty({ description: 'Average for this assessment type', example: 85.0 })
  average: number;

  @ApiProperty({ description: 'Total weight for this assessment type', example: 2.0 })
  totalWeight: number;

  constructor(data: {
    assessmentType: string;
    grades: GradeEntity[];
    average: number;
    totalWeight: number;
  }) {
    this.assessmentType = data.assessmentType;
    this.grades = data.grades.map(grade => new GradePresenter(grade));
    this.average = data.average;
    this.totalWeight = data.totalWeight;
  }
}
