import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { InstitutionUsecasesProxy } from '../../../infrastructure/usecases-proxy/institution/institution-usecase-proxy.constants';
import { UseCaseProxy } from '../../../infrastructure/usecases-proxy/usecases-proxy';
import { CreateInstitutionUseCase } from '../../../usecases/institution/create-institution.usecase';
import { DeleteInstitutionUseCase } from '../../../usecases/institution/delete-institution.usecase';
import { GetInstitutionUseCase } from '../../../usecases/institution/get-institution.usecase';
import { GetInstitutionsUseCase } from '../../../usecases/institution/get-institutions.usecase';
import { UpdateInstitutionUseCase } from '../../../usecases/institution/update-institution.usecase';
import { RequirePermissions } from '../../common/decorators/require-permissions.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../../common/guards/permission.guard';
import { CreateInstitutionDto, UpdateInstitutionDto } from './institution.dto';
import { InstitutionPresenter } from './institution.presenter';

@Controller('institution')
@ApiTags('Institution')
@ApiResponse({ status: 403, description: 'Forbidden.' })
@ApiResponse({ status: 500, description: 'Internal Server Error.' })
@ApiExtraModels(InstitutionPresenter)
export class InstitutionController {
  constructor(
    @Inject(InstitutionUsecasesProxy.POST_INSTITUTION_USECASE_PROXY)
    private readonly postInstitutionUsecaseProxy: UseCaseProxy<CreateInstitutionUseCase>,
    @Inject(InstitutionUsecasesProxy.UPDATE_INSTITUTION_USECASE_PROXY)
    private readonly updateIsntitutionUsecaseProxy: UseCaseProxy<UpdateInstitutionUseCase>,
    @Inject(InstitutionUsecasesProxy.DELETE_INSTITUTION_USECASE_PROXY)
    private readonly deleteIsntitutionUsecaseProxy: UseCaseProxy<DeleteInstitutionUseCase>,
    @Inject(InstitutionUsecasesProxy.GET_INSTITUTION_USECASE_PROXY)
    private readonly getInstitutionUsecaseProxy: UseCaseProxy<GetInstitutionUseCase>,
    @Inject(InstitutionUsecasesProxy.GET_INSTITUTIONS_USECASE_PROXY)
    private readonly getInstitutionsUsecaseProxy: UseCaseProxy<GetInstitutionsUseCase>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('institution.create')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: InstitutionPresenter,
    description: 'Institution created'
  })
  async createInstitution(
    @Body() body: CreateInstitutionDto
  ): Promise<InstitutionPresenter> {
    const institution = await this.postInstitutionUsecaseProxy
      .getInstance()
      .execute(body);
    return new InstitutionPresenter(institution);
  }

  @Put(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: InstitutionPresenter,
    description: 'Institution updated'
  })
  async updateInstitution(
    @Param('id') id: string,
    @Body() body: UpdateInstitutionDto
  ): Promise<InstitutionPresenter> {
    const institution = await this.updateIsntitutionUsecaseProxy
      .getInstance()
      .execute(id, body);
    return new InstitutionPresenter(institution);
  }

  @Delete(':id')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Institution deleted'
  })
  async deleteInstitution(@Param('id') id: string): Promise<void> {
    await this.deleteIsntitutionUsecaseProxy.getInstance().execute(id);
  }

  @Get(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: InstitutionPresenter,
    description: 'Institution returned'
  })
  async getInstitution(@Param('id') id: string): Promise<InstitutionPresenter> {
    const user = await this.getInstitutionUsecaseProxy
      .getInstance()
      .execute(id);
    return user;
  }

  @Get()
  @ApiResponse({
    status: HttpStatus.OK,
    type: InstitutionPresenter,
    description: 'Institutions returned'
  })
  async getInstitutions(): Promise<InstitutionPresenter[]> {
    const users = await this.getInstitutionsUsecaseProxy
      .getInstance()
      .execute();
    return users;
  }
}
