import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateInstitutionDto {
  @ApiProperty()
  @IsOptional()
  name: string; // Nome Fantasia

  @ApiProperty()
  @IsNotEmpty()
  legal_name: string; // Razão Social

  @ApiProperty()
  @IsNotEmpty()
  phone_number: string;

  @ApiProperty()
  @IsNotEmpty()
  tax_id: string; // CNPJ

  @ApiProperty()
  @IsOptional()
  incorporation_date?: Date; // Data de Fundação

  @ApiProperty()
  @IsNotEmpty()
  address_id: string;

  @ApiProperty()
  @IsOptional()
  plan_id: string;

  @ApiProperty()
  @IsOptional()
  main_institution_id?: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsOptional()
  website: string;

  status: string;

  actived: boolean;

  created_by: string;

  created_at: Date;
}

export class UpdateInstitutionDto {
  @ApiProperty()
  @IsNotEmpty()
  id: string;

  @ApiProperty()
  @IsOptional()
  name: string; // Nome Fantasia

  @ApiProperty()
  @IsNotEmpty()
  legal_name: string; // Razão Social

  @ApiProperty()
  @IsNotEmpty()
  phone_number: string;

  @ApiProperty()
  @IsNotEmpty()
  tax_id: string; // CNPJ

  @ApiProperty()
  @IsOptional()
  incorporation_date?: Date; // Data de Fundação

  @ApiProperty()
  @IsNotEmpty()
  address_id: string;

  @ApiProperty()
  @IsOptional()
  plan_id: string;

  @ApiProperty()
  @IsOptional()
  main_institution_id?: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsOptional()
  website: string;

  status: string;

  actived: boolean;

  updated_by: string;

  updated_at: Date;
}
