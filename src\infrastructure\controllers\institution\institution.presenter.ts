import { ApiProperty } from '@nestjs/swagger';
import { InstitutionEntity } from '../../../domain/entities/institution.entity';

export class InstitutionPresenter {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string; // Nome Fantasia

  @ApiProperty()
  legal_name: string; // Razão Social

  @ApiProperty()
  phone_number: string;

  @ApiProperty()
  tax_id: string; // CNPJ

  @ApiProperty()
  incorporation_date: Date; // Data de Fundação

  @ApiProperty()
  address_id: string;

  @ApiProperty()
  subscription_id: string;

  @ApiProperty()
  main_institution_id?: string;

  @ApiProperty()
  status: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  website: string;

  @ApiProperty()
  actived: boolean;

  @ApiProperty()
  created_by: string;

  @ApiProperty()
  updated_by: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;

  constructor(institution: InstitutionEntity) {
    this.id = institution.id;
    this.name = institution.name;
    this.legal_name = institution.legal_name;
    this.phone_number = institution.phone_number;
    this.tax_id = institution.tax_id;
    this.incorporation_date = institution.incorporation_date;
    this.email = institution.email;
    this.website = institution.website;
    this.actived = institution.actived;
    this.status = institution.status;
    this.subscription_id = institution.subscription_id;
    this.main_institution_id = institution.main_institution_id;
    this.address_id = institution.address_id;
    this.address_id = institution.address_id;
    this.created_by = institution.created_by;
    this.updated_by = institution.updated_by;
    this.created_at = institution.created_at;
    this.updated_at = institution.updated_at;
  }
}
