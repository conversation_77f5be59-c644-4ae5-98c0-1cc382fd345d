import {
  Body,
  Controller,
  Get,
  Headers,
  HttpStatus,
  Param,
  Post,
  RawBody,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../../common/guards/permission.guard';
import { RequirePermissions } from '../../common/decorators/require-permissions.decorator';
import {
  CurrentUser,
  CurrentUserData
} from '../../common/decorators/current-user.decorator';
import { CreatePaymentSessionUseCase } from '../../../usecases/payment/create-payment-session.usecase';
import { CreateSubscriptionUseCase } from '../../../usecases/payment/create-subscription.usecase';
import { HandleStripeWebhookUseCase } from '../../../usecases/payment/handle-stripe-webhook.usecase';
import {
  CreatePaymentSessionDto,
  CreateSubscriptionDto,
  CancelSubscriptionDto
} from './payment.dto';
import {
  PaymentSessionPresenter,
  PaymentPresenter,
  SubscriptionPresenter,
  CreateSubscriptionPresenter
} from './payment.presenter';

@Controller('payment')
@ApiTags('Payment')
@ApiExtraModels(
  PaymentSessionPresenter,
  PaymentPresenter,
  SubscriptionPresenter,
  CreateSubscriptionPresenter
)
export class PaymentController {
  constructor(
    private readonly createPaymentSessionUseCase: CreatePaymentSessionUseCase,
    private readonly createSubscriptionUseCase: CreateSubscriptionUseCase,
    private readonly handleStripeWebhookUseCase: HandleStripeWebhookUseCase
  ) {}

  @Post('create-session')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('payment.create')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: PaymentSessionPresenter,
    description: 'Payment session created successfully'
  })
  async createPaymentSession(
    @Body() body: CreatePaymentSessionDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<PaymentSessionPresenter> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    const result = await this.createPaymentSessionUseCase.execute({
      ...body,
      institution_id: user.institution_id,
      created_by: user.sub
    });

    return new PaymentSessionPresenter(result);
  }

  @Post('subscription')
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('payment.create')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: CreateSubscriptionPresenter,
    description: 'Subscription created successfully'
  })
  async createSubscription(
    @Body() body: CreateSubscriptionDto,
    @CurrentUser() user: CurrentUserData
  ): Promise<CreateSubscriptionPresenter> {
    if (!user.institution_id) {
      throw new Error('User must be associated with an institution');
    }

    const result = await this.createSubscriptionUseCase.execute({
      ...body,
      institution_id: user.institution_id,
      created_by: user.sub
    });

    return new CreateSubscriptionPresenter(result);
  }

  @Post('webhook')
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Webhook processed successfully'
  })
  async handleWebhook(
    @RawBody() body: string,
    @Headers('stripe-signature') signature: string
  ): Promise<{ received: boolean }> {
    await this.handleStripeWebhookUseCase.execute(body, signature);
    return { received: true };
  }

  @Get('config')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Stripe configuration returned'
  })
  async getStripeConfig(): Promise<{ publishableKey: string }> {
    // Retorna apenas a chave pública para o frontend
    return {
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY!
    };
  }
}
