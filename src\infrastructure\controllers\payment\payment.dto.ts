import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  IsUrl,
  Min,
  IsObject,
  IsPositive
} from 'class-validator';

export class CreatePaymentSessionDto {
  @ApiProperty({
    description:
      'Payment amount in the smallest currency unit (e.g., cents for BRL)',
    example: 2999,
    minimum: 1
  })
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  amount: number;

  @ApiPropertyOptional({
    description: 'Currency code',
    example: 'BRL',
    default: 'BRL'
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Payment description',
    example: 'Mensalidade Janeiro 2024'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Payment type',
    enum: ['one_time', 'subscription'],
    example: 'one_time'
  })
  @IsNotEmpty()
  @IsEnum(['one_time', 'subscription'])
  payment_type: 'one_time' | 'subscription';

  @ApiPropertyOptional({
    description: 'Plan ID for subscription payments (Stripe Price ID)',
    example: 'price_1234567890'
  })
  @IsOptional()
  @IsString()
  plan_id?: string;

  @ApiPropertyOptional({
    description: 'Trial period in days for subscriptions',
    example: 7,
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  trial_days?: number;

  @ApiPropertyOptional({
    description: 'Success URL after payment',
    example: 'https://myapp.com/success'
  })
  @IsOptional()
  @IsUrl()
  success_url?: string;

  @ApiPropertyOptional({
    description: 'Cancel URL if payment is cancelled',
    example: 'https://myapp.com/cancel'
  })
  @IsOptional()
  @IsUrl()
  cancel_url?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { course_id: 'uuid', student_id: 'uuid' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class CreateSubscriptionDto {
  @ApiProperty({
    description: 'Stripe Plan/Price ID',
    example: 'price_1234567890'
  })
  @IsNotEmpty()
  @IsString()
  plan_id: string;

  @ApiProperty({
    description: 'Customer email',
    example: '<EMAIL>'
  })
  @IsNotEmpty()
  @IsEmail()
  customer_email: string;

  @ApiPropertyOptional({
    description: 'Customer name',
    example: 'João Silva'
  })
  @IsOptional()
  @IsString()
  customer_name?: string;

  @ApiPropertyOptional({
    description: 'Trial period in days',
    example: 7,
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  trial_days?: number;

  @ApiPropertyOptional({
    description: 'Stripe Payment Method ID',
    example: 'pm_1234567890'
  })
  @IsOptional()
  @IsString()
  payment_method_id?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { department: 'sales' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class CancelSubscriptionDto {
  @ApiPropertyOptional({
    description: 'Cancel immediately or at period end',
    example: false,
    default: false
  })
  @IsOptional()
  cancel_immediately?: boolean;

  @ApiPropertyOptional({
    description: 'Cancellation reason',
    example: 'Customer request'
  })
  @IsOptional()
  @IsString()
  reason?: string;
}
