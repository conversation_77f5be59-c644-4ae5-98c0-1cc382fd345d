import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  FinancialTransactionEntity,
  SubscriptionEntity
} from '../../../domain/entities/payment.entity';

export class PaymentSessionPresenter {
  @ApiProperty({ description: 'Stripe session ID', example: 'cs_1234567890' })
  session_id: string;

  @ApiProperty({
    description: 'Stripe checkout URL',
    example: 'https://checkout.stripe.com/...'
  })
  session_url: string;

  @ApiProperty({ description: 'Internal payment ID', example: 'uuid' })
  payment_id: string;

  constructor(data: {
    session_id: string;
    session_url: string;
    payment_id: string;
  }) {
    this.session_id = data.session_id;
    this.session_url = data.session_url;
    this.payment_id = data.payment_id;
  }
}

export class PaymentPresenter {
  @ApiProperty({ description: 'Payment ID', example: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Institution ID', example: 'uuid' })
  institution_id: string;

  @ApiPropertyOptional({
    description: 'Stripe Payment Intent ID',
    example: 'pi_1234567890'
  })
  stripe_payment_intent_id?: string;

  @ApiPropertyOptional({
    description: 'Stripe Session ID',
    example: 'cs_1234567890'
  })
  stripe_session_id?: string;

  @ApiProperty({ description: 'Payment amount', example: 2999 })
  amount: number;

  @ApiProperty({ description: 'Currency', example: 'BRL' })
  currency: string;

  @ApiProperty({
    description: 'Payment status',
    enum: ['pending', 'processing', 'succeeded', 'failed', 'canceled'],
    example: 'succeeded'
  })
  status: string;

  @ApiProperty({
    description: 'Payment type',
    enum: ['one_time', 'subscription'],
    example: 'one_time'
  })
  payment_type: string;

  @ApiPropertyOptional({
    description: 'Payment description',
    example: 'Mensalidade Janeiro'
  })
  description?: string;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'Created by user ID', example: 'uuid' })
  created_by: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z'
  })
  created_at: Date;

  @ApiPropertyOptional({
    description: 'Update date',
    example: '2024-01-02T00:00:00.000Z'
  })
  updated_at?: Date;

  constructor(payment: FinancialTransactionEntity) {
    this.id = payment.id;
    this.institution_id = payment.institution_id;
    this.stripe_payment_intent_id = payment.stripe_payment_intent_id;
    this.stripe_session_id = payment.stripe_session_id;
    this.amount = payment.amount;
    this.currency = payment.currency;
    this.status = payment.status;
    this.payment_type = payment.payment_type;
    this.description = payment.description;
    this.metadata = payment.metadata;
    this.created_by = payment.created_by;
    this.created_at = payment.created_at;
    this.updated_at = payment.updated_at;
  }
}

export class SubscriptionPresenter {
  @ApiProperty({ description: 'Subscription ID', example: 'uuid' })
  id: string;

  @ApiProperty({ description: 'Institution ID', example: 'uuid' })
  institution_id: string;

  @ApiProperty({ description: 'Plan ID', example: 'uuid' })
  plan_id: string;

  @ApiProperty({
    description: 'Stripe Subscription ID',
    example: 'sub_1234567890'
  })
  stripe_subscription_id: string;

  @ApiProperty({ description: 'Stripe Customer ID', example: 'cus_1234567890' })
  stripe_customer_id: string;

  @ApiProperty({
    description: 'Subscription status',
    enum: [
      'active',
      'canceled',
      'incomplete',
      'incomplete_expired',
      'past_due',
      'trialing',
      'unpaid'
    ],
    example: 'active'
  })
  status: string;

  @ApiProperty({
    description: 'Current period start',
    example: '2024-01-01T00:00:00.000Z'
  })
  current_period_start: Date;

  @ApiProperty({
    description: 'Current period end',
    example: '2024-02-01T00:00:00.000Z'
  })
  current_period_end: Date;

  @ApiPropertyOptional({
    description: 'Trial start',
    example: '2024-01-01T00:00:00.000Z'
  })
  trial_start?: Date;

  @ApiPropertyOptional({
    description: 'Trial end',
    example: '2024-01-08T00:00:00.000Z'
  })
  trial_end?: Date;

  @ApiPropertyOptional({
    description: 'Canceled at',
    example: '2024-01-15T00:00:00.000Z'
  })
  canceled_at?: Date;

  @ApiPropertyOptional({
    description: 'Ended at',
    example: '2024-01-15T00:00:00.000Z'
  })
  ended_at?: Date;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'Created by user ID', example: 'uuid' })
  created_by: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z'
  })
  created_at: Date;

  @ApiPropertyOptional({
    description: 'Update date',
    example: '2024-01-02T00:00:00.000Z'
  })
  updated_at?: Date;

  constructor(subscription: SubscriptionEntity) {
    this.id = subscription.id;
    this.institution_id = subscription.institution_id;
    this.plan_id = subscription.plan_id;
    this.stripe_subscription_id = subscription.stripe_subscription_id;
    this.stripe_customer_id = subscription.stripe_customer_id;
    this.status = subscription.status;
    this.current_period_start = subscription.current_period_start;
    this.current_period_end = subscription.current_period_end;
    this.trial_start = subscription.trial_start;
    this.trial_end = subscription.trial_end;
    this.canceled_at = subscription.canceled_at;
    this.ended_at = subscription.ended_at;
    this.metadata = subscription.metadata;
    this.created_by = subscription.created_by;
    this.created_at = subscription.created_at;
    this.updated_at = subscription.updated_at;
  }
}

export class CreateSubscriptionPresenter {
  @ApiProperty({
    description: 'Stripe Subscription ID',
    example: 'sub_1234567890'
  })
  subscription_id: string;

  @ApiProperty({ description: 'Stripe Customer ID', example: 'cus_1234567890' })
  customer_id: string;

  @ApiProperty({ description: 'Subscription status', example: 'active' })
  status: string;

  @ApiProperty({
    description: 'Current period end',
    example: '2024-02-01T00:00:00.000Z'
  })
  current_period_end: Date;

  @ApiPropertyOptional({
    description: 'Trial end',
    example: '2024-01-08T00:00:00.000Z'
  })
  trial_end?: Date;

  @ApiPropertyOptional({
    description: 'Latest invoice URL',
    example: 'https://invoice.stripe.com/...'
  })
  latest_invoice_url?: string;

  constructor(data: {
    subscription_id: string;
    customer_id: string;
    status: string;
    current_period_end: Date;
    trial_end?: Date;
    latest_invoice_url?: string;
  }) {
    this.subscription_id = data.subscription_id;
    this.customer_id = data.customer_id;
    this.status = data.status;
    this.current_period_end = data.current_period_end;
    this.trial_end = data.trial_end;
    this.latest_invoice_url = data.latest_invoice_url;
  }
}
