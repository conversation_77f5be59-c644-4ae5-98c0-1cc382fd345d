import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  UseGuards,
  UseInterceptors
} from '@nestjs/common';
import { ApiExtraModels, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PermissionUsecasesProxy } from '../../../infrastructure/usecases-proxy/permission/permission-usecases-proxy.constants';
import { UseCaseProxy } from '../../../infrastructure/usecases-proxy/usecases-proxy';
import { CreatePermissionUseCase } from '../../../usecases/permission/create-permission.usecase';
import { DeletePermissionUseCase } from '../../../usecases/permission/delete-permission.usecase';
import { GetPermissionUseCase } from '../../../usecases/permission/get-permission.usecase';
import { GetPermissionsUseCase } from '../../../usecases/permission/get-permissions.usecase';
import { UpdatePermissionUseCase } from '../../../usecases/permission/update-permission.usecase';
import { CreatePermissionDto, UpdatePermissionDto } from './permission.dto';
import { PermissionPresenter } from './permission.presenter';
import { AuditInterceptor } from 'src/infrastructure/common/interceptors/audit.interceptor';
import { JwtAuthGuard } from 'src/infrastructure/common/guards/jwt-auth.guard';

@Controller('permission')
@ApiTags('Permission')
@ApiResponse({ status: 403, description: 'Forbidden.' })
@ApiResponse({ status: 500, description: 'Internal Server Error.' })
@ApiExtraModels(PermissionPresenter)
export class PermissionController {
  constructor(
    @Inject(PermissionUsecasesProxy.POST_PERMISSION_USECASE_PROXY)
    private readonly postPermissionUsecaseProxy: UseCaseProxy<CreatePermissionUseCase>,
    @Inject(PermissionUsecasesProxy.GET_PERMISSIONS_USECASE_PROXY)
    private readonly getPermissionsUsecaseProxy: UseCaseProxy<GetPermissionsUseCase>,
    @Inject(PermissionUsecasesProxy.GET_PERMISSION_USECASE_PROXY)
    private readonly getPermissionUsecaseProxy: UseCaseProxy<GetPermissionUseCase>,
    @Inject(PermissionUsecasesProxy.DELETE_PERMISSION_USECASE_PROXY)
    private readonly deletePermissionUsecaseProxy: UseCaseProxy<DeletePermissionUseCase>,
    @Inject(PermissionUsecasesProxy.UPDATE_PERMISSION_USECASE_PROXY)
    private readonly updatePermissionUsecaseProxy: UseCaseProxy<UpdatePermissionUseCase>
  ) {}

  @Post()
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: PermissionPresenter,
    description: 'Permission created'
  })
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(AuditInterceptor)
  async createPermission(
    @Body() body: CreatePermissionDto
  ): Promise<PermissionPresenter> {
    const user = await this.postPermissionUsecaseProxy
      .getInstance()
      .execute(body);
    return new PermissionPresenter(user);
  }

  @Put(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: PermissionPresenter,
    description: 'Permission updated'
  })
  async updatePermission(
    @Param('id') id: string,
    @Body() body: UpdatePermissionDto
  ): Promise<PermissionPresenter> {
    const user = await this.updatePermissionUsecaseProxy
      .getInstance()
      .execute(id, body);
    return new PermissionPresenter(user);
  }

  @Delete(':id')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Permission deleted'
  })
  async deletePermission(@Param('id') id: string): Promise<void> {
    await this.deletePermissionUsecaseProxy.getInstance().execute(id);
  }

  @Get(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: PermissionPresenter,
    description: 'Permission returned'
  })
  async getPermission(@Param('id') id: string): Promise<PermissionPresenter> {
    const permission = await this.getPermissionUsecaseProxy
      .getInstance()
      .execute(id);
    return permission;
  }

  @Get()
  @ApiResponse({
    status: HttpStatus.OK,
    type: PermissionPresenter,
    description: 'Permissions returned'
  })
  async getPermissions(): Promise<PermissionPresenter[]> {
    const permissions = await this.getPermissionsUsecaseProxy
      .getInstance()
      .execute();
    return permissions;
  }
}
