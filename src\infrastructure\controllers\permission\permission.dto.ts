import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreatePermissionDto {
  @ApiProperty()
  @IsNotEmpty()
  readonly name: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly key: string;

  @ApiProperty()
  @IsOptional()
  readonly description: string;

  readonly created_by?: string;

  readonly created_at?: Date;
}

export class UpdatePermissionDto {
  @ApiProperty()
  @IsNotEmpty()
  readonly id: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly name: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly key: string;

  @ApiProperty()
  @IsOptional()
  readonly description: string;

  readonly updated_by?: string;

  readonly updated_at?: Date;
}
