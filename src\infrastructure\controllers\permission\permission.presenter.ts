import { ApiProperty } from '@nestjs/swagger';
import { PermissionEntity } from '../../../domain/entities/permission.entity';

export class PermissionPresenter {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  key: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  created_by: string;

  @ApiProperty()
  updated_by: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;

  constructor(permission: PermissionEntity) {
    this.id = permission.id;
    this.name = permission.name;
    this.key = permission.key;
    this.description = permission.description;
    this.created_by = permission.created_by;
    this.updated_by = permission.updated_by;
    this.created_at = permission.created_at;
    this.updated_at = permission.updated_at;
  }
}
