import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put
} from '@nestjs/common';
import { ApiExtraModels, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PlanUsecasesProxy } from '../../../infrastructure/usecases-proxy/plan/plan-usecases-proxy.constants';
import { UseCaseProxy } from '../../../infrastructure/usecases-proxy/usecases-proxy';
import { CreatePlanUseCase } from '../../../usecases/plan/create-plan.usecase';
import { DeletePlanUseCase } from '../../../usecases/plan/delete-plan.usecase';
import { GetActivePlansUseCase } from '../../../usecases/plan/get-active-plans.usecase';
import { GetPlanUseCase } from '../../../usecases/plan/get-plan.usecase';
import { GetPlansUseCase } from '../../../usecases/plan/get-plans.usecase';
import { UpdatePlanUseCase } from '../../../usecases/plan/update-plan.usecase';
import { CreatePlanDto, UpdatePlanDto } from './plan.dto';
import { PlanPresenter } from './plan.presenter';

@Controller('plan')
@ApiTags('Plan')
@ApiResponse({ status: 403, description: 'Forbidden.' })
@ApiResponse({ status: 500, description: 'Internal Server Error.' })
@ApiExtraModels(PlanPresenter)
export class PlanController {
  constructor(
    @Inject(PlanUsecasesProxy.POST_PLAN_USECASE_PROXY)
    private readonly postPlanUsecaseProxy: UseCaseProxy<CreatePlanUseCase>,
    @Inject(PlanUsecasesProxy.GET_PLANS_USECASE_PROXY)
    private readonly getPlansUsecaseProxy: UseCaseProxy<GetPlansUseCase>,
    @Inject(PlanUsecasesProxy.GET_ACTIVE_PLANS_USECASE_PROXY)
    private readonly getActivePlansUsecaseProxy: UseCaseProxy<GetActivePlansUseCase>,
    @Inject(PlanUsecasesProxy.GET_PLAN_USECASE_PROXY)
    private readonly getPlanUsecaseProxy: UseCaseProxy<GetPlanUseCase>,
    @Inject(PlanUsecasesProxy.DELETE_PLAN_USECASE_PROXY)
    private readonly deletePlanUsecaseProxy: UseCaseProxy<DeletePlanUseCase>,
    @Inject(PlanUsecasesProxy.UPDATE_PLAN_USECASE_PROXY)
    private readonly updatePlanUsecaseProxy: UseCaseProxy<UpdatePlanUseCase>
  ) {}

  @Post()
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: PlanPresenter,
    description: 'Plan created'
  })
  async createPlan(@Body() body: CreatePlanDto): Promise<PlanPresenter> {
    const plan = await this.postPlanUsecaseProxy.getInstance().execute(body);
    return new PlanPresenter(plan);
  }

  @Put(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: PlanPresenter,
    description: 'Plan updated'
  })
  async updatePlan(
    @Param('id') id: string,
    @Body() body: UpdatePlanDto
  ): Promise<PlanPresenter> {
    const plan = await this.updatePlanUsecaseProxy
      .getInstance()
      .execute(id, body);
    return new PlanPresenter(plan);
  }

  @Delete(':id')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Plan deleted'
  })
  async deletePlan(@Param('id') id: string): Promise<void> {
    await this.deletePlanUsecaseProxy.getInstance().execute(id);
  }

  @Get('active')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [PlanPresenter],
    description: 'Active plans returned'
  })
  async getActivePlans(): Promise<PlanPresenter[]> {
    const plans = await this.getActivePlansUsecaseProxy.getInstance().execute();
    return plans.map(plan => new PlanPresenter(plan));
  }

  @Get(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: PlanPresenter,
    description: 'Plan returned'
  })
  async getPlan(@Param('id') id: string): Promise<PlanPresenter> {
    const plan = await this.getPlanUsecaseProxy.getInstance().execute(id);
    return new PlanPresenter(plan);
  }

  @Get()
  @ApiResponse({
    status: HttpStatus.OK,
    type: [PlanPresenter],
    description: 'Plans returned'
  })
  async getPlans(): Promise<PlanPresenter[]> {
    const plans = await this.getPlansUsecaseProxy.getInstance().execute();
    return plans.map(plan => new PlanPresenter(plan));
  }
}
