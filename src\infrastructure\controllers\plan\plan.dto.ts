import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID, Min } from 'class-validator';

export class CreatePlanDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly name: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly description?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  readonly price: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly currency?: string = 'BRL';

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly billing_period: string; // monthly, yearly

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Min(1)
  readonly max_users?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Min(1)
  readonly max_institutions?: number;

  @ApiProperty()
  @IsOptional()
  readonly features?: Record<string, any>;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly stripe_price_id?: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  readonly is_active?: boolean = true;

  readonly created_by?: string;

  readonly created_at?: Date;
}

export class UpdatePlanDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly name: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly description?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  readonly price: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly currency?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  readonly billing_period: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Min(1)
  readonly max_users?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Min(1)
  readonly max_institutions?: number;

  @ApiProperty()
  @IsOptional()
  readonly features?: Record<string, any>;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly stripe_price_id?: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  readonly is_active?: boolean;

  readonly updated_by: string;

  readonly updated_at: Date;
}
