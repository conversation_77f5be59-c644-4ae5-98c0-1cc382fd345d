import { ApiProperty } from '@nestjs/swagger';
import { PlanEntity } from '../../../domain/entities/plan.entity';

export class PlanPresenter {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  price: number;

  @ApiProperty()
  currency: string;

  @ApiProperty()
  billing_period: string;

  @ApiProperty()
  max_users: number;

  @ApiProperty()
  max_institutions: number;

  @ApiProperty()
  features: Record<string, any>;

  @ApiProperty()
  stripe_price_id?: string;

  @ApiProperty()
  is_active: boolean;

  @ApiProperty()
  created_by: string;

  @ApiProperty()
  updated_by: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;

  constructor(plan: PlanEntity) {
    this.id = plan.id;
    this.name = plan.name;
    this.description = plan.description;
    this.price = plan.price;
    this.currency = plan.currency;
    this.billing_period = plan.billing_period;
    this.max_users = plan.max_users;
    this.max_institutions = plan.max_institutions;
    this.features = plan.features;
    this.stripe_price_id = plan.stripe_price_id;
    this.is_active = plan.is_active;
    this.created_by = plan.created_by;
    this.updated_by = plan.updated_by;
    this.created_at = plan.created_at;
    this.updated_at = plan.updated_at;
  }
}
