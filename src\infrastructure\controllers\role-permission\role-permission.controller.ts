import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { RolePermissionUsecasesProxy } from '../../../infrastructure/usecases-proxy/role-permission/role-permission-usecases-proxy.constants';
import { UseCaseProxy } from '../../../infrastructure/usecases-proxy/usecases-proxy';
import { CreateRolePermissionUseCase } from '../../../usecases/role_permission/create-role-permission.usecase';
import { DeleteRolePermissionUseCase } from '../../../usecases/role_permission/delete-role-permission.usecase';
import { GetRolePermissionUseCase } from '../../../usecases/role_permission/get-role-permission.usecase';
import { GetRolePermissionsUseCase } from '../../../usecases/role_permission/get-role-permissions.usecase';
import { UpdateRolePermissionUseCase } from '../../../usecases/role_permission/update-role-permission.usecase';
import { RequirePermissions } from '../../common/decorators/require-permissions.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../../common/guards/permission.guard';
import {
  CreateRolePermissionDto,
  UpdateRolePermissionDto
} from './role-permission.dto';
import { RolePermissionPresenter } from './role-permission.presenter';

@Controller('role-permission')
@ApiTags('Role Permission')
@ApiResponse({ status: 403, description: 'Forbidden.' })
@ApiResponse({ status: 500, description: 'Internal Server Error.' })
@ApiExtraModels(RolePermissionPresenter)
export class RolePermissionController {
  constructor(
    @Inject(RolePermissionUsecasesProxy.POST_ROLE_PERMISSION_USECASE_PROXY)
    private readonly postRolePermissionUsecaseProxy: UseCaseProxy<CreateRolePermissionUseCase>,
    @Inject(RolePermissionUsecasesProxy.GET_ROLE_PERMISSIONS_USECASE_PROXY)
    private readonly getRolePermissionsUsecaseProxy: UseCaseProxy<GetRolePermissionsUseCase>,
    @Inject(RolePermissionUsecasesProxy.GET_ROLE_PERMISSION_USECASE_PROXY)
    private readonly getRolePermissionUsecaseProxy: UseCaseProxy<GetRolePermissionUseCase>,
    @Inject(RolePermissionUsecasesProxy.DELETE_ROLE_PERMISSION_USECASE_PROXY)
    private readonly deleteRolePermissionUsecaseProxy: UseCaseProxy<DeleteRolePermissionUseCase>,
    @Inject(RolePermissionUsecasesProxy.UPDATE_ROLE_PERMISSION_USECASE_PROXY)
    private readonly updateRolePermissionUsecaseProxy: UseCaseProxy<UpdateRolePermissionUseCase>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('role_permission.create')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: RolePermissionPresenter,
    description: 'Role Permission created'
  })
  async createRolePermission(
    @Body() body: CreateRolePermissionDto
  ): Promise<RolePermissionPresenter> {
    const rolePermission = await this.postRolePermissionUsecaseProxy
      .getInstance()
      .execute(body);
    return new RolePermissionPresenter(rolePermission);
  }

  @Put(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: RolePermissionPresenter,
    description: 'Role Permission updated'
  })
  async updateRolePermission(
    @Param('id') id: string,
    @Body() body: UpdateRolePermissionDto
  ): Promise<RolePermissionPresenter> {
    const rolePermission = await this.updateRolePermissionUsecaseProxy
      .getInstance()
      .execute(id, body);
    return new RolePermissionPresenter(rolePermission);
  }

  @Delete(':id')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Role Permission deleted'
  })
  async deleteRolePermission(@Param('id') id: string): Promise<void> {
    await this.deleteRolePermissionUsecaseProxy.getInstance().execute(id);
  }

  @Get(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: RolePermissionPresenter,
    description: 'Role Permission returned'
  })
  async getRolePermission(
    @Param('id') id: string
  ): Promise<RolePermissionPresenter> {
    const rolePermission = await this.getRolePermissionUsecaseProxy
      .getInstance()
      .execute(id);
    return rolePermission;
  }

  @Get()
  @ApiResponse({
    status: HttpStatus.OK,
    type: RolePermissionPresenter,
    description: 'Role Permissions returned'
  })
  async getRolePermissions(): Promise<RolePermissionPresenter[]> {
    const rolePermissions = await this.getRolePermissionsUsecaseProxy
      .getInstance()
      .execute();
    return rolePermissions;
  }
}
