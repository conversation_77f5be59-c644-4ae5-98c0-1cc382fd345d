import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';

export class CreateRolePermissionDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly role_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly permission_id: string;

  readonly created_by?: string;

  readonly created_at?: Date;
}

export class UpdateRolePermissionDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly role_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly permission_id: string;

  readonly updated_by: string;

  readonly updated_at: Date;
}
