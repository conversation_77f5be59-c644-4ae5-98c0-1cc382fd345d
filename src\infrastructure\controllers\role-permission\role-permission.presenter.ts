import { ApiProperty } from '@nestjs/swagger';
import { RolePermissionEntity } from '../../../domain/entities/role_permissions.entity';

export class RolePermissionPresenter {
  @ApiProperty()
  id: string;

  @ApiProperty()
  role_id: string;

  @ApiProperty()
  permission_id: string;

  @ApiProperty()
  created_by: string;

  @ApiProperty()
  updated_by: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;

  constructor(rolePermission: RolePermissionEntity) {
    this.id = rolePermission.id;
    this.role_id = rolePermission.role_id;
    this.permission_id = rolePermission.permission_id;
    this.created_by = rolePermission.created_by;
    this.updated_by = rolePermission.updated_by;
    this.created_at = rolePermission.created_at;
    this.updated_at = rolePermission.updated_at;
  }
}
