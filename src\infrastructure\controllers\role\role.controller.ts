import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { RoleUsecasesProxy } from '../../../infrastructure/usecases-proxy/role/role-usecases-proxy.constants';
import { UseCaseProxy } from '../../../infrastructure/usecases-proxy/usecases-proxy';
import { CreateRoleUseCase } from '../../../usecases/role/create-role.usecase';
import { DeleteRoleUseCase } from '../../../usecases/role/delete-role.usecase';
import { GetRoleUseCase } from '../../../usecases/role/get-role.usecase';
import { GetRolesUseCase } from '../../../usecases/role/get-roles.usecase';
import { UpdateRoleUseCase } from '../../../usecases/role/update-role.usecase';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { CreateRoleDto, UpdateRoleDto } from './role.dto';
import { RolePresenter } from './role.presenter';

@Controller('role')
@ApiTags('Role')
@ApiResponse({ status: 403, description: 'Forbidden.' })
@ApiResponse({ status: 500, description: 'Internal Server Error.' })
@ApiExtraModels(RolePresenter)
export class RoleController {
  constructor(
    @Inject(RoleUsecasesProxy.POST_ROLE_USECASE_PROXY)
    private readonly postRoleUsecaseProxy: UseCaseProxy<CreateRoleUseCase>,
    @Inject(RoleUsecasesProxy.GET_ROLES_USECASE_PROXY)
    private readonly getRolesUsecaseProxy: UseCaseProxy<GetRolesUseCase>,
    @Inject(RoleUsecasesProxy.GET_ROLE_USECASE_PROXY)
    private readonly getRoleUsecaseProxy: UseCaseProxy<GetRoleUseCase>,
    @Inject(RoleUsecasesProxy.DELETE_ROLE_USECASE_PROXY)
    private readonly deleteRoleUsecaseProxy: UseCaseProxy<DeleteRoleUseCase>,
    @Inject(RoleUsecasesProxy.UPDATE_ROLE_USECASE_PROXY)
    private readonly updateRoleUsecaseProxy: UseCaseProxy<UpdateRoleUseCase>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: RolePresenter,
    description: 'Role created'
  })
  async createRole(@Body() body: CreateRoleDto): Promise<RolePresenter> {
    const user = await this.postRoleUsecaseProxy.getInstance().execute(body);
    return new RolePresenter(user);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: RolePresenter,
    description: 'Role updated'
  })
  async updateRole(
    @Param('id') id: string,
    @Body() body: UpdateRoleDto
  ): Promise<RolePresenter> {
    const user = await this.updateRoleUsecaseProxy
      .getInstance()
      .execute(id, body);
    return new RolePresenter(user);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Role deleted'
  })
  async deleteRole(@Param('id') id: string): Promise<void> {
    await this.deleteRoleUsecaseProxy.getInstance().execute(id);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: RolePresenter,
    description: 'Role returned'
  })
  async getRole(@Param('id') id: string): Promise<RolePresenter> {
    const role = await this.getRoleUsecaseProxy.getInstance().execute(id);
    return role;
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: RolePresenter,
    description: 'Roles returned'
  })
  async getRoles(): Promise<RolePresenter[]> {
    const roles = await this.getRolesUsecaseProxy.getInstance().execute();
    return roles;
  }
}
