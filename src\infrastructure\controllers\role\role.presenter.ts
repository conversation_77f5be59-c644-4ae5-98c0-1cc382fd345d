import { ApiProperty } from '@nestjs/swagger';
import { RoleEntity } from '../../../domain/entities/role.entity';

export class RolePresenter {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  key: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  institution_id: string;

  @ApiProperty()
  created_by: string;

  @ApiProperty()
  updated_by: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;

  constructor(role: RoleEntity) {
    this.id = role.id;
    this.name = role.name;
    this.key = role.key;
    this.description = role.description;
    this.created_by = role.created_by;
    this.updated_by = role.updated_by;
    this.created_at = role.created_at;
    this.updated_at = role.updated_at;
  }
}
