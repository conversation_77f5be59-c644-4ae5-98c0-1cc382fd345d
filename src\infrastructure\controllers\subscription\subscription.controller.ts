import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { SubscriptionUsecasesProxy } from '../../../infrastructure/usecases-proxy/subscription/subscription-usecases-proxy.constants';
import { UseCaseProxy } from '../../../infrastructure/usecases-proxy/usecases-proxy';
import { CancelSubscriptionUseCase } from '../../../usecases/subscription/cancel-subscription.usecase';
import { CreateSubscriptionUseCase } from '../../../usecases/subscription/create-subscription.usecase';
import { GetInstitutionSubscriptionsUseCase } from '../../../usecases/subscription/get-institution-subscriptions.usecase';
import { GetSubscriptionUseCase } from '../../../usecases/subscription/get-subscription.usecase';
import { GetSubscriptionsUseCase } from '../../../usecases/subscription/get-subscriptions.usecase';
import { RequirePermissions } from '../../common/decorators/require-permissions.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { PermissionGuard } from '../../common/guards/permission.guard';
import { CreateSubscriptionDto } from './subscription.dto';
import { SubscriptionPresenter } from './subscription.presenter';

@Controller('subscription')
@ApiTags('Subscription')
@ApiResponse({ status: 403, description: 'Forbidden.' })
@ApiResponse({ status: 500, description: 'Internal Server Error.' })
@ApiExtraModels(SubscriptionPresenter)
export class SubscriptionController {
  constructor(
    @Inject(SubscriptionUsecasesProxy.POST_SUBSCRIPTION_USECASE_PROXY)
    private readonly postSubscriptionUsecaseProxy: UseCaseProxy<CreateSubscriptionUseCase>,
    @Inject(SubscriptionUsecasesProxy.GET_SUBSCRIPTIONS_USECASE_PROXY)
    private readonly getSubscriptionsUsecaseProxy: UseCaseProxy<GetSubscriptionsUseCase>,
    @Inject(SubscriptionUsecasesProxy.GET_SUBSCRIPTION_USECASE_PROXY)
    private readonly getSubscriptionUsecaseProxy: UseCaseProxy<GetSubscriptionUseCase>,
    @Inject(
      SubscriptionUsecasesProxy.GET_INSTITUTION_SUBSCRIPTIONS_USECASE_PROXY
    )
    private readonly getInstitutionSubscriptionsUsecaseProxy: UseCaseProxy<GetInstitutionSubscriptionsUseCase>,
    @Inject(SubscriptionUsecasesProxy.CANCEL_SUBSCRIPTION_USECASE_PROXY)
    private readonly cancelSubscriptionUsecaseProxy: UseCaseProxy<CancelSubscriptionUseCase>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, PermissionGuard)
  @RequirePermissions('subscription.create')
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: SubscriptionPresenter,
    description: 'Subscription created'
  })
  async createSubscription(
    @Body() body: CreateSubscriptionDto
  ): Promise<SubscriptionPresenter> {
    const subscription = await this.postSubscriptionUsecaseProxy
      .getInstance()
      .execute(body);
    return new SubscriptionPresenter(subscription);
  }

  @Put(':id/cancel')
  @ApiResponse({
    status: HttpStatus.OK,
    type: SubscriptionPresenter,
    description: 'Subscription canceled'
  })
  async cancelSubscription(
    @Param('id') id: string,
    @Body('canceled_by') canceledBy: string
  ): Promise<SubscriptionPresenter> {
    const subscription = await this.cancelSubscriptionUsecaseProxy
      .getInstance()
      .execute(id, canceledBy);
    return new SubscriptionPresenter(subscription);
  }

  @Get('institution/:institutionId')
  @ApiResponse({
    status: HttpStatus.OK,
    type: [SubscriptionPresenter],
    description: 'Institution subscriptions returned'
  })
  async getInstitutionSubscriptions(
    @Param('institutionId') institutionId: string
  ): Promise<SubscriptionPresenter[]> {
    const subscriptions = await this.getInstitutionSubscriptionsUsecaseProxy
      .getInstance()
      .execute(institutionId);
    return subscriptions.map(
      subscription => new SubscriptionPresenter(subscription)
    );
  }

  @Get(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: SubscriptionPresenter,
    description: 'Subscription returned'
  })
  async getSubscription(
    @Param('id') id: string
  ): Promise<SubscriptionPresenter> {
    const subscription = await this.getSubscriptionUsecaseProxy
      .getInstance()
      .execute(id);
    return new SubscriptionPresenter(subscription);
  }

  @Get()
  @ApiResponse({
    status: HttpStatus.OK,
    type: [SubscriptionPresenter],
    description: 'Subscriptions returned'
  })
  async getSubscriptions(): Promise<SubscriptionPresenter[]> {
    const subscriptions = await this.getSubscriptionsUsecaseProxy
      .getInstance()
      .execute();
    return subscriptions.map(
      subscription => new SubscriptionPresenter(subscription)
    );
  }
}
