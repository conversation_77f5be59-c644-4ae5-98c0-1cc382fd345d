import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateSubscriptionDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly institution_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly plan_id: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly status?: string = 'active';

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly stripe_subscription_id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly stripe_customer_id?: string;

  @ApiProperty()
  @IsOptional()
  readonly current_period_start?: Date;

  @ApiProperty()
  @IsOptional()
  readonly current_period_end?: Date;

  @ApiProperty()
  @IsOptional()
  readonly trial_start?: Date;

  @ApiProperty()
  @IsOptional()
  readonly trial_end?: Date;

  @ApiProperty()
  @IsOptional()
  readonly metadata?: Record<string, any>;

  readonly created_by?: string;

  readonly created_at?: Date;
}

export class UpdateSubscriptionDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly institution_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly plan_id: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly status?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly stripe_subscription_id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly stripe_customer_id?: string;

  @ApiProperty()
  @IsOptional()
  readonly current_period_start?: Date;

  @ApiProperty()
  @IsOptional()
  readonly current_period_end?: Date;

  @ApiProperty()
  @IsOptional()
  readonly trial_start?: Date;

  @ApiProperty()
  @IsOptional()
  readonly trial_end?: Date;

  @ApiProperty()
  @IsOptional()
  readonly canceled_at?: Date;

  @ApiProperty()
  @IsOptional()
  readonly ended_at?: Date;

  @ApiProperty()
  @IsOptional()
  readonly metadata?: Record<string, any>;

  readonly updated_by: string;

  readonly updated_at: Date;
}
