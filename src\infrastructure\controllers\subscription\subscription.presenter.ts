import { ApiProperty } from '@nestjs/swagger';
import { SubscriptionEntity } from '../../../domain/entities/subscription.entity';

export class SubscriptionPresenter {
  @ApiProperty()
  id: string;

  @ApiProperty()
  institution_id: string;

  @ApiProperty()
  plan_id: string;

  @ApiProperty()
  status: string;

  @ApiProperty()
  stripe_subscription_id?: string;

  @ApiProperty()
  stripe_customer_id?: string;

  @ApiProperty()
  current_period_start?: Date;

  @ApiProperty()
  current_period_end?: Date;

  @ApiProperty()
  trial_start?: Date;

  @ApiProperty()
  trial_end?: Date;

  @ApiProperty()
  canceled_at?: Date;

  @ApiProperty()
  ended_at?: Date;

  @ApiProperty()
  metadata: Record<string, any>;

  @ApiProperty()
  created_by: string;

  @ApiProperty()
  updated_by: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;

  constructor(subscription: SubscriptionEntity) {
    this.id = subscription.id;
    this.institution_id = subscription.institution_id;
    this.plan_id = subscription.plan_id;
    this.status = subscription.status;
    this.stripe_subscription_id = subscription.stripe_subscription_id;
    this.stripe_customer_id = subscription.stripe_customer_id;
    this.current_period_start = subscription.current_period_start;
    this.current_period_end = subscription.current_period_end;
    this.trial_start = subscription.trial_start;
    this.trial_end = subscription.trial_end;
    this.canceled_at = subscription.canceled_at;
    this.ended_at = subscription.ended_at;
    this.metadata = subscription.metadata;
    this.created_by = subscription.created_by;
    this.updated_by = subscription.updated_by;
    this.created_at = subscription.created_at;
    this.updated_at = subscription.updated_at;
  }
}
