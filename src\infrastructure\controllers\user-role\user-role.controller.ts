import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put
} from '@nestjs/common';
import { ApiExtraModels, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserRoleUsecasesProxy } from '../../../infrastructure/usecases-proxy/user-role/user-role-usecases-proxy.constants';
import { UseCaseProxy } from '../../../infrastructure/usecases-proxy/usecases-proxy';
import { CreateUserRoleUseCase } from '../../../usecases/user_role/create-user-role.usecase';
import { DeleteUserRoleUseCase } from '../../../usecases/user_role/delete-user-role.usecase';
import { GetUserRoleUseCase } from '../../../usecases/user_role/get-user-role.usecase';
import { GetUserRolesUseCase } from '../../../usecases/user_role/get-user-roles.usecase';
import { UpdateUserRoleUseCase } from '../../../usecases/user_role/update-user-role.usecase';
import { CreateUserRoleDto, UpdateUserRoleDto } from './user-role.dto';
import { UserRolePresenter } from './user-role.presenter';

@Controller('user-role')
@ApiTags('User Role')
@ApiResponse({ status: 403, description: 'Forbidden.' })
@ApiResponse({ status: 500, description: 'Internal Server Error.' })
@ApiExtraModels(UserRolePresenter)
export class UserRoleController {
  constructor(
    @Inject(UserRoleUsecasesProxy.POST_USER_ROLE_USECASE_PROXY)
    private readonly postUserRoleUsecaseProxy: UseCaseProxy<CreateUserRoleUseCase>,
    @Inject(UserRoleUsecasesProxy.GET_USER_ROLES_USECASE_PROXY)
    private readonly getUserRolesUsecaseProxy: UseCaseProxy<GetUserRolesUseCase>,
    @Inject(UserRoleUsecasesProxy.GET_USER_ROLE_USECASE_PROXY)
    private readonly getUserRoleUsecaseProxy: UseCaseProxy<GetUserRoleUseCase>,
    @Inject(UserRoleUsecasesProxy.DELETE_USER_ROLE_USECASE_PROXY)
    private readonly deleteUserRoleUsecaseProxy: UseCaseProxy<DeleteUserRoleUseCase>,
    @Inject(UserRoleUsecasesProxy.UPDATE_USER_ROLE_USECASE_PROXY)
    private readonly updateUserRoleUsecaseProxy: UseCaseProxy<UpdateUserRoleUseCase>
  ) {}

  @Post()
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: UserRolePresenter,
    description: 'User Role created'
  })
  async createUserRole(
    @Body() body: CreateUserRoleDto
  ): Promise<UserRolePresenter> {
    const userRole = await this.postUserRoleUsecaseProxy
      .getInstance()
      .execute(body);
    return new UserRolePresenter(userRole);
  }

  @Put(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: UserRolePresenter,
    description: 'User Role updated'
  })
  async updateUserRole(
    @Param('id') id: string,
    @Body() body: UpdateUserRoleDto
  ): Promise<UserRolePresenter> {
    const userRole = await this.updateUserRoleUsecaseProxy
      .getInstance()
      .execute(id, body);
    return new UserRolePresenter(userRole);
  }

  @Delete(':id')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'User Role deleted'
  })
  async deleteUserRole(@Param('id') id: string): Promise<void> {
    await this.deleteUserRoleUsecaseProxy.getInstance().execute(id);
  }

  @Get(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: UserRolePresenter,
    description: 'User Role returned'
  })
  async getUserRole(@Param('id') id: string): Promise<UserRolePresenter> {
    const userRole = await this.getUserRoleUsecaseProxy
      .getInstance()
      .execute(id);
    return userRole;
  }

  @Get()
  @ApiResponse({
    status: HttpStatus.OK,
    type: UserRolePresenter,
    description: 'User Roles returned'
  })
  async getUserRoles(): Promise<UserRolePresenter[]> {
    const userRoles = await this.getUserRolesUsecaseProxy
      .getInstance()
      .execute();
    return userRoles;
  }
}
