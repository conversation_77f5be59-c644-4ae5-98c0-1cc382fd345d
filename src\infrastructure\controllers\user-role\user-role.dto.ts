import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';

export class CreateUserRoleDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly role_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly user_id: string;

  readonly created_by?: string;

  readonly created_at?: Date;
}

export class UpdateUserRoleDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly role_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  readonly user_id: string;

  readonly updated_by: string;

  readonly updated_at: Date;
}
