import { ApiProperty } from '@nestjs/swagger';
import { UserRoleEntity } from '../../../domain/entities/user_role.entity';

export class UserRolePresenter {
  @ApiProperty()
  id: string;

  @ApiProperty()
  role_id: string;

  @ApiProperty()
  user_id: string;

  @ApiProperty()
  created_by: string;

  @ApiProperty()
  updated_by: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;

  constructor(userRole: UserRoleEntity) {
    this.id = userRole.id;
    this.role_id = userRole.role_id;
    this.user_id = userRole.user_id;
    this.created_by = userRole.created_by;
    this.updated_by = userRole.updated_by;
    this.created_at = userRole.created_at;
    this.updated_at = userRole.updated_at;
  }
}
