import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  UseGuards,
  UseInterceptors
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { UseCaseProxy } from '../../../infrastructure/usecases-proxy/usecases-proxy';
import { UserUsecasesProxy } from '../../../infrastructure/usecases-proxy/user/user-usecases-proxy.constants';
import { CreateUserUseCase } from '../../../usecases/user/create-user.usecase';
import { DeleteUserUseCase } from '../../../usecases/user/delete-user.usecase';
import { GetUserUseCase } from '../../../usecases/user/get-user.usecase';
import { GetUsersUseCase } from '../../../usecases/user/get-users.usecase';
import { UpdateUserUseCase } from '../../../usecases/user/update-user.usecase';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { AuditInterceptor } from '../../common/interceptors/audit.interceptor';
import { CreateUserDto, UpdateUserDto } from './user.dto';
import { UserPresenter } from './user.presenter';

@Controller('user')
@ApiTags('User')
@ApiResponse({ status: 403, description: 'Forbidden.' })
@ApiResponse({ status: 500, description: 'Internal Server Error.' })
@ApiExtraModels(UserPresenter)
export class UserController {
  constructor(
    @Inject(UserUsecasesProxy.POST_USER_USECASE_PROXY)
    private readonly postUserUsecaseProxy: UseCaseProxy<CreateUserUseCase>,
    @Inject(UserUsecasesProxy.UPDATE_USER_USECASE_PROXY)
    private readonly updateUserUsecaseProxy: UseCaseProxy<UpdateUserUseCase>,
    @Inject(UserUsecasesProxy.DELETE_USER_USECASE_PROXY)
    private readonly deleteUserUsecaseProxy: UseCaseProxy<DeleteUserUseCase>,
    @Inject(UserUsecasesProxy.GET_USER_USECASE_PROXY)
    private readonly getUserUsecaseProxy: UseCaseProxy<GetUserUseCase>,
    @Inject(UserUsecasesProxy.GET_USERS_USECASE_PROXY)
    private readonly getUsersUsecaseProxy: UseCaseProxy<GetUsersUseCase>
  ) {}

  @Post()
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: UserPresenter,
    description: 'User created'
  })
  async createUser(@Body() body: CreateUserDto): Promise<UserPresenter> {
    const user = await this.postUserUsecaseProxy.getInstance().execute(body);
    return new UserPresenter(user);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(AuditInterceptor)
  @ApiBearerAuth('JWT-auth')
  @ApiResponse({
    status: HttpStatus.OK,
    type: UserPresenter,
    description: 'User updated'
  })
  async updateUser(
    @Param('id') id: string,
    @Body() body: UpdateUserDto
  ): Promise<UserPresenter> {
    const user = await this.updateUserUsecaseProxy
      .getInstance()
      .execute(id, body, body.token);
    return new UserPresenter(user);
  }

  @Delete(':id')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'User deleted'
  })
  async deleteUser(
    @Param('id') id: string,
    @Body('token') token: string
  ): Promise<void> {
    await this.deleteUserUsecaseProxy.getInstance().execute(id, token);
  }

  @Get(':id')
  @ApiResponse({
    status: HttpStatus.OK,
    type: UserPresenter,
    description: 'User returned'
  })
  async getUser(@Param('id') id: string): Promise<UserPresenter> {
    const user = await this.getUserUsecaseProxy.getInstance().execute(id);
    return new UserPresenter(user);
  }

  @Get()
  @ApiResponse({
    status: HttpStatus.OK,
    type: UserPresenter,
    description: 'Users returned'
  })
  async getUsers(): Promise<UserPresenter[]> {
    const users = await this.getUsersUsecaseProxy.getInstance().execute();
    return users.map(user => new UserPresenter(user));
  }
}
