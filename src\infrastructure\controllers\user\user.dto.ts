import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateUserDto {
  @ApiProperty()
  @IsNotEmpty()
  readonly name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEmail()
  readonly email: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly phone_number: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly tax_id: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly birth_date: Date;

  @ApiProperty()
  @IsOptional()
  readonly institution_id?: string;

  readonly created_by?: string;

  readonly created_at?: Date;
}

export class UpdateUserDto {
  @ApiProperty()
  @IsNotEmpty()
  readonly id: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEmail()
  readonly email: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly phone_number: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly tax_id: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly birth_date: Date;

  @ApiProperty()
  @IsNotEmpty()
  readonly is_enabled_2fa_app: boolean;

  @ApiProperty()
  @IsNotEmpty()
  readonly is_email_verified: boolean;

  @ApiProperty()
  @IsNotEmpty()
  readonly is_phone_verified: boolean;

  @ApiProperty()
  @IsOptional()
  readonly institution_id?: string;

  @IsOptional()
  readonly newPassword: string;

  @IsOptional()
  readonly oldPassword: string;

  readonly token: string;

  readonly secret_key_2fa: string;

  readonly updated_by?: string;

  readonly updated_at?: Date;
}
