import { ApiProperty } from '@nestjs/swagger';
import { UserEntity } from '../../../domain/entities/user.entity';

export class UserPresenter {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  phone_number: string;

  @ApiProperty()
  tax_id: string;

  @ApiProperty()
  birth_date: Date;

  @ApiProperty()
  institution_id?: string;

  @ApiProperty()
  is_enabled_2fa_app: boolean;

  @ApiProperty()
  is_email_verified: boolean;

  @ApiProperty()
  is_phone_verified: boolean;

  @ApiProperty()
  secret_key_2fa: string;

  @ApiProperty()
  actived: boolean;

  @ApiProperty()
  created_by: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;

  constructor(user: UserEntity) {
    this.id = user.id;
    this.name = user.name;
    this.email = user.email;
    this.phone_number = user.phone_number;
    this.tax_id = user.tax_id;
    this.birth_date = user.birth_date;
    this.institution_id = user.institution_id;
    this.is_enabled_2fa_app = user.is_enabled_2fa_app;
    this.is_email_verified = user.is_email_verified;
    this.is_phone_verified = user.is_phone_verified;
    this.secret_key_2fa = user.secret_key_2fa;
    this.actived = user.actived;
    this.created_by = user.created_by;
    this.created_at = user.created_at;
    this.updated_at = user.updated_at;
  }
}
