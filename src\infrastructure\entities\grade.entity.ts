import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from 'typeorm';
import { Enrollment } from './enrollment.entity';
import { Institution } from './institution.entity';
import { User } from './user.entity';

@Entity({ schema: 'educational', name: 'grades' })
export class Grade {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  enrollment_id: string;

  @Column({ type: 'uuid', nullable: false })
  institution_id: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  assessment_type: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  assessment_name: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: false })
  grade: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: false })
  max_grade: number;

  @Column({
    type: 'decimal',
    precision: 3,
    scale: 2,
    nullable: false,
    default: 1.0
  })
  weight: number;

  @Column({ type: 'date', nullable: false })
  assessment_date: Date;

  @Column({ type: 'uuid', nullable: false })
  recorded_by: string;

  @Column({ type: 'uuid', nullable: false })
  created_by: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at?: Date;

  // Relations
  @ManyToOne(() => Enrollment)
  @JoinColumn({ name: 'enrollment_id' })
  enrollment: Enrollment;

  @ManyToOne(() => Institution)
  @JoinColumn({ name: 'institution_id' })
  institution: Institution;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'recorded_by' })
  recorder: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updater?: User;
}
