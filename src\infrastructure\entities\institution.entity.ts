import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm';
import { Address } from './address.entity';

@Entity({ name: 'institutions', schema: 'core' })
export class Institution {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar' })
  name: string; // Nome Fantasia

  @Column({ type: 'varchar' })
  legal_name: string; // Razão Social

  @Column({ type: 'varchar' })
  phone_number: string;

  @Column({ type: 'varchar', unique: true })
  tax_id: string; // CNPJ

  @Column({ type: 'date' })
  incorporation_date: Date; // Data de Fundação

  @Column({ type: 'uuid' })
  address_id: string;

  @OneToOne(() => Address, { onDelete: 'NO ACTION', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'address_id' })
  address: Address;

  @Column({ type: 'uuid', nullable: true })
  plan_id: string;

  @Column({ type: 'uuid', nullable: true, default: null })
  main_institution_id?: string;

  @ManyToOne(() => Institution, { onDelete: 'CASCADE', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'main_institution_id' })
  main_institution: Institution;

  @Column({ type: 'varchar', default: 'inactive' })
  status: string;

  @Column({ type: 'varchar' })
  email: string;

  @Column({ type: 'varchar' })
  website: string;

  @Column({ type: 'boolean', default: true })
  actived: boolean;

  @Column({ type: 'uuid' })
  created_by: string;

  @Column({ type: 'uuid' })
  updated_by: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    nullable: true,
    onUpdate: 'CURRENT_TIMESTAMP'
  })
  updated_at: Date;
}
