import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from 'typeorm';
import { Enrollment } from './enrollment.entity';
import { Institution } from './institution.entity';
import { PaymentMethod } from './payment-method.entity';
import { User } from './user.entity';

@Entity({ schema: 'core', name: 'invoices' })
@Index(['enrollment_id'])
@Index(['institution_id'])
@Index(['status'])
@Index(['due_date'])
@Index(['payment_date'])
@Index(['payment_method_id'])
@Index(['stripe_invoice_id'])
@Index(['stripe_payment_intent_id'])
@Index(['currency'])
export class Invoice {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  enrollment_id: string;

  @Column({ type: 'uuid', nullable: false })
  institution_id: string;

  @Column({ type: 'varchar', length: 50, nullable: false, unique: true })
  invoice_number: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: false })
  amount: number;

  @Column({ type: 'timestamp', nullable: false })
  due_date: Date;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: false,
    default: 'pending'
  })
  status: 'pending' | 'paid' | 'overdue' | 'cancelled' | 'processing' | 'succeeded' | 'failed' | 'refunded' | 'draft' | 'open' | 'void' | 'uncollectible';

  @Column({ type: 'timestamp', nullable: true })
  payment_date?: Date;

  @Column({ type: 'uuid', nullable: true })
  payment_method_id?: string;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  stripe_invoice_id?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  stripe_payment_intent_id?: string;

  @Column({ type: 'varchar', length: 3, nullable: false, default: 'BRL' })
  currency: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @Column({ type: 'uuid', nullable: false })
  created_by: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at?: Date;

  // Relations
  @ManyToOne(() => Enrollment)
  @JoinColumn({ name: 'enrollment_id' })
  enrollment: Enrollment;

  @ManyToOne(() => Institution)
  @JoinColumn({ name: 'institution_id' })
  institution: Institution;

  @ManyToOne(() => PaymentMethod)
  @JoinColumn({ name: 'payment_method_id' })
  paymentMethod?: PaymentMethod;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updater?: User;
}
