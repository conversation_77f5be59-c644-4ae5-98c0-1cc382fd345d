import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON><PERSON>n,
  <PERSON><PERSON>ty,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  Index
} from 'typeorm';
import { Institution } from './institution.entity';
import { User } from './user.entity';

@Entity({ schema: 'financial', name: 'payment_methods' })
@Index(['institution_id'])
@Index(['stripe_payment_method_id'])
@Index(['stripe_customer_id'])
export class PaymentMethod {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  institution_id: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  stripe_payment_method_id: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  stripe_customer_id: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: false,
    default: 'card'
  })
  type: 'card' | 'boleto' | 'pix';

  @Column({ type: 'varchar', length: 50, nullable: true })
  card_brand?: string;

  @Column({ type: 'varchar', length: 4, nullable: true })
  card_last4?: string;

  @Column({ type: 'integer', nullable: true })
  card_exp_month?: number;

  @Column({ type: 'integer', nullable: true })
  card_exp_year?: number;

  @Column({ type: 'boolean', nullable: false, default: false })
  is_default: boolean;

  @Column({ type: 'uuid', nullable: false })
  created_by: string;

  @CreateDateColumn()
  created_at: Date;

  // Relations
  @ManyToOne(() => Institution)
  @JoinColumn({ name: 'institution_id' })
  institution: Institution;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;
}
