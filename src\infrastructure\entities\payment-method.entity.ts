import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm';
import { Institution } from './institution.entity';
import { User } from './user.entity';

@Entity({ schema: 'core', name: 'payment_methods' })
@Index(['institution_id'])
@Index(['stripe_payment_method_id'])
@Index(['stripe_customer_id'])
@Index(['is_default'])
export class PaymentMethod {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  institution_id: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false
  })
  type: 'card' | 'boleto' | 'pix' | 'bank_transfer' | 'cash' | 'check';

  @Column({ type: 'boolean', nullable: false, default: true })
  is_active: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  stripe_payment_method_id?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  stripe_customer_id?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  card_brand?: string;

  @Column({ type: 'varchar', length: 4, nullable: true })
  card_last4?: string;

  @Column({ type: 'integer', nullable: true })
  card_exp_month?: number;

  @Column({ type: 'integer', nullable: true })
  card_exp_year?: number;

  @Column({ type: 'boolean', nullable: false, default: false })
  is_default: boolean;

  @Column({ type: 'uuid', nullable: false })
  created_by: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at?: Date;

  // Relations
  @ManyToOne(() => Institution)
  @JoinColumn({ name: 'institution_id' })
  institution: Institution;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updater?: User;
}
