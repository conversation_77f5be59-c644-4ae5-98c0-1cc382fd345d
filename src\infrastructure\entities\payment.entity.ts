import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm';
import { Institution } from './institution.entity';
import { PaymentMethod } from './payment-method.entity';
import { User } from './user.entity';

@Entity({ schema: 'core', name: 'financial_transactions' })
@Index(['institution_id'])
@Index(['stripe_payment_intent_id'])
@Index(['stripe_session_id'])
@Index(['status'])
@Index(['payment_type'])
@Index(['currency'])
export class FinancialTransaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  institution_id: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  stripe_payment_intent_id?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  stripe_session_id?: string;

  @Column({ type: 'varchar', length: 20, nullable: false })
  type: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  category: string;

  @Column({ type: 'decimal', precision: 12, scale: 2, nullable: false })
  amount: number;

  @Column({ type: 'varchar', length: 3, nullable: false, default: 'BRL' })
  currency: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: false,
    default: 'pending'
  })
  status: 'pending' | 'completed' | 'cancelled';

  @Column({
    type: 'varchar',
    length: 20,
    nullable: false,
    default: 'one_time'
  })
  payment_type: 'one_time' | 'subscription' | 'refund';

  @Column({ type: 'text', nullable: false })
  description: string;

  @Column({ type: 'uuid', nullable: true })
  reference_id?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  reference_type?: string;

  @Column({ type: 'uuid', nullable: true })
  payment_method_id?: string;

  @Column({ type: 'timestamp', nullable: false })
  transaction_date: Date;

  @Column({ type: 'timestamp', nullable: true })
  due_date?: Date;

  @Column({ type: 'timestamp', nullable: true })
  paid_date?: Date;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @Column({ type: 'uuid', nullable: false })
  created_by: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at?: Date;

  // Relations
  @ManyToOne(() => Institution)
  @JoinColumn({ name: 'institution_id' })
  institution: Institution;

  @ManyToOne(() => PaymentMethod)
  @JoinColumn({ name: 'payment_method_id' })
  paymentMethod?: PaymentMethod;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updater?: User;
}
