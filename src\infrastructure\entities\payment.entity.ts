import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Index
} from 'typeorm';
import { Institution } from './institution.entity';
import { User } from './user.entity';

@Entity({ schema: 'financial', name: 'payments' })
@Index(['institution_id'])
@Index(['stripe_payment_intent_id'])
@Index(['stripe_session_id'])
@Index(['status'])
export class Payment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  institution_id: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  stripe_payment_intent_id?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  stripe_session_id?: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: false })
  amount: number;

  @Column({ type: 'varchar', length: 3, nullable: false, default: 'BRL' })
  currency: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: false,
    default: 'pending'
  })
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'canceled';

  @Column({
    type: 'varchar',
    length: 20,
    nullable: false,
    default: 'one_time'
  })
  payment_type: 'one_time' | 'subscription';

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @Column({ type: 'uuid', nullable: false })
  created_by: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at?: Date;

  // Relations
  @ManyToOne(() => Institution)
  @JoinColumn({ name: 'institution_id' })
  institution: Institution;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updater?: User;
}
