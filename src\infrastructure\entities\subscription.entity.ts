import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON>ty,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm';
import { Institution } from './institution.entity';
import { Plan } from './plan.entity';

@Entity({ name: 'subscriptions', schema: 'core' })
export class Subscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  institution_id: string;

  @ManyToOne(() => Institution, { onDelete: 'CASCADE', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'institution_id' })
  institution: Institution;

  @Column({ type: 'uuid' })
  plan_id: string;

  @ManyToOne(() => Plan, { onDelete: 'RESTRICT', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'plan_id' })
  plan: Plan;

  @Column({ type: 'varchar', length: 20, default: 'active' })
  status: string;

  @Column({ type: 'varchar', nullable: true })
  stripe_subscription_id: string;

  @Column({ type: 'varchar', nullable: true })
  stripe_customer_id: string;

  @Column({ type: 'timestamp', nullable: true })
  current_period_start: Date;

  @Column({ type: 'timestamp', nullable: true })
  current_period_end: Date;

  @Column({ type: 'timestamp', nullable: true })
  trial_start: Date;

  @Column({ type: 'timestamp', nullable: true })
  trial_end: Date;

  @Column({ type: 'timestamp', nullable: true })
  canceled_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  ended_at: Date;

  @Column({ type: 'jsonb', default: '{}' })
  metadata: Record<string, any>;

  @Column({ type: 'uuid' })
  created_by: string;

  @Column({ type: 'uuid' })
  updated_by: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    nullable: true,
    onUpdate: 'CURRENT_TIMESTAMP'
  })
  updated_at: Date;
}
