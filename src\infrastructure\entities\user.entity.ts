import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON>tity,
  <PERSON>in<PERSON><PERSON>umn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm';
import { Institution } from './institution.entity';
import { UserRole } from './user_role.entity';

@Entity({ name: 'users', schema: 'core' })
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', unique: true })
  email: string;

  @Column({ type: 'varchar', nullable: true })
  password_hash: string;

  @Column({ type: 'varchar', nullable: true })
  phone_number: string;

  @Column({ type: 'varchar', nullable: true })
  tax_id: string;

  @Column({ type: 'date', nullable: true })
  birth_date: Date;

  @Column({ type: 'uuid', nullable: true })
  institution_id: string;

  @OneToOne(() => Institution, { onDelete: 'NO ACTION', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'institution_id' })
  institution: Institution;

  @Column({ type: 'boolean', default: false })
  is_enabled_2fa_app: boolean;

  @Column({ type: 'boolean', default: false })
  is_email_verified: boolean;

  @Column({ type: 'boolean', default: false })
  is_phone_verified: boolean;

  @Column({ type: 'varchar', nullable: true })
  secret_key_2fa: string;

  @Column({ type: 'boolean', default: true })
  actived: boolean;

  @Column({ type: 'uuid', nullable: true })
  created_by: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    nullable: true,
    onUpdate: 'CURRENT_TIMESTAMP'
  })
  updated_at: Date;

  @OneToMany(() => UserRole, userRole => userRole.user)
  userRoles: UserRole[];
}
