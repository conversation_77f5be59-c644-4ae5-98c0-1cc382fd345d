import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IAddressRepository } from '../../domain/abstractions/address-repository.abstraction';
import { AddressEntity } from '../../domain/entities/address.entity';
import { Address } from '../entities/address.entity';

@Injectable()
export class AddressRepository implements IAddressRepository {
  constructor(
    @InjectRepository(Address)
    private readonly addressTypeOrmRepository: Repository<Address>
  ) {}
  async findByZipCode(zip_code: string): Promise<AddressEntity | null> {
    const address = await this.addressTypeOrmRepository.findOne({
      where: { zip_code }
    });
    return address;
  }

  async insert(Address: Partial<AddressEntity>): Promise<AddressEntity> {
    const newAddress = await this.addressTypeOrmRepository.save(Address);
    return newAddress;
  }

  async findAll(): Promise<AddressEntity[]> {
    const address = await this.addressTypeOrmRepository.find();
    return address;
  }

  async findById(id: string): Promise<AddressEntity | null> {
    const address = await this.addressTypeOrmRepository.findOne({
      where: { id }
    });
    return address;
  }

  async update(id: string, address: AddressEntity): Promise<AddressEntity> {
    const updatedAddress = await this.addressTypeOrmRepository.save({
      ...address,
      id
    });
    return updatedAddress;
  }

  async delete(id: string): Promise<void> {
    await this.addressTypeOrmRepository.delete(id);
  }
}
