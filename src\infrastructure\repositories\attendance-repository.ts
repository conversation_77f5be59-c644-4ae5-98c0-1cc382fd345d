import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { AttendanceEntity } from '../../domain/entities/attendance.entity';
import { AttendanceRepository } from '../../domain/abstractions/attendance-repository.abstraction';
import { Attendance } from '../entities/attendance.entity';

@Injectable()
export class DatabaseAttendanceRepository implements AttendanceRepository {
  constructor(
    @InjectRepository(Attendance)
    private readonly attendanceRepository: Repository<Attendance>
  ) {}

  async create(
    attendanceData: Partial<AttendanceEntity>
  ): Promise<AttendanceEntity> {
    const attendanceEntity = this.attendanceRepository.create(attendanceData);
    const savedAttendance =
      await this.attendanceRepository.save(attendanceEntity);
    return this.toModel(savedAttendance);
  }

  async findAll(): Promise<AttendanceEntity[]> {
    const attendances = await this.attendanceRepository.find({
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { class_date: 'DESC', created_at: 'DESC' }
    });
    return attendances.map(attendance => this.toModel(attendance));
  }

  async findById(id: string): Promise<AttendanceEntity> {
    const attendance = await this.attendanceRepository.findOne({
      where: { id },
      relations: ['enrollment', 'recorder', 'creator', 'updater']
    });
    if (!attendance) {
      throw new Error('Attendance record not found');
    }
    return this.toModel(attendance);
  }

  async findByEnrollmentId(enrollmentId: string): Promise<AttendanceEntity[]> {
    const attendances = await this.attendanceRepository.find({
      where: { enrollment_id: enrollmentId },
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { class_date: 'DESC' }
    });
    return attendances.map(attendance => this.toModel(attendance));
  }

  async findByClassDate(classDate: Date): Promise<AttendanceEntity[]> {
    const attendances = await this.attendanceRepository.find({
      where: { class_date: classDate },
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return attendances.map(attendance => this.toModel(attendance));
  }

  async findByEnrollmentAndDate(
    enrollmentId: string,
    classDate: Date
  ): Promise<AttendanceEntity | null> {
    const attendance = await this.attendanceRepository.findOne({
      where: {
        enrollment_id: enrollmentId,
        class_date: classDate
      },
      relations: ['enrollment', 'recorder', 'creator', 'updater']
    });
    return attendance ? this.toModel(attendance) : null;
  }

  async findByStatus(status: string): Promise<AttendanceEntity[]> {
    const attendances = await this.attendanceRepository.find({
      where: { status: status as 'present' | 'absent' | 'late' | 'excused' },
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { class_date: 'DESC' }
    });
    return attendances.map(attendance => this.toModel(attendance));
  }

  async findByDateRange(
    startDate: Date,
    endDate: Date
  ): Promise<AttendanceEntity[]> {
    const attendances = await this.attendanceRepository.find({
      where: {
        class_date: Between(startDate, endDate)
      },
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { class_date: 'DESC' }
    });
    return attendances.map(attendance => this.toModel(attendance));
  }

  async findByEnrollmentAndDateRange(
    enrollmentId: string,
    startDate: Date,
    endDate: Date
  ): Promise<AttendanceEntity[]> {
    const attendances = await this.attendanceRepository.find({
      where: {
        enrollment_id: enrollmentId,
        class_date: Between(startDate, endDate)
      },
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { class_date: 'DESC' }
    });
    return attendances.map(attendance => this.toModel(attendance));
  }

  async update(
    id: string,
    attendanceData: Partial<AttendanceEntity>
  ): Promise<AttendanceEntity> {
    await this.attendanceRepository.update(id, attendanceData);
    const updatedAttendance = await this.findById(id);
    return updatedAttendance;
  }

  async delete(id: string): Promise<void> {
    const result = await this.attendanceRepository.delete(id);
    if (result.affected === 0) {
      throw new Error('Attendance record not found');
    }
  }

  async getAttendanceStats(enrollmentId: string): Promise<{
    total: number;
    present: number;
    absent: number;
    late: number;
    excused: number;
    attendanceRate: number;
  }> {
    const attendances = await this.attendanceRepository.find({
      where: { enrollment_id: enrollmentId }
    });

    const stats = {
      total: attendances.length,
      present: attendances.filter(a => a.status === 'present').length,
      absent: attendances.filter(a => a.status === 'absent').length,
      late: attendances.filter(a => a.status === 'late').length,
      excused: attendances.filter(a => a.status === 'excused').length,
      attendanceRate: 0
    };

    if (stats.total > 0) {
      stats.attendanceRate = ((stats.present + stats.late) / stats.total) * 100;
    }

    return stats;
  }

  private toModel(attendance: Attendance): AttendanceEntity {
    return {
      id: attendance.id,
      enrollment_id: attendance.enrollment_id,
      class_date: attendance.class_date,
      status: attendance.status,
      notes: attendance.notes,
      recorded_by: attendance.recorded_by,
      created_by: attendance.created_by,
      updated_by: attendance.updated_by,
      created_at: attendance.created_at,
      updated_at: attendance.updated_at
    };
  }
}
