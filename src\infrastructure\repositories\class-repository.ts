import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClassEntity } from '../../domain/entities/class.entity';
import { ClassRepository } from '../../domain/abstractions/class-repository.abstraction';
import { Class } from '../entities/class.entity';

@Injectable()
export class DatabaseClassRepository implements ClassRepository {
  constructor(
    @InjectRepository(Class)
    private readonly classRepository: Repository<Class>
  ) {}

  async create(classData: Partial<ClassEntity>): Promise<ClassEntity> {
    const classEntity = this.classRepository.create(classData);
    const savedClass = await this.classRepository.save(classEntity);
    return this.toModel(savedClass);
  }

  async findAll(): Promise<ClassEntity[]> {
    const classes = await this.classRepository.find({
      relations: ['course', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return classes.map(classItem => this.toModel(classItem));
  }

  async findById(id: string): Promise<ClassEntity> {
    const classItem = await this.classRepository.findOne({
      where: { id },
      relations: ['course', 'creator', 'updater']
    });
    if (!classItem) {
      throw new Error('Class not found');
    }
    return this.toModel(classItem);
  }

  async findByCourseId(courseId: string): Promise<ClassEntity[]> {
    const classes = await this.classRepository.find({
      where: { course_id: courseId },
      relations: ['course', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return classes.map(classItem => this.toModel(classItem));
  }

  async findByStatus(status: string): Promise<ClassEntity[]> {
    const classes = await this.classRepository.find({
      where: {
        status: status as 'open' | 'closed' | 'in_progress' | 'finished'
      },
      relations: ['course', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return classes.map(classItem => this.toModel(classItem));
  }

  async findAvailableClasses(): Promise<ClassEntity[]> {
    const classes = await this.classRepository
      .createQueryBuilder('class')
      .where('class.status IN (:...statuses)', {
        statuses: ['open', 'in_progress']
      })
      .andWhere('class.current_students < class.max_students')
      .leftJoinAndSelect('class.course', 'course')
      .leftJoinAndSelect('class.creator', 'creator')
      .leftJoinAndSelect('class.updater', 'updater')
      .orderBy('class.created_at', 'DESC')
      .getMany();

    return classes.map(classItem => this.toModel(classItem));
  }

  async update(
    id: string,
    classData: Partial<ClassEntity>
  ): Promise<ClassEntity> {
    await this.classRepository.update(id, classData);
    const updatedClass = await this.findById(id);
    return updatedClass;
  }

  async delete(id: string): Promise<void> {
    const result = await this.classRepository.delete(id);
    if (result.affected === 0) {
      throw new Error('Class not found');
    }
  }

  async incrementStudentCount(id: string): Promise<ClassEntity> {
    await this.classRepository.increment({ id }, 'current_students', 1);
    return await this.findById(id);
  }

  async decrementStudentCount(id: string): Promise<ClassEntity> {
    await this.classRepository.decrement({ id }, 'current_students', 1);
    return await this.findById(id);
  }

  private toModel(classEntity: Class): ClassEntity {
    return {
      id: classEntity.id,
      course_id: classEntity.course_id,
      name: classEntity.name,
      description: classEntity.description,
      max_students: classEntity.max_students,
      current_students: classEntity.current_students,
      status: classEntity.status,
      start_date: classEntity.start_date,
      end_date: classEntity.end_date,
      schedule: classEntity.schedule,
      created_by: classEntity.created_by,
      updated_by: classEntity.updated_by,
      created_at: classEntity.created_at,
      updated_at: classEntity.updated_at
    };
  }
}
