import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CourseRepository } from '../../domain/abstractions/course-repository.abstraction';
import { CourseEntity } from '../../domain/entities/course.entity';
import { Course } from '../entities/course.entity';

@Injectable()
export class DatabaseCourseRepository implements CourseRepository {
  constructor(
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>
  ) {}

  async create(course: Partial<CourseEntity>): Promise<CourseEntity> {
    const courseEntity = this.courseRepository.create(course);
    const savedCourse = await this.courseRepository.save(courseEntity);
    return savedCourse;
  }

  async findAll(): Promise<CourseEntity[]> {
    const courses = await this.courseRepository.find({
      relations: ['institution', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return courses.map(course => course);
  }

  async findById(id: string): Promise<CourseEntity> {
    const course = await this.courseRepository.findOne({
      where: { id },
      relations: ['institution', 'creator', 'updater']
    });
    if (!course) {
      throw new Error('Course not found');
    }
    return course;
  }

  async findByInstitutionId(institutionId: string): Promise<CourseEntity[]> {
    const courses = await this.courseRepository.find({
      where: { institution_id: institutionId },
      relations: ['institution', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return courses.map(course => course);
  }

  async findByName(name: string): Promise<CourseEntity> {
    const course = await this.courseRepository.findOne({
      where: { name },
      relations: ['institution', 'creator', 'updater']
    });
    if (!course) {
      throw new Error('Course not found');
    }
    return course;
  }

  async findByStatus(status: string): Promise<CourseEntity[]> {
    const courses = await this.courseRepository.find({
      where: {
        status: status as 'active' | 'inactive' | 'completed' | 'cancelled'
      },
      relations: ['institution', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return courses.map(course => course);
  }

  async update(
    id: string,
    courseData: Partial<CourseEntity>
  ): Promise<CourseEntity> {
    await this.courseRepository.update(id, courseData);
    const updatedCourse = await this.findById(id);
    return updatedCourse;
  }

  async delete(id: string): Promise<void> {
    const result = await this.courseRepository.delete(id);
    if (result.affected === 0) {
      throw new Error('Course not found');
    }
  }
}
