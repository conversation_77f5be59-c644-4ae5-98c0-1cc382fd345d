import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Course } from '../../domain/entities/course.entity';
import { CourseRepository } from '../../domain/abstractions/course-repository.abstraction';
import { CourseEntity } from '../entities/course.entity';

@Injectable()
export class DatabaseCourseRepository implements CourseRepository {
  constructor(
    @InjectRepository(CourseEntity)
    private readonly courseRepository: Repository<CourseEntity>
  ) {}

  async create(course: Course): Promise<Course> {
    const courseEntity = this.courseRepository.create(course);
    const savedCourse = await this.courseRepository.save(courseEntity);
    return this.toModel(savedCourse);
  }

  async findAll(): Promise<Course[]> {
    const courses = await this.courseRepository.find({
      relations: ['institution', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return courses.map(course => this.toModel(course));
  }

  async findById(id: string): Promise<Course> {
    const course = await this.courseRepository.findOne({
      where: { id },
      relations: ['institution', 'creator', 'updater']
    });
    if (!course) {
      throw new Error('Course not found');
    }
    return this.toModel(course);
  }

  async findByInstitutionId(institutionId: string): Promise<Course[]> {
    const courses = await this.courseRepository.find({
      where: { institution_id: institutionId },
      relations: ['institution', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return courses.map(course => this.toModel(course));
  }

  async findByCode(code: string): Promise<Course> {
    const course = await this.courseRepository.findOne({
      where: { code },
      relations: ['institution', 'creator', 'updater']
    });
    if (!course) {
      throw new Error('Course not found');
    }
    return this.toModel(course);
  }

  async findByStatus(status: string): Promise<Course[]> {
    const courses = await this.courseRepository.find({
      where: { status: status as 'active' | 'inactive' | 'archived' },
      relations: ['institution', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return courses.map(course => this.toModel(course));
  }

  async update(id: string, courseData: Partial<Course>): Promise<Course> {
    await this.courseRepository.update(id, courseData);
    const updatedCourse = await this.findById(id);
    return updatedCourse;
  }

  async delete(id: string): Promise<void> {
    const result = await this.courseRepository.delete(id);
    if (result.affected === 0) {
      throw new Error('Course not found');
    }
  }

  private toModel(courseEntity: CourseEntity): Course {
    return {
      id: courseEntity.id,
      name: courseEntity.name,
      description: courseEntity.description,
      code: courseEntity.code,
      credits: courseEntity.credits,
      duration_hours: courseEntity.duration_hours,
      status: courseEntity.status,
      institution_id: courseEntity.institution_id,
      created_by: courseEntity.created_by,
      updated_by: courseEntity.updated_by,
      created_at: courseEntity.created_at,
      updated_at: courseEntity.updated_at
    };
  }
}
