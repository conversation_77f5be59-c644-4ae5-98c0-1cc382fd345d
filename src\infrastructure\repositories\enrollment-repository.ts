import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EnrollmentEntity } from '../../domain/entities/enrollment.entity';
import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';
import { Enrollment } from '../entities/enrollment.entity';

@Injectable()
export class DatabaseEnrollmentRepository implements EnrollmentRepository {
  constructor(
    @InjectRepository(Enrollment)
    private readonly enrollmentRepository: Repository<Enrollment>
  ) {}

  async create(
    enrollmentData: Partial<EnrollmentEntity>
  ): Promise<EnrollmentEntity> {
    const enrollmentEntity = this.enrollmentRepository.create(enrollmentData);
    const savedEnrollment =
      await this.enrollmentRepository.save(enrollmentEntity);
    return this.toModel(savedEnrollment);
  }

  async findAll(): Promise<EnrollmentEntity[]> {
    const enrollments = await this.enrollmentRepository.find({
      relations: [
        'student',
        'class',
        'institution',
        'approver',
        'creator',
        'updater'
      ],
      order: { created_at: 'DESC' }
    });
    return enrollments.map(enrollment => this.toModel(enrollment));
  }

  async findById(id: string): Promise<EnrollmentEntity> {
    const enrollment = await this.enrollmentRepository.findOne({
      where: { id },
      relations: [
        'student',
        'class',
        'institution',
        'approver',
        'creator',
        'updater'
      ]
    });
    if (!enrollment) {
      throw new Error('Enrollment not found');
    }
    return this.toModel(enrollment);
  }

  async findByStudentId(studentId: string): Promise<EnrollmentEntity[]> {
    const enrollments = await this.enrollmentRepository.find({
      where: { student_id: studentId },
      relations: [
        'student',
        'class',
        'institution',
        'approver',
        'creator',
        'updater'
      ],
      order: { created_at: 'DESC' }
    });
    return enrollments.map(enrollment => this.toModel(enrollment));
  }

  async findByClassId(classId: string): Promise<EnrollmentEntity[]> {
    const enrollments = await this.enrollmentRepository.find({
      where: { class_id: classId },
      relations: [
        'student',
        'class',
        'institution',
        'approver',
        'creator',
        'updater'
      ],
      order: { created_at: 'DESC' }
    });
    return enrollments.map(enrollment => this.toModel(enrollment));
  }

  async findByInstitutionId(
    institutionId: string
  ): Promise<EnrollmentEntity[]> {
    const enrollments = await this.enrollmentRepository.find({
      where: { institution_id: institutionId },
      relations: [
        'student',
        'class',
        'institution',
        'approver',
        'creator',
        'updater'
      ],
      order: { created_at: 'DESC' }
    });
    return enrollments.map(enrollment => this.toModel(enrollment));
  }

  async findByStatus(status: string): Promise<EnrollmentEntity[]> {
    const enrollments = await this.enrollmentRepository.find({
      where: {
        status: status as
          | 'pending'
          | 'approved'
          | 'rejected'
          | 'cancelled'
          | 'completed'
      },
      relations: [
        'student',
        'class',
        'institution',
        'approver',
        'creator',
        'updater'
      ],
      order: { created_at: 'DESC' }
    });
    return enrollments.map(enrollment => this.toModel(enrollment));
  }

  async findPendingEnrollments(): Promise<EnrollmentEntity[]> {
    const enrollments = await this.enrollmentRepository.find({
      where: { status: 'pending' },
      relations: [
        'student',
        'class',
        'institution',
        'approver',
        'creator',
        'updater'
      ],
      order: { enrollment_date: 'ASC' }
    });
    return enrollments.map(enrollment => this.toModel(enrollment));
  }

  async findActiveEnrollments(): Promise<EnrollmentEntity[]> {
    const enrollments = await this.enrollmentRepository.find({
      where: { status: 'approved' },
      relations: [
        'student',
        'class',
        'institution',
        'approver',
        'creator',
        'updater'
      ],
      order: { approval_date: 'DESC' }
    });
    return enrollments.map(enrollment => this.toModel(enrollment));
  }

  async findStudentEnrollmentInClass(
    studentId: string,
    classId: string
  ): Promise<EnrollmentEntity | null> {
    const enrollment = await this.enrollmentRepository.findOne({
      where: {
        student_id: studentId,
        class_id: classId
      },
      relations: [
        'student',
        'class',
        'institution',
        'approver',
        'creator',
        'updater'
      ]
    });
    return enrollment ? this.toModel(enrollment) : null;
  }

  async update(
    id: string,
    enrollmentData: Partial<EnrollmentEntity>
  ): Promise<EnrollmentEntity> {
    await this.enrollmentRepository.update(id, enrollmentData);
    const updatedEnrollment = await this.findById(id);
    return updatedEnrollment;
  }

  async delete(id: string): Promise<void> {
    const result = await this.enrollmentRepository.delete(id);
    if (result.affected === 0) {
      throw new Error('Enrollment not found');
    }
  }

  async approveEnrollment(
    id: string,
    approvedBy: string,
    notes?: string
  ): Promise<EnrollmentEntity> {
    const updateData = {
      status: 'approved' as const,
      approval_date: new Date(),
      approved_by: approvedBy,
      updated_by: approvedBy,
      notes: notes || undefined,
      updated_at: new Date()
    };

    await this.enrollmentRepository.update(id, updateData);
    return await this.findById(id);
  }

  async rejectEnrollment(
    id: string,
    rejectionReason: string,
    updatedBy: string
  ): Promise<EnrollmentEntity> {
    const updateData = {
      status: 'rejected' as const,
      rejection_reason: rejectionReason,
      updated_by: updatedBy,
      updated_at: new Date()
    };

    await this.enrollmentRepository.update(id, updateData);
    return await this.findById(id);
  }

  async cancelEnrollment(
    id: string,
    updatedBy: string,
    notes?: string
  ): Promise<EnrollmentEntity> {
    const updateData = {
      status: 'cancelled' as const,
      updated_by: updatedBy,
      notes: notes || undefined,
      updated_at: new Date()
    };

    await this.enrollmentRepository.update(id, updateData);
    return await this.findById(id);
  }

  private toModel(enrollment: Enrollment): EnrollmentEntity {
    return {
      id: enrollment.id,
      student_id: enrollment.student_id,
      class_id: enrollment.class_id,
      institution_id: enrollment.institution_id,
      status: enrollment.status,
      enrollment_date: enrollment.enrollment_date,
      approval_date: enrollment.approval_date,
      approved_by: enrollment.approved_by,
      rejection_reason: enrollment.rejection_reason,
      notes: enrollment.notes,
      created_by: enrollment.created_by,
      updated_by: enrollment.updated_by,
      created_at: enrollment.created_at,
      updated_at: enrollment.updated_at
    };
  }
}
