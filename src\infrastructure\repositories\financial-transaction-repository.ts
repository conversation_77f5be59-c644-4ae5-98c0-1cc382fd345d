import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, Repository } from 'typeorm';
import { FinancialTransactionRepository } from '../../domain/abstractions/financial-repository.abstraction';
import { FinancialTransactionEntity } from '../../domain/entities/payment.entity';
import { FinancialTransaction } from '../entities/payment.entity';

@Injectable()
export class DatabaseFinancialTransactionRepository
  implements FinancialTransactionRepository
{
  constructor(
    @InjectRepository(FinancialTransaction)
    private readonly repository: Repository<FinancialTransaction>
  ) {}

  async create(
    transactionData: Partial<FinancialTransactionEntity>
  ): Promise<FinancialTransactionEntity> {
    const transaction = this.repository.create(transactionData as any);
    const savedTransaction = await this.repository.save(transaction);
    const result = Array.isArray(savedTransaction)
      ? savedTransaction[0]
      : savedTransaction;
    return this.mapToEntity(result);
  }

  async findAll(): Promise<FinancialTransactionEntity[]> {
    const transactions = await this.repository.find({
      relations: ['institution', 'paymentMethod', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return transactions.map(this.mapToEntity);
  }

  async findAllByInstitution(
    institutionId: string
  ): Promise<FinancialTransactionEntity[]> {
    const transactions = await this.repository.find({
      where: { institution_id: institutionId },
      relations: ['institution', 'paymentMethod', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return transactions.map(this.mapToEntity);
  }

  async findById(id: string): Promise<FinancialTransactionEntity> {
    const transaction = await this.repository.findOne({
      where: { id },
      relations: ['institution', 'paymentMethod', 'creator', 'updater']
    });
    if (!transaction) {
      throw new Error('Financial transaction not found');
    }
    return this.mapToEntity(transaction);
  }

  async findByIdAndInstitution(
    id: string,
    institutionId: string
  ): Promise<FinancialTransactionEntity> {
    const transaction = await this.repository.findOne({
      where: { id, institution_id: institutionId },
      relations: ['institution', 'paymentMethod', 'creator', 'updater']
    });
    if (!transaction) {
      throw new Error('Financial transaction not found');
    }
    return this.mapToEntity(transaction);
  }

  async findByType(type: string): Promise<FinancialTransactionEntity[]> {
    const transactions = await this.repository.find({
      where: { type },
      relations: ['institution', 'paymentMethod', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return transactions.map(this.mapToEntity);
  }

  async findByTypeAndInstitution(
    type: string,
    institutionId: string
  ): Promise<FinancialTransactionEntity[]> {
    const transactions = await this.repository.find({
      where: { type, institution_id: institutionId },
      relations: ['institution', 'paymentMethod', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return transactions.map(this.mapToEntity);
  }

  async findByCategory(
    category: string
  ): Promise<FinancialTransactionEntity[]> {
    const transactions = await this.repository.find({
      where: { category },
      relations: ['institution', 'paymentMethod', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return transactions.map(this.mapToEntity);
  }

  async findByCategoryAndInstitution(
    category: string,
    institutionId: string
  ): Promise<FinancialTransactionEntity[]> {
    const transactions = await this.repository.find({
      where: { category, institution_id: institutionId },
      relations: ['institution', 'paymentMethod', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return transactions.map(this.mapToEntity);
  }

  async findByStatus(status: string): Promise<FinancialTransactionEntity[]> {
    const transactions = await this.repository.find({
      where: { status: status as any },
      relations: ['institution', 'paymentMethod', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return transactions.map(this.mapToEntity);
  }

  async findByStatusAndInstitution(
    status: string,
    institutionId: string
  ): Promise<FinancialTransactionEntity[]> {
    const transactions = await this.repository.find({
      where: { status: status as any, institution_id: institutionId },
      relations: ['institution', 'paymentMethod', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return transactions.map(this.mapToEntity);
  }

  async findByReference(
    referenceId: string,
    referenceType: string
  ): Promise<FinancialTransactionEntity[]> {
    const transactions = await this.repository.find({
      where: { reference_id: referenceId, reference_type: referenceType },
      relations: ['institution', 'paymentMethod', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return transactions.map(this.mapToEntity);
  }

  async findByReferenceAndInstitution(
    referenceId: string,
    referenceType: string,
    institutionId: string
  ): Promise<FinancialTransactionEntity[]> {
    const transactions = await this.repository.find({
      where: {
        reference_id: referenceId,
        reference_type: referenceType,
        institution_id: institutionId
      },
      relations: ['institution', 'paymentMethod', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return transactions.map(this.mapToEntity);
  }

  async findByDateRange(
    startDate: Date,
    endDate: Date
  ): Promise<FinancialTransactionEntity[]> {
    const transactions = await this.repository.find({
      where: { transaction_date: Between(startDate, endDate) },
      relations: ['institution', 'paymentMethod', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return transactions.map(this.mapToEntity);
  }

  async findByDateRangeAndInstitution(
    startDate: Date,
    endDate: Date,
    institutionId: string
  ): Promise<FinancialTransactionEntity[]> {
    const transactions = await this.repository.find({
      where: {
        transaction_date: Between(startDate, endDate),
        institution_id: institutionId
      },
      relations: ['institution', 'paymentMethod', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return transactions.map(this.mapToEntity);
  }

  async findByStripePaymentIntentId(
    stripePaymentIntentId: string
  ): Promise<FinancialTransactionEntity | null> {
    const transaction = await this.repository.findOne({
      where: { stripe_payment_intent_id: stripePaymentIntentId },
      relations: ['institution', 'paymentMethod', 'creator', 'updater']
    });
    return transaction ? this.mapToEntity(transaction) : null;
  }

  async findByStripeSessionId(
    stripeSessionId: string
  ): Promise<FinancialTransactionEntity | null> {
    const transaction = await this.repository.findOne({
      where: { stripe_session_id: stripeSessionId },
      relations: ['institution', 'paymentMethod', 'creator', 'updater']
    });
    return transaction ? this.mapToEntity(transaction) : null;
  }

  async update(
    id: string,
    transactionData: Partial<FinancialTransactionEntity>
  ): Promise<FinancialTransactionEntity> {
    await this.repository.update(id, transactionData as any);
    return this.findById(id);
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  async getFinancialSummary(
    institutionId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalIncome: number;
    totalExpense: number;
    balance: number;
    transactionCount: number;
  }> {
    const queryBuilder = this.repository
      .createQueryBuilder('ft')
      .where('ft.institution_id = :institutionId', { institutionId })
      .andWhere('ft.status = :status', { status: 'completed' });

    if (startDate && endDate) {
      queryBuilder.andWhere(
        'ft.transaction_date BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate
        }
      );
    }

    const transactions = await queryBuilder.getMany();

    const totalIncome = transactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + Number(t.amount), 0);

    const totalExpense = transactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + Number(t.amount), 0);

    return {
      totalIncome,
      totalExpense,
      balance: totalIncome - totalExpense,
      transactionCount: transactions.length
    };
  }

  private mapToEntity(
    transaction: FinancialTransaction
  ): FinancialTransactionEntity {
    return {
      id: transaction.id,
      institution_id: transaction.institution_id,
      type: transaction.type as any,
      category: transaction.category as any,
      amount: Number(transaction.amount),
      description: transaction.description,
      reference_id: transaction.reference_id,
      reference_type: transaction.reference_type,
      payment_method_id: transaction.payment_method_id,
      status: transaction.status as any,
      transaction_date: transaction.transaction_date,
      due_date: transaction.due_date,
      paid_date: transaction.paid_date,
      stripe_payment_intent_id: transaction.stripe_payment_intent_id,
      stripe_session_id: transaction.stripe_session_id,
      payment_type: transaction.payment_type as any,
      currency: transaction.currency,
      metadata: transaction.metadata,
      created_by: transaction.created_by,
      updated_by: transaction.updated_by,
      created_at: transaction.created_at,
      updated_at: transaction.updated_at
    };
  }
}
