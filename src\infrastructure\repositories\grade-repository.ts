import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { GradeEntity } from '../../domain/entities/grade.entity';
import { GradeRepository } from '../../domain/abstractions/grade-repository.abstraction';
import { Grade } from '../entities/grade.entity';

@Injectable()
export class DatabaseGradeRepository implements GradeRepository {
  constructor(
    @InjectRepository(Grade)
    private readonly gradeRepository: Repository<Grade>
  ) {}

  async create(gradeData: Partial<GradeEntity>): Promise<GradeEntity> {
    const gradeEntity = this.gradeRepository.create(gradeData);
    const savedGrade = await this.gradeRepository.save(gradeEntity);
    return this.toModel(savedGrade);
  }

  async findAll(): Promise<GradeEntity[]> {
    const grades = await this.gradeRepository.find({
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { assessment_date: 'DESC', created_at: 'DESC' }
    });
    return grades.map(grade => this.toModel(grade));
  }

  async findById(id: string): Promise<GradeEntity> {
    const grade = await this.gradeRepository.findOne({
      where: { id },
      relations: ['enrollment', 'recorder', 'creator', 'updater']
    });
    if (!grade) {
      throw new Error('Grade not found');
    }
    return this.toModel(grade);
  }

  async findByEnrollmentId(enrollmentId: string): Promise<GradeEntity[]> {
    const grades = await this.gradeRepository.find({
      where: { enrollment_id: enrollmentId },
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { assessment_date: 'DESC' }
    });
    return grades.map(grade => this.toModel(grade));
  }

  async findByAssessmentType(assessmentType: string): Promise<GradeEntity[]> {
    const grades = await this.gradeRepository.find({
      where: { assessment_type: assessmentType },
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { assessment_date: 'DESC' }
    });
    return grades.map(grade => this.toModel(grade));
  }

  async findByEnrollmentAndAssessmentType(
    enrollmentId: string,
    assessmentType: string
  ): Promise<GradeEntity[]> {
    const grades = await this.gradeRepository.find({
      where: {
        enrollment_id: enrollmentId,
        assessment_type: assessmentType
      },
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { assessment_date: 'DESC' }
    });
    return grades.map(grade => this.toModel(grade));
  }

  async findByDateRange(
    startDate: Date,
    endDate: Date
  ): Promise<GradeEntity[]> {
    const grades = await this.gradeRepository.find({
      where: {
        assessment_date: Between(startDate, endDate)
      },
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { assessment_date: 'DESC' }
    });
    return grades.map(grade => this.toModel(grade));
  }

  async findByEnrollmentAndDateRange(
    enrollmentId: string,
    startDate: Date,
    endDate: Date
  ): Promise<GradeEntity[]> {
    const grades = await this.gradeRepository.find({
      where: {
        enrollment_id: enrollmentId,
        assessment_date: Between(startDate, endDate)
      },
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { assessment_date: 'DESC' }
    });
    return grades.map(grade => this.toModel(grade));
  }

  async update(
    id: string,
    gradeData: Partial<GradeEntity>
  ): Promise<GradeEntity> {
    await this.gradeRepository.update(id, gradeData);
    const updatedGrade = await this.findById(id);
    return updatedGrade;
  }

  async delete(id: string): Promise<void> {
    const result = await this.gradeRepository.delete(id);
    if (result.affected === 0) {
      throw new Error('Grade not found');
    }
  }

  async calculateWeightedAverage(enrollmentId: string): Promise<{
    weightedAverage: number;
    totalWeight: number;
    gradeCount: number;
  }> {
    const grades = await this.gradeRepository.find({
      where: { enrollment_id: enrollmentId }
    });

    if (grades.length === 0) {
      return {
        weightedAverage: 0,
        totalWeight: 0,
        gradeCount: 0
      };
    }

    let totalWeightedScore = 0;
    let totalWeight = 0;

    grades.forEach(grade => {
      const normalizedGrade =
        (Number(grade.grade) / Number(grade.max_grade)) * 100;
      totalWeightedScore += normalizedGrade * Number(grade.weight);
      totalWeight += Number(grade.weight);
    });

    const weightedAverage =
      totalWeight > 0 ? totalWeightedScore / totalWeight : 0;

    return {
      weightedAverage: Math.round(weightedAverage * 100) / 100,
      totalWeight,
      gradeCount: grades.length
    };
  }

  async getGradesByAssessmentType(enrollmentId: string): Promise<
    {
      assessmentType: string;
      grades: GradeEntity[];
      average: number;
      totalWeight: number;
    }[]
  > {
    const grades = await this.gradeRepository.find({
      where: { enrollment_id: enrollmentId },
      relations: ['enrollment', 'recorder', 'creator', 'updater'],
      order: { assessment_date: 'DESC' }
    });

    const gradesByType = new Map<string, GradeEntity[]>();

    grades.forEach(grade => {
      const gradeEntity = this.toModel(grade);
      if (!gradesByType.has(grade.assessment_type)) {
        gradesByType.set(grade.assessment_type, []);
      }
      gradesByType.get(grade.assessment_type)!.push(gradeEntity);
    });

    const result: {
      assessmentType: string;
      grades: GradeEntity[];
      average: number;
      totalWeight: number;
    }[] = [];

    gradesByType.forEach((typeGrades, assessmentType) => {
      let totalWeightedScore = 0;
      let totalWeight = 0;

      typeGrades.forEach(grade => {
        const normalizedGrade = (grade.grade / grade.max_grade) * 100;
        totalWeightedScore += normalizedGrade * grade.weight;
        totalWeight += grade.weight;
      });

      const average = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;

      result.push({
        assessmentType,
        grades: typeGrades,
        average: Math.round(average * 100) / 100,
        totalWeight
      });
    });

    return result.sort((a, b) =>
      a.assessmentType.localeCompare(b.assessmentType)
    );
  }

  private toModel(grade: Grade): GradeEntity {
    return {
      id: grade.id,
      enrollment_id: grade.enrollment_id,
      assessment_type: grade.assessment_type,
      assessment_name: grade.assessment_name,
      grade: Number(grade.grade),
      max_grade: Number(grade.max_grade),
      weight: Number(grade.weight),
      assessment_date: grade.assessment_date,
      recorded_by: grade.recorded_by,
      created_by: grade.created_by,
      updated_by: grade.updated_by,
      created_at: grade.created_at,
      updated_at: grade.updated_at
    };
  }
}
