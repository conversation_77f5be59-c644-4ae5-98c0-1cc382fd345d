import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IInstitutionRepository } from '../../domain/abstractions/institution-repository.abstraction';
import { InstitutionEntity } from '../../domain/entities/institution.entity';
import { Institution } from '../entities/institution.entity';

@Injectable()
export class InstitutionRepository implements IInstitutionRepository {
  constructor(
    @InjectRepository(Institution)
    private readonly institutionTypeOrmRepository: Repository<Institution>
  ) {}

  async findByTaxId(tax_id: string): Promise<InstitutionEntity | null> {
    const institution = await this.institutionTypeOrmRepository.findOne({
      where: { tax_id }
    });
    return institution;
  }

  async insert(
    institution: Partial<InstitutionEntity>
  ): Promise<InstitutionEntity> {
    const newInstitution =
      await this.institutionTypeOrmRepository.save(institution);
    return newInstitution;
  }

  async findAll(): Promise<InstitutionEntity[]> {
    const institutions = await this.institutionTypeOrmRepository.find();
    return institutions;
  }

  async findById(id: string): Promise<InstitutionEntity | null> {
    const institution = await this.institutionTypeOrmRepository.findOne({
      where: { id }
    });
    return institution;
  }

  async update(
    id: string,
    institution: InstitutionEntity
  ): Promise<InstitutionEntity> {
    const updatedInstitution = await this.institutionTypeOrmRepository.save({
      ...institution,
      id
    });
    return updatedInstitution;
  }

  async delete(id: string): Promise<void> {
    await this.institutionTypeOrmRepository.delete(id);
  }
}
