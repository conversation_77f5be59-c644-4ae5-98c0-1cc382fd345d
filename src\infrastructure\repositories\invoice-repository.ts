import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThan } from 'typeorm';
import { InvoiceRepository } from '../../domain/abstractions/financial-repository.abstraction';
import { InvoiceEntity } from '../../domain/entities/payment.entity';
import { Invoice } from '../entities/invoice.entity';

@Injectable()
export class DatabaseInvoiceRepository implements InvoiceRepository {
  constructor(
    @InjectRepository(Invoice)
    private readonly repository: Repository<Invoice>
  ) {}

  async create(invoiceData: Partial<InvoiceEntity>): Promise<InvoiceEntity> {
    const invoice = this.repository.create(invoiceData);
    const savedInvoice = await this.repository.save(invoice);
    return this.mapToEntity(savedInvoice);
  }

  async findAll(): Promise<InvoiceEntity[]> {
    const invoices = await this.repository.find({
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ],
      order: { created_at: 'DESC' }
    });
    return invoices.map(this.mapToEntity);
  }

  async findAllByInstitution(institutionId: string): Promise<InvoiceEntity[]> {
    const invoices = await this.repository.find({
      where: { institution_id: institutionId },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ],
      order: { created_at: 'DESC' }
    });
    return invoices.map(this.mapToEntity);
  }

  async findById(id: string): Promise<InvoiceEntity> {
    const invoice = await this.repository.findOne({
      where: { id },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ]
    });
    if (!invoice) {
      throw new Error('Invoice not found');
    }
    return this.mapToEntity(invoice);
  }

  async findByIdAndInstitution(
    id: string,
    institutionId: string
  ): Promise<InvoiceEntity> {
    const invoice = await this.repository.findOne({
      where: { id, institution_id: institutionId },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ]
    });
    if (!invoice) {
      throw new Error('Invoice not found');
    }
    return this.mapToEntity(invoice);
  }

  async findByEnrollmentId(enrollmentId: string): Promise<InvoiceEntity[]> {
    const invoices = await this.repository.find({
      where: { enrollment_id: enrollmentId },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ],
      order: { created_at: 'DESC' }
    });
    return invoices.map(this.mapToEntity);
  }

  async findByEnrollmentIdAndInstitution(
    enrollmentId: string,
    institutionId: string
  ): Promise<InvoiceEntity[]> {
    const invoices = await this.repository.find({
      where: { enrollment_id: enrollmentId, institution_id: institutionId },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ],
      order: { created_at: 'DESC' }
    });
    return invoices.map(this.mapToEntity);
  }

  async findByInvoiceNumber(
    invoiceNumber: string
  ): Promise<InvoiceEntity | null> {
    const invoice = await this.repository.findOne({
      where: { invoice_number: invoiceNumber },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ]
    });
    return invoice ? this.mapToEntity(invoice) : null;
  }

  async findByStatus(status: string): Promise<InvoiceEntity[]> {
    const invoices = await this.repository.find({
      where: { status: status as any },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ],
      order: { created_at: 'DESC' }
    });
    return invoices.map(this.mapToEntity);
  }

  async findByStatusAndInstitution(
    status: string,
    institutionId: string
  ): Promise<InvoiceEntity[]> {
    const invoices = await this.repository.find({
      where: { status: status as any, institution_id: institutionId },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ],
      order: { created_at: 'DESC' }
    });
    return invoices.map(this.mapToEntity);
  }

  async findOverdueInvoices(): Promise<InvoiceEntity[]> {
    const today = new Date();
    const invoices = await this.repository.find({
      where: {
        due_date: LessThan(today),
        status: 'pending' as any
      },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ],
      order: { due_date: 'ASC' }
    });
    return invoices.map(this.mapToEntity);
  }

  async findOverdueInvoicesByInstitution(
    institutionId: string
  ): Promise<InvoiceEntity[]> {
    const today = new Date();
    const invoices = await this.repository.find({
      where: {
        due_date: LessThan(today),
        status: 'pending' as any,
        institution_id: institutionId
      },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ],
      order: { due_date: 'ASC' }
    });
    return invoices.map(this.mapToEntity);
  }

  async findByDueDateRange(
    startDate: Date,
    endDate: Date
  ): Promise<InvoiceEntity[]> {
    const invoices = await this.repository.find({
      where: { due_date: Between(startDate, endDate) },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ],
      order: { due_date: 'ASC' }
    });
    return invoices.map(this.mapToEntity);
  }

  async findByDueDateRangeAndInstitution(
    startDate: Date,
    endDate: Date,
    institutionId: string
  ): Promise<InvoiceEntity[]> {
    const invoices = await this.repository.find({
      where: {
        due_date: Between(startDate, endDate),
        institution_id: institutionId
      },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ],
      order: { due_date: 'ASC' }
    });
    return invoices.map(this.mapToEntity);
  }

  async findByStripeInvoiceId(
    stripeInvoiceId: string
  ): Promise<InvoiceEntity | null> {
    const invoice = await this.repository.findOne({
      where: { stripe_invoice_id: stripeInvoiceId },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ]
    });
    return invoice ? this.mapToEntity(invoice) : null;
  }

  async findByStripePaymentIntentId(
    stripePaymentIntentId: string
  ): Promise<InvoiceEntity | null> {
    const invoice = await this.repository.findOne({
      where: { stripe_payment_intent_id: stripePaymentIntentId },
      relations: [
        'enrollment',
        'institution',
        'paymentMethod',
        'creator',
        'updater'
      ]
    });
    return invoice ? this.mapToEntity(invoice) : null;
  }

  async update(
    id: string,
    invoiceData: Partial<InvoiceEntity>
  ): Promise<InvoiceEntity> {
    await this.repository.update(id, invoiceData);
    return this.findById(id);
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  async generateInvoiceNumber(institutionId: string): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');

    // Buscar o último número da fatura para esta instituição no mês atual
    const lastInvoice = await this.repository
      .createQueryBuilder('invoice')
      .where('invoice.institution_id = :institutionId', { institutionId })
      .andWhere('invoice.invoice_number LIKE :pattern', {
        pattern: `${year}${month}%`
      })
      .orderBy('invoice.invoice_number', 'DESC')
      .getOne();

    let nextNumber = 1;
    if (lastInvoice) {
      const lastNumber = parseInt(lastInvoice.invoice_number.slice(-4));
      nextNumber = lastNumber + 1;
    }

    return `${year}${month}${String(nextNumber).padStart(4, '0')}`;
  }

  async markAsPaid(
    id: string,
    paymentDate: Date,
    paymentMethodId?: string
  ): Promise<InvoiceEntity> {
    await this.repository.update(id, {
      status: 'paid' as any,
      payment_date: paymentDate,
      payment_method_id: paymentMethodId,
      updated_at: new Date()
    });
    return this.findById(id);
  }

  async markAsOverdue(id: string): Promise<InvoiceEntity> {
    await this.repository.update(id, {
      status: 'overdue' as any,
      updated_at: new Date()
    });
    return this.findById(id);
  }

  private mapToEntity(invoice: Invoice): InvoiceEntity {
    return {
      id: invoice.id,
      enrollment_id: invoice.enrollment_id,
      institution_id: invoice.institution_id,
      invoice_number: invoice.invoice_number,
      amount: Number(invoice.amount),
      due_date: invoice.due_date,
      status: invoice.status as any,
      payment_date: invoice.payment_date,
      payment_method_id: invoice.payment_method_id,
      notes: invoice.notes,
      stripe_invoice_id: invoice.stripe_invoice_id,
      stripe_payment_intent_id: invoice.stripe_payment_intent_id,
      currency: invoice.currency,
      metadata: invoice.metadata,
      created_by: invoice.created_by,
      updated_by: invoice.updated_by,
      created_at: invoice.created_at,
      updated_at: invoice.updated_at
    };
  }
}
