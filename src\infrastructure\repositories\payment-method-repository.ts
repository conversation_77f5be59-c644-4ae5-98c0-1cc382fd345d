import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaymentMethodRepository } from '../../domain/abstractions/financial-repository.abstraction';
import { PaymentMethodEntity } from '../../domain/entities/payment.entity';
import { PaymentMethod } from '../entities/payment-method.entity';

@Injectable()
export class DatabasePaymentMethodRepository implements PaymentMethodRepository {
  constructor(
    @InjectRepository(PaymentMethod)
    private readonly repository: Repository<PaymentMethod>
  ) {}

  async create(paymentMethodData: Partial<PaymentMethodEntity>): Promise<PaymentMethodEntity> {
    const paymentMethod = this.repository.create(paymentMethodData);
    const savedPaymentMethod = await this.repository.save(paymentMethod);
    return this.mapToEntity(savedPaymentMethod);
  }

  async findAll(): Promise<PaymentMethodEntity[]> {
    const paymentMethods = await this.repository.find({
      relations: ['institution', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return paymentMethods.map(this.mapToEntity);
  }

  async findAllByInstitution(institutionId: string): Promise<PaymentMethodEntity[]> {
    const paymentMethods = await this.repository.find({
      where: { institution_id: institutionId },
      relations: ['institution', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return paymentMethods.map(this.mapToEntity);
  }

  async findById(id: string): Promise<PaymentMethodEntity> {
    const paymentMethod = await this.repository.findOne({
      where: { id },
      relations: ['institution', 'creator', 'updater']
    });
    if (!paymentMethod) {
      throw new Error('Payment method not found');
    }
    return this.mapToEntity(paymentMethod);
  }

  async findByIdAndInstitution(id: string, institutionId: string): Promise<PaymentMethodEntity> {
    const paymentMethod = await this.repository.findOne({
      where: { id, institution_id: institutionId },
      relations: ['institution', 'creator', 'updater']
    });
    if (!paymentMethod) {
      throw new Error('Payment method not found');
    }
    return this.mapToEntity(paymentMethod);
  }

  async findByType(type: string): Promise<PaymentMethodEntity[]> {
    const paymentMethods = await this.repository.find({
      where: { type: type as any },
      relations: ['institution', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return paymentMethods.map(this.mapToEntity);
  }

  async findByTypeAndInstitution(type: string, institutionId: string): Promise<PaymentMethodEntity[]> {
    const paymentMethods = await this.repository.find({
      where: { type: type as any, institution_id: institutionId },
      relations: ['institution', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return paymentMethods.map(this.mapToEntity);
  }

  async findActiveByInstitution(institutionId: string): Promise<PaymentMethodEntity[]> {
    const paymentMethods = await this.repository.find({
      where: { institution_id: institutionId, is_active: true },
      relations: ['institution', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return paymentMethods.map(this.mapToEntity);
  }

  async findByStripePaymentMethodId(stripePaymentMethodId: string): Promise<PaymentMethodEntity | null> {
    const paymentMethod = await this.repository.findOne({
      where: { stripe_payment_method_id: stripePaymentMethodId },
      relations: ['institution', 'creator', 'updater']
    });
    return paymentMethod ? this.mapToEntity(paymentMethod) : null;
  }

  async findByStripeCustomerId(stripeCustomerId: string): Promise<PaymentMethodEntity[]> {
    const paymentMethods = await this.repository.find({
      where: { stripe_customer_id: stripeCustomerId },
      relations: ['institution', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
    return paymentMethods.map(this.mapToEntity);
  }

  async findDefaultByInstitution(institutionId: string): Promise<PaymentMethodEntity | null> {
    const paymentMethod = await this.repository.findOne({
      where: { institution_id: institutionId, is_default: true, is_active: true },
      relations: ['institution', 'creator', 'updater']
    });
    return paymentMethod ? this.mapToEntity(paymentMethod) : null;
  }

  async update(id: string, paymentMethodData: Partial<PaymentMethodEntity>): Promise<PaymentMethodEntity> {
    await this.repository.update(id, paymentMethodData);
    return this.findById(id);
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  async setAsDefault(id: string, institutionId: string): Promise<PaymentMethodEntity> {
    // Primeiro, remover o padrão de todos os outros métodos da instituição
    await this.repository.update(
      { institution_id: institutionId },
      { is_default: false, updated_at: new Date() }
    );

    // Depois, definir este como padrão
    await this.repository.update(id, {
      is_default: true,
      updated_at: new Date()
    });

    return this.findById(id);
  }

  async activate(id: string): Promise<PaymentMethodEntity> {
    await this.repository.update(id, {
      is_active: true,
      updated_at: new Date()
    });
    return this.findById(id);
  }

  async deactivate(id: string): Promise<PaymentMethodEntity> {
    await this.repository.update(id, {
      is_active: false,
      is_default: false, // Se estava como padrão, remover
      updated_at: new Date()
    });
    return this.findById(id);
  }

  private mapToEntity(paymentMethod: PaymentMethod): PaymentMethodEntity {
    return {
      id: paymentMethod.id,
      institution_id: paymentMethod.institution_id,
      name: paymentMethod.name,
      type: paymentMethod.type as any,
      is_active: paymentMethod.is_active,
      stripe_payment_method_id: paymentMethod.stripe_payment_method_id,
      stripe_customer_id: paymentMethod.stripe_customer_id,
      card_brand: paymentMethod.card_brand,
      card_last4: paymentMethod.card_last4,
      card_exp_month: paymentMethod.card_exp_month,
      card_exp_year: paymentMethod.card_exp_year,
      is_default: paymentMethod.is_default,
      created_by: paymentMethod.created_by,
      updated_by: paymentMethod.updated_by,
      created_at: paymentMethod.created_at,
      updated_at: paymentMethod.updated_at
    };
  }
}
