import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IPermissionRepository } from '../../domain/abstractions/permission-repository.abstraction';
import { PermissionEntity } from '../../domain/entities/permission.entity';
import { Permission } from '../entities/permission.entity';

@Injectable()
export class PermissionRepository implements IPermissionRepository {
  constructor(
    @InjectRepository(Permission)
    private readonly permissionTypeOrmRepository: Repository<Permission>
  ) {}
  async findByKey(key: string): Promise<PermissionEntity | null> {
    const permission = await this.permissionTypeOrmRepository.findOne({
      where: { key }
    });
    return permission;
  }

  async insert(
    permission: Partial<PermissionEntity>
  ): Promise<PermissionEntity> {
    const newPermission =
      await this.permissionTypeOrmRepository.save(permission);
    return newPermission;
  }

  async findAll(): Promise<PermissionEntity[]> {
    const permissions = await this.permissionTypeOrmRepository.find();
    return permissions;
  }

  async findById(id: string): Promise<PermissionEntity | null> {
    const permission = await this.permissionTypeOrmRepository.findOne({
      where: { id }
    });
    return permission;
  }

  async update(
    id: string,
    permission: PermissionEntity
  ): Promise<PermissionEntity> {
    const updatedPermission = await this.permissionTypeOrmRepository.save({
      ...permission,
      id
    });
    return updatedPermission;
  }

  async delete(id: string): Promise<void> {
    await this.permissionTypeOrmRepository.delete(id);
  }
}
