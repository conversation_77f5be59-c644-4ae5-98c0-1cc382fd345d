import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IPlanRepository } from '../../domain/abstractions/plan-repository.abstraction';
import { PlanEntity } from '../../domain/entities/plan.entity';
import { Plan } from '../entities/plan.entity';

@Injectable()
export class PlanRepository implements IPlanRepository {
  constructor(
    @InjectRepository(Plan)
    private readonly planTypeOrmRepository: Repository<Plan>
  ) {}

  async insert(plan: Partial<PlanEntity>): Promise<PlanEntity> {
    const newPlan = await this.planTypeOrmRepository.save(plan);
    return newPlan;
  }

  async findAll(): Promise<PlanEntity[]> {
    const plans = await this.planTypeOrmRepository.find();
    return plans;
  }

  async findById(id: string): Promise<PlanEntity | null> {
    const plan = await this.planTypeOrmRepository.findOne({
      where: { id }
    });
    return plan;
  }

  async findByName(name: string): Promise<PlanEntity | null> {
    const plan = await this.planTypeOrmRepository.findOne({
      where: { name }
    });
    return plan;
  }

  async findActive(): Promise<PlanEntity[]> {
    const plans = await this.planTypeOrmRepository.find({
      where: { is_active: true }
    });
    return plans;
  }

  async findByBillingPeriod(billingPeriod: string): Promise<PlanEntity[]> {
    const plans = await this.planTypeOrmRepository.find({
      where: { billing_period: billingPeriod, is_active: true }
    });
    return plans;
  }

  async update(id: string, plan: PlanEntity): Promise<PlanEntity> {
    const updatedPlan = await this.planTypeOrmRepository.save({
      ...plan,
      id
    });
    return updatedPlan;
  }

  async delete(id: string): Promise<void> {
    await this.planTypeOrmRepository.delete(id);
  }
}
