import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClassRepository } from '../../domain/abstractions/class-repository.abstraction';
import { CourseRepository } from '../../domain/abstractions/course-repository.abstraction';
import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';
import { TypeOrmConfigModule } from '../config/typeorm/typeorm.module';
import { Address } from '../entities/address.entity';
import { Class } from '../entities/class.entity';
import { Course } from '../entities/course.entity';
import { Enrollment } from '../entities/enrollment.entity';
import { Institution } from '../entities/institution.entity';
import { Permission } from '../entities/permission.entity';
import { Plan } from '../entities/plan.entity';
import { Role } from '../entities/role.entity';
import { RolePermission } from '../entities/role_permission.entity';
import { Subscription } from '../entities/subscription.entity';
import { User } from '../entities/user.entity';
import { UserRole } from '../entities/user_role.entity';
import { AddressRepository } from './address-repository';
import { DatabaseClassRepository } from './class-repository';
import { DatabaseCourseRepository } from './course-repository';
import { DatabaseEnrollmentRepository } from './enrollment-repository';
import { InstitutionRepository } from './institution-repository';
import { PermissionRepository } from './permission-repository';
import { PlanRepository } from './plan-repository';
import { RoleRepository } from './role-repository';
import { RolePermissionRepository } from './role_permission-repository';
import { SubscriptionRepository } from './subscription-repository';
import { UserRepository } from './user-repository';
import { UserRoleRepository } from './user_role-repository';

@Module({
  imports: [
    TypeOrmConfigModule,
    TypeOrmModule.forFeature([
      User,
      Address,
      Class,
      Course,
      Enrollment,
      Institution,
      Permission,
      RolePermission,
      UserRole,
      Role,
      Plan,
      Subscription
    ])
  ],
  providers: [
    UserRepository,
    AddressRepository,
    {
      provide: ClassRepository,
      useClass: DatabaseClassRepository
    },
    {
      provide: CourseRepository,
      useClass: DatabaseCourseRepository
    },
    {
      provide: EnrollmentRepository,
      useClass: DatabaseEnrollmentRepository
    },
    InstitutionRepository,
    PermissionRepository,
    RolePermissionRepository,
    UserRoleRepository,
    RoleRepository,
    PlanRepository,
    SubscriptionRepository
  ],
  exports: [
    UserRepository,
    AddressRepository,
    ClassRepository,
    CourseRepository,
    EnrollmentRepository,
    InstitutionRepository,
    PermissionRepository,
    RolePermissionRepository,
    UserRoleRepository,
    RoleRepository,
    PlanRepository,
    SubscriptionRepository
  ]
})
export class RepositoriesModule {}
