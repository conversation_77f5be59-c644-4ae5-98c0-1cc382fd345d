import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TypeOrmConfigModule } from '../config/typeorm/typeorm.module';
import { Address } from '../entities/address.entity';
import { Institution } from '../entities/institution.entity';
import { Permission } from '../entities/permission.entity';
import { Role } from '../entities/role.entity';
import { RolePermission } from '../entities/role_permission.entity';
import { User } from '../entities/user.entity';
import { UserRole } from '../entities/user_role.entity';
import { AddressRepository } from './address-repository';
import { InstitutionRepository } from './institution-repository';
import { PermissionRepository } from './permission-repository';
import { RoleRepository } from './role-repository';
import { RolePermissionRepository } from './role_permission-repository';
import { UserRepository } from './user-repository';
import { UserRoleRepository } from './user_role-repository';

@Module({
  imports: [
    TypeOrmConfigModule,
    TypeOrmModule.forFeature([
      User,
      Address,
      Institution,
      Permission,
      RolePermission,
      UserRole,
      Role
    ])
  ],
  providers: [
    UserRepository,
    AddressRepository,
    InstitutionRepository,
    PermissionRepository,
    RolePermissionRepository,
    UserRoleRepository,
    RoleRepository
  ],
  exports: [
    UserRepository,
    AddressRepository,
    InstitutionRepository,
    PermissionRepository,
    RolePermissionRepository,
    UserRoleRepository,
    RoleRepository
  ]
})
export class RepositoriesModule {}
