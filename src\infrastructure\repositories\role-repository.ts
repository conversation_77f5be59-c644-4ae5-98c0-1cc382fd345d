import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IRoleRepository } from '../../domain/abstractions/role-repository.abstraction';
import { RoleEntity } from '../../domain/entities/role.entity';
import { Role } from '../entities/role.entity';

@Injectable()
export class RoleRepository implements IRoleRepository {
  constructor(
    @InjectRepository(Role)
    private readonly roleTypeOrmRepository: Repository<Role>
  ) {}
  async findByKey(key: string): Promise<RoleEntity | null> {
    const role = await this.roleTypeOrmRepository.findOne({
      where: { key }
    });
    return role;
  }

  async insert(role: Partial<RoleEntity>): Promise<RoleEntity> {
    const newRole = await this.roleTypeOrmRepository.save(role);
    return newRole;
  }

  async findAll(): Promise<RoleEntity[]> {
    const roles = await this.roleTypeOrmRepository.find();
    return roles;
  }

  async findById(id: string): Promise<RoleEntity | null> {
    const role = await this.roleTypeOrmRepository.findOne({
      where: { id }
    });
    return role;
  }

  async update(id: string, role: RoleEntity): Promise<RoleEntity> {
    const updatedRole = await this.roleTypeOrmRepository.save({
      ...role,
      id
    });
    return updatedRole;
  }

  async delete(id: string): Promise<void> {
    await this.roleTypeOrmRepository.delete(id);
  }
}
