import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IRolePermissionRepository } from '../../domain/abstractions/role_permission-repository.abstraction';
import { RolePermissionEntity } from '../../domain/entities/role_permissions.entity';
import { RolePermission } from '../entities/role_permission.entity';

@Injectable()
export class RolePermissionRepository implements IRolePermissionRepository {
  constructor(
    @InjectRepository(RolePermission)
    private readonly rolePermissionTypeOrmRepository: Repository<RolePermission>
  ) {}

  async insert(
    rolePermission: Partial<RolePermissionEntity>
  ): Promise<RolePermissionEntity> {
    const newRolePermission =
      await this.rolePermissionTypeOrmRepository.save(rolePermission);
    return newRolePermission;
  }

  async findAll(): Promise<RolePermissionEntity[]> {
    const rolePermissions = await this.rolePermissionTypeOrmRepository.find();
    return rolePermissions;
  }

  async findById(id: string): Promise<RolePermissionEntity | null> {
    const rolePermission = await this.rolePermissionTypeOrmRepository.findOne({
      where: { id }
    });
    return rolePermission;
  }

  async update(
    id: string,
    rolePermission: RolePermissionEntity
  ): Promise<RolePermissionEntity> {
    const updatedRolePermission =
      await this.rolePermissionTypeOrmRepository.save({
        ...rolePermission,
        id
      });
    return updatedRolePermission;
  }

  async delete(id: string): Promise<void> {
    await this.rolePermissionTypeOrmRepository.delete(id);
  }
}
