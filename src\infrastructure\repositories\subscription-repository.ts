import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ISubscriptionRepository } from '../../domain/abstractions/subscription-repository.abstraction';
import { SubscriptionEntity } from '../../domain/entities/subscription.entity';
import { Subscription } from '../entities/subscription.entity';

@Injectable()
export class SubscriptionRepository implements ISubscriptionRepository {
  constructor(
    @InjectRepository(Subscription)
    private readonly subscriptionTypeOrmRepository: Repository<Subscription>
  ) {}

  async insert(subscription: Partial<SubscriptionEntity>): Promise<SubscriptionEntity> {
    const newSubscription = await this.subscriptionTypeOrmRepository.save(subscription);
    return newSubscription;
  }

  async findAll(): Promise<SubscriptionEntity[]> {
    const subscriptions = await this.subscriptionTypeOrmRepository.find({
      relations: ['institution', 'plan']
    });
    return subscriptions;
  }

  async findById(id: string): Promise<SubscriptionEntity | null> {
    const subscription = await this.subscriptionTypeOrmRepository.findOne({
      where: { id },
      relations: ['institution', 'plan']
    });
    return subscription;
  }

  async findByInstitutionId(institutionId: string): Promise<SubscriptionEntity[]> {
    const subscriptions = await this.subscriptionTypeOrmRepository.find({
      where: { institution_id: institutionId },
      relations: ['plan'],
      order: { created_at: 'DESC' }
    });
    return subscriptions;
  }

  async findActiveByInstitutionId(institutionId: string): Promise<SubscriptionEntity | null> {
    const subscription = await this.subscriptionTypeOrmRepository.findOne({
      where: { 
        institution_id: institutionId,
        status: 'active'
      },
      relations: ['plan']
    });
    return subscription;
  }

  async findByPlanId(planId: string): Promise<SubscriptionEntity[]> {
    const subscriptions = await this.subscriptionTypeOrmRepository.find({
      where: { plan_id: planId },
      relations: ['institution']
    });
    return subscriptions;
  }

  async findByStatus(status: string): Promise<SubscriptionEntity[]> {
    const subscriptions = await this.subscriptionTypeOrmRepository.find({
      where: { status },
      relations: ['institution', 'plan']
    });
    return subscriptions;
  }

  async findByStripeSubscriptionId(stripeSubscriptionId: string): Promise<SubscriptionEntity | null> {
    const subscription = await this.subscriptionTypeOrmRepository.findOne({
      where: { stripe_subscription_id: stripeSubscriptionId },
      relations: ['institution', 'plan']
    });
    return subscription;
  }

  async update(id: string, subscription: SubscriptionEntity): Promise<SubscriptionEntity> {
    const updatedSubscription = await this.subscriptionTypeOrmRepository.save({
      ...subscription,
      id
    });
    return updatedSubscription;
  }

  async delete(id: string): Promise<void> {
    await this.subscriptionTypeOrmRepository.delete(id);
  }
}
