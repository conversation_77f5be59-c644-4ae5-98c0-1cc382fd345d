import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IUserRepository } from '../../domain/abstractions/user-repository.abstraction';
import { UserEntity } from '../../domain/entities/user.entity';
import { User } from '../entities/user.entity';

@Injectable()
export class UserRepository implements IUserRepository {
  constructor(
    @InjectRepository(User)
    private readonly userTypeOrmRepository: Repository<User>
  ) {}
  async findByTaxId(tax_id: string): Promise<UserEntity | null> {
    const user = await this.userTypeOrmRepository.findOne({
      where: { tax_id }
    });
    return user;
  }

  async insert(user: Partial<UserEntity>): Promise<UserEntity> {
    const newUser = await this.userTypeOrmRepository.save(user);
    return newUser;
  }

  async findAll(): Promise<UserEntity[]> {
    const users = await this.userTypeOrmRepository.find();
    return users;
  }

  async findById(id: string): Promise<UserEntity | null> {
    const user = await this.userTypeOrmRepository.findOne({ where: { id } });
    return user;
  }

  async findByEmail(email: string): Promise<UserEntity | null> {
    const user = await this.userTypeOrmRepository.findOne({ where: { email } });
    return user;
  }

  async update(id: string, user: UserEntity): Promise<UserEntity> {
    const updatedUser = await this.userTypeOrmRepository.save({ ...user, id });
    return updatedUser;
  }

  async delete(id: string): Promise<void> {
    await this.userTypeOrmRepository.delete(id);
  }

  async query(query: string, parameters?: any[]): Promise<any> {
    return await this.userTypeOrmRepository.query(query, parameters);
  }
}
