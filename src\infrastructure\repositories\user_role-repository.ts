import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IUserRoleRepository } from '../../domain/abstractions/user_role-repository.abstraction';
import { UserRoleEntity } from '../../domain/entities/user_role.entity';
import { UserRole } from '../entities/user_role.entity';

@Injectable()
export class UserRoleRepository implements IUserRoleRepository {
  constructor(
    @InjectRepository(UserRole)
    private readonly userRoleTypeOrmRepository: Repository<UserRole>
  ) {}

  async insert(userRole: Partial<UserRoleEntity>): Promise<UserRoleEntity> {
    const newUserRole = await this.userRoleTypeOrmRepository.save(userRole);
    return newUserRole;
  }

  async findAll(): Promise<UserRoleEntity[]> {
    const userRoles = await this.userRoleTypeOrmRepository.find();
    return userRoles;
  }

  async findById(id: string): Promise<UserRoleEntity | null> {
    const userRole = await this.userRoleTypeOrmRepository.findOne({
      where: { id }
    });
    return userRole;
  }

  async update(id: string, userRole: UserRoleEntity): Promise<UserRoleEntity> {
    const updatedUserRole = await this.userRoleTypeOrmRepository.save({
      ...userRole,
      id
    });
    return updatedUserRole;
  }

  async delete(id: string): Promise<void> {
    await this.userRoleTypeOrmRepository.delete(id);
  }
}
