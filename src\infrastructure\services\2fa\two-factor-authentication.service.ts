import { Injectable } from '@nestjs/common';
import * as speakeasy from 'speakeasy';

@Injectable()
export class TwoFactorAuthenticationService {
  generateSecret(length = 20): string {
    const secret = speakeasy.generateSecret({ length });
    return secret.base32;
  }

  verifyToken(secret: string, token: string) {
    return {
      token,
      secret,
      isValid: speakeasy.totp.verify({
        secret: secret,
        encoding: 'base32',
        token: token
      })
    };
  }

  generateGoogleAuthenticatorURL(secret: string, email: string): string {
    return `otpauth://totp/EduSys:${email}?secret=${secret}&issuer=EduSys`;
  }
}
