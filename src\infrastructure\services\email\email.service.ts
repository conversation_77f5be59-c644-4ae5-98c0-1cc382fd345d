import { Injectable } from '@nestjs/common';
import fs from 'fs/promises';
import handlebars from 'handlebars';
import juice from 'juice';
import nodemailer from 'nodemailer';
import { LoggerService } from '../../../infrastructure/logger/logger.service';

@Injectable()
export class EmailService {
  constructor(private readonly logger: LoggerService) {}

  async sendEmail<T>(
    to: string,
    subject: string,
    templateName: string,
    data: T
  ): Promise<void> {
    const templateHtml = await fs.readFile(
      'templates/' + templateName + '.hbs',
      'utf-8'
    );
    const template = handlebars.compile(templateHtml);
    const html = template(data);

    const htmlWithStyles = juice(html);

    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD
      }
    });

    const info = await transporter.sendMail({
      from: process.env.SMTP_FROM,
      to,
      subject,
      html: htmlWithStyles
    });

    this.logger.log('EmailService', `Email sent: ${info.response}`);
  }
}
