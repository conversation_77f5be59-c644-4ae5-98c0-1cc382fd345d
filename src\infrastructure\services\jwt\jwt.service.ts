import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import {
  IJwtService,
  IJwtServiceFirstAccessTokenPayload,
  IJwtServicePayload,
  IJwtServiceRefreshPayload
} from '../../../domain/types/jwt/jwt';

@Injectable()
export class JwtTokenService implements IJwtService {
  constructor(private readonly jwtService: JwtService) {}

  createFirstAccessToken(payload: IJwtServiceFirstAccessTokenPayload): string {
    return this.jwtService.sign(payload, {
      secret: process.env.JWT_SECRET,
      // Token expira em 30 minutos
      expiresIn: 1800
    });
  }

  async checkToken(token: string): Promise<any> {
    try {
      const decode = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET
      });
      return decode;
    } catch (error) {
      // Se falhar com JWT_SECRET, tentar com JWT_REFRESH_SECRET
      const decode = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_REFRESH_SECRET || 'refresh-secret'
      });
      return decode;
    }
  }

  createToken(
    payload: IJwtServicePayload | IJwtServiceRefreshPayload,
    secret: string,
    expiresIn: string
  ): string {
    return this.jwtService.sign(payload, {
      secret: secret,
      expiresIn: expiresIn
    });
  }
}
