import { Injectable } from '@nestjs/common';
import { LoggerService } from '../../logger/logger.service';
import { UserRepository } from '../../repositories/user-repository';

@Injectable()
export class PermissionService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly logger: LoggerService
  ) {}

  private readonly logContextName: string = 'PERMISSION_SERVICE';

  async getUserPermissions(userId: string): Promise<string[]> {
    this.logger.log(
      this.logContextName,
      `Buscando permissões do usuário: ${userId}`
    );

    try {
      const query = `
        SELECT DISTINCT p.key
        FROM core.permissions p
        JOIN core.role_permissions rp ON p.id = rp.permission_id
        JOIN core.roles r ON rp.role_id = r.id
        JOIN core.user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = $1
      `;

      const result = await this.userRepository.query(query, [userId]);
      const permissions = result.map((row: any) => row.key);

      this.logger.log(
        this.logContextName,
        `Encontradas ${permissions.length} permissões para o usuário ${userId}`
      );

      return permissions;
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro ao buscar permissões do usuário ${userId}`,
        error
      );
      return [];
    }
  }

  async hasPermission(userId: string, permission: string): Promise<boolean> {
    this.logger.log(
      this.logContextName,
      `Verificando permissão '${permission}' para usuário: ${userId}`
    );

    try {
      const permissions = await this.getUserPermissions(userId);
      const hasPermission = permissions.includes(permission);

      this.logger.log(
        this.logContextName,
        `Usuário ${userId} ${hasPermission ? 'TEM' : 'NÃO TEM'} a permissão '${permission}'`
      );

      return hasPermission;
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro ao verificar permissão '${permission}' para usuário ${userId}`,
        error
      );
      return false;
    }
  }

  async getUserRole(userId: string): Promise<string | null> {
    this.logger.log(this.logContextName, `Buscando role do usuário: ${userId}`);

    try {
      const query = `
        SELECT r.key
        FROM core.roles r
        JOIN core.user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = $1
        LIMIT 1
      `;

      const result = await this.userRepository.query(query, [userId]);
      const role = result[0]?.key || null;

      this.logger.log(
        this.logContextName,
        `Role encontrada para usuário ${userId}: ${role || 'nenhuma'}`
      );

      return role;
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro ao buscar role do usuário ${userId}`,
        error
      );
      return null;
    }
  }

  async getUserRoles(userId: string): Promise<string[]> {
    this.logger.log(
      this.logContextName,
      `Buscando todas as roles do usuário: ${userId}`
    );

    try {
      const query = `
        SELECT r.key
        FROM core.roles r
        JOIN core.user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = $1
      `;

      const result = await this.userRepository.query(query, [userId]);
      const roles = result.map((row: any) => row.key);

      this.logger.log(
        this.logContextName,
        `Encontradas ${roles.length} roles para o usuário ${userId}: ${roles.join(', ')}`
      );

      return roles;
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro ao buscar roles do usuário ${userId}`,
        error
      );
      return [];
    }
  }

  async hasRole(userId: string, role: string): Promise<boolean> {
    this.logger.log(
      this.logContextName,
      `Verificando role '${role}' para usuário: ${userId}`
    );

    try {
      const roles = await this.getUserRoles(userId);
      const hasRole = roles.includes(role);

      this.logger.log(
        this.logContextName,
        `Usuário ${userId} ${hasRole ? 'TEM' : 'NÃO TEM'} a role '${role}'`
      );

      return hasRole;
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro ao verificar role '${role}' para usuário ${userId}`,
        error
      );
      return false;
    }
  }

  async hasAnyPermission(
    userId: string,
    permissions: string[]
  ): Promise<boolean> {
    this.logger.log(
      this.logContextName,
      `Verificando se usuário ${userId} tem alguma das permissões: ${permissions.join(', ')}`
    );

    try {
      const userPermissions = await this.getUserPermissions(userId);

      const hasAny = permissions.some(permission =>
        userPermissions.includes(permission)
      );

      this.logger.log(
        this.logContextName,
        `Usuário ${userId} ${hasAny ? 'TEM' : 'NÃO TEM'} alguma das permissões solicitadas`
      );

      return hasAny;
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro ao verificar permissões para usuário ${userId}`,
        error
      );
      return false;
    }
  }

  async hasAllPermissions(
    userId: string,
    permissions: string[]
  ): Promise<boolean> {
    this.logger.log(
      this.logContextName,
      `Verificando se usuário ${userId} tem todas as permissões: ${permissions.join(', ')}`
    );

    try {
      const userPermissions = await this.getUserPermissions(userId);
      const hasAll = permissions.every(permission =>
        userPermissions.includes(permission)
      );

      this.logger.log(
        this.logContextName,
        `Usuário ${userId} ${hasAll ? 'TEM' : 'NÃO TEM'} todas as permissões solicitadas`
      );

      return hasAll;
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro ao verificar permissões para usuário ${userId}`,
        error
      );
      return false;
    }
  }
}
