import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { RedisModule } from '../../config/redis/redis.module';
import { LoggerModule } from '../../logger/logger.module';
import { TokenBlacklistService } from './token-blacklist.service';

@Module({
  imports: [
    RedisModule,
    LoggerModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '15m' }
    })
  ],
  providers: [TokenBlacklistService],
  exports: [TokenBlacklistService]
})
export class TokenBlacklistModule {}
