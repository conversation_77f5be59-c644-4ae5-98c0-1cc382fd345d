import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { LoggerService } from '../../logger/logger.service';
import { RedisConfigService } from '../../config/redis/redis.config';

@Injectable()
export class TokenBlacklistService {
  private readonly logContextName = 'TOKEN_BLACKLIST_SERVICE';

  constructor(
    private readonly redisConfig: RedisConfigService,
    private readonly jwtService: JwtService,
    private readonly logger: LoggerService
  ) {}

  /**
   * Adiciona um token à blacklist
   * @param token - Token JWT a ser invalidado
   * @param expirationTime - Tempo de expiração em segundos (opcional)
   */
  async addToBlacklist(token: string, expirationTime?: number): Promise<void> {
    try {
      const redis = this.redisConfig.getClient();
      const key = `blacklist:${token}`;

      // Se não foi fornecido tempo de expiração, extrair do token
      let ttl = expirationTime;
      if (!ttl) {
        try {
          const decoded = this.jwtService.decode(token) as any;
          if (decoded && decoded.exp) {
            const now = Math.floor(Date.now() / 1000);
            ttl = decoded.exp - now;
            
            // Se o token já expirou, não precisa adicionar à blacklist
            if (ttl <= 0) {
              this.logger.log(
                this.logContextName,
                'Token já expirado, não adicionado à blacklist'
              );
              return;
            }
          }
        } catch (error) {
          this.logger.warn(
            this.logContextName,
            'Erro ao decodificar token para TTL, usando TTL padrão'
          );
          ttl = 7 * 24 * 60 * 60; // 7 dias como padrão
        }
      }

      // Adicionar à blacklist com TTL
      if (ttl && ttl > 0) {
        await redis.setex(key, ttl, 'blacklisted');
      } else {
        await redis.set(key, 'blacklisted');
      }

      this.logger.log(
        this.logContextName,
        `Token adicionado à blacklist com TTL: ${ttl}s`
      );
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro ao adicionar token à blacklist: ${error.message}`
      );
      throw error;
    }
  }

  /**
   * Verifica se um token está na blacklist
   * @param token - Token JWT a ser verificado
   * @returns true se o token está na blacklist, false caso contrário
   */
  async isBlacklisted(token: string): Promise<boolean> {
    try {
      const redis = this.redisConfig.getClient();
      const key = `blacklist:${token}`;
      
      const result = await redis.get(key);
      const isBlacklisted = result === 'blacklisted';

      if (isBlacklisted) {
        this.logger.warn(
          this.logContextName,
          'Token encontrado na blacklist'
        );
      }

      return isBlacklisted;
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro ao verificar blacklist: ${error.message}`
      );
      // Em caso de erro no Redis, permitir o token (fail-safe)
      return false;
    }
  }

  /**
   * Remove um token da blacklist (raramente usado)
   * @param token - Token JWT a ser removido da blacklist
   */
  async removeFromBlacklist(token: string): Promise<void> {
    try {
      const redis = this.redisConfig.getClient();
      const key = `blacklist:${token}`;
      
      await redis.del(key);
      
      this.logger.log(
        this.logContextName,
        'Token removido da blacklist'
      );
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro ao remover token da blacklist: ${error.message}`
      );
      throw error;
    }
  }

  /**
   * Adiciona múltiplos tokens à blacklist (útil para logout de todas as sessões)
   * @param tokens - Array de tokens a serem invalidados
   */
  async addMultipleToBlacklist(tokens: string[]): Promise<void> {
    try {
      const redis = this.redisConfig.getClient();
      const pipeline = redis.pipeline();

      for (const token of tokens) {
        const key = `blacklist:${token}`;
        
        // Tentar extrair TTL do token
        let ttl = 7 * 24 * 60 * 60; // 7 dias como padrão
        try {
          const decoded = this.jwtService.decode(token) as any;
          if (decoded && decoded.exp) {
            const now = Math.floor(Date.now() / 1000);
            const tokenTtl = decoded.exp - now;
            if (tokenTtl > 0) {
              ttl = tokenTtl;
            }
          }
        } catch (error) {
          // Usar TTL padrão se não conseguir decodificar
        }

        pipeline.setex(key, ttl, 'blacklisted');
      }

      await pipeline.exec();
      
      this.logger.log(
        this.logContextName,
        `${tokens.length} tokens adicionados à blacklist`
      );
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro ao adicionar múltiplos tokens à blacklist: ${error.message}`
      );
      throw error;
    }
  }

  /**
   * Limpa tokens expirados da blacklist (manutenção)
   */
  async cleanupExpiredTokens(): Promise<void> {
    try {
      const redis = this.redisConfig.getClient();
      
      // O Redis automaticamente remove chaves com TTL expirado
      // Este método pode ser usado para limpeza manual se necessário
      const keys = await redis.keys('blacklist:*');
      let cleanedCount = 0;

      for (const key of keys) {
        const ttl = await redis.ttl(key);
        if (ttl === -2) { // Chave não existe (já expirou)
          cleanedCount++;
        }
      }

      this.logger.log(
        this.logContextName,
        `Limpeza concluída. ${cleanedCount} tokens expirados removidos automaticamente`
      );
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro na limpeza de tokens expirados: ${error.message}`
      );
    }
  }
}
