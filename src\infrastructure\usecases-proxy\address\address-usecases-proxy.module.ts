import { Modu<PERSON> } from '@nestjs/common';
import { CreateAddressUseCase } from '../../../usecases/address/create-address.usecase';
import { DeleteAddressUseCase } from '../../../usecases/address/delete-address.usecase';
import { GetAddressUseCase } from '../../../usecases/address/get-address.usecase';
import { GetAddressesUseCase } from '../../../usecases/address/get-addresses.usecase';
import { UpdateAddressUseCase } from '../../../usecases/address/update-address.usecase';
import { LoggerService } from '../../logger/logger.service';
import { AddressRepository } from '../../repositories/address-repository';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { TwoFactorAuthenticationService } from '../../services/2fa/two-factor-authentication.service';
import { UseCaseProxy } from '../usecases-proxy';
import { AddressUsecasesProxy } from './address-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule],
  providers: [
    TwoFactorAuthenticationService,
    LoggerService,
    {
      inject: [AddressRepository, LoggerService],
      provide: AddressUsecasesProxy.POST_ADDRESS_USECASE_PROXY,
      useFactory: (
        addressRepository: AddressRepository,
        logger: LoggerService
      ) => new UseCaseProxy(new CreateAddressUseCase(addressRepository, logger))
    },
    {
      inject: [AddressRepository, LoggerService],
      provide: AddressUsecasesProxy.GET_ADDRESSES_USECASE_PROXY,
      useFactory: (
        addressRepository: AddressRepository,
        logger: LoggerService
      ) => new UseCaseProxy(new GetAddressesUseCase(addressRepository, logger))
    },
    {
      inject: [AddressRepository, LoggerService],
      provide: AddressUsecasesProxy.GET_ADDRESS_USECASE_PROXY,
      useFactory: (
        addressRepository: AddressRepository,
        logger: LoggerService
      ) => new UseCaseProxy(new GetAddressUseCase(addressRepository, logger))
    },
    {
      inject: [AddressRepository, LoggerService],
      provide: AddressUsecasesProxy.UPDATE_ADDRESS_USECASE_PROXY,
      useFactory: (
        addressRepository: AddressRepository,
        logger: LoggerService
      ) => new UseCaseProxy(new UpdateAddressUseCase(addressRepository, logger))
    },
    {
      inject: [AddressRepository, LoggerService],
      provide: AddressUsecasesProxy.DELETE_ADDRESS_USECASE_PROXY,
      useFactory: (
        addressRepository: AddressRepository,
        logger: LoggerService
      ) => new UseCaseProxy(new DeleteAddressUseCase(addressRepository, logger))
    }
  ],
  exports: [
    AddressUsecasesProxy.POST_ADDRESS_USECASE_PROXY,
    AddressUsecasesProxy.GET_ADDRESSES_USECASE_PROXY,
    AddressUsecasesProxy.GET_ADDRESS_USECASE_PROXY,
    AddressUsecasesProxy.UPDATE_ADDRESS_USECASE_PROXY,
    AddressUsecasesProxy.DELETE_ADDRESS_USECASE_PROXY
  ]
})
export class AddressUseCasesProxyModule {}
