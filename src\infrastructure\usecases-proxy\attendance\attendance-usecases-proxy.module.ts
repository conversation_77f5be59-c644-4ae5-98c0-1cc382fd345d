import { Module } from '@nestjs/common';
import { AttendanceRepository } from '../../../domain/abstractions/attendance-repository.abstraction';
import { EnrollmentRepository } from '../../../domain/abstractions/enrollment-repository.abstraction';
import { CreateAttendanceUseCase } from '../../../usecases/attendance/create-attendance.usecase';
import { DeleteAttendanceUseCase } from '../../../usecases/attendance/delete-attendance.usecase';
import { GetAttendanceUseCase } from '../../../usecases/attendance/get-attendance.usecase';
import { GetAttendancesByEnrollmentUseCase } from '../../../usecases/attendance/get-attendances-by-enrollment.usecase';
import { GetAttendancesByDateUseCase } from '../../../usecases/attendance/get-attendances-by-date.usecase';
import { GetAttendancesUseCase } from '../../../usecases/attendance/get-attendances.usecase';
import { GetAttendanceStatsUseCase } from '../../../usecases/attendance/get-attendance-stats.usecase';
import { UpdateAttendanceUseCase } from '../../../usecases/attendance/update-attendance.usecase';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { UseCaseProxy } from '../usecases-proxy';
import { AttendanceUsecasesProxy } from './attendance-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule],
  providers: [
    {
      inject: [AttendanceRepository],
      provide: AttendanceUsecasesProxy.GET_ATTENDANCE_USECASE_PROXY,
      useFactory: (attendanceRepository: AttendanceRepository) =>
        new UseCaseProxy(new GetAttendanceUseCase(attendanceRepository))
    },
    {
      inject: [AttendanceRepository],
      provide: AttendanceUsecasesProxy.GET_ATTENDANCES_USECASE_PROXY,
      useFactory: (attendanceRepository: AttendanceRepository) =>
        new UseCaseProxy(new GetAttendancesUseCase(attendanceRepository))
    },
    {
      inject: [AttendanceRepository],
      provide:
        AttendanceUsecasesProxy.GET_ATTENDANCES_BY_ENROLLMENT_USECASE_PROXY,
      useFactory: (attendanceRepository: AttendanceRepository) =>
        new UseCaseProxy(
          new GetAttendancesByEnrollmentUseCase(attendanceRepository)
        )
    },
    {
      inject: [AttendanceRepository],
      provide: AttendanceUsecasesProxy.GET_ATTENDANCES_BY_DATE_USECASE_PROXY,
      useFactory: (attendanceRepository: AttendanceRepository) =>
        new UseCaseProxy(new GetAttendancesByDateUseCase(attendanceRepository))
    },
    {
      inject: [AttendanceRepository],
      provide: AttendanceUsecasesProxy.GET_ATTENDANCE_STATS_USECASE_PROXY,
      useFactory: (attendanceRepository: AttendanceRepository) =>
        new UseCaseProxy(new GetAttendanceStatsUseCase(attendanceRepository))
    },
    {
      inject: [AttendanceRepository, EnrollmentRepository],
      provide: AttendanceUsecasesProxy.POST_ATTENDANCE_USECASE_PROXY,
      useFactory: (
        attendanceRepository: AttendanceRepository,
        enrollmentRepository: EnrollmentRepository
      ) =>
        new UseCaseProxy(
          new CreateAttendanceUseCase(
            attendanceRepository,
            enrollmentRepository
          )
        )
    },
    {
      inject: [AttendanceRepository],
      provide: AttendanceUsecasesProxy.PUT_ATTENDANCE_USECASE_PROXY,
      useFactory: (attendanceRepository: AttendanceRepository) =>
        new UseCaseProxy(new UpdateAttendanceUseCase(attendanceRepository))
    },
    {
      inject: [AttendanceRepository],
      provide: AttendanceUsecasesProxy.DELETE_ATTENDANCE_USECASE_PROXY,
      useFactory: (attendanceRepository: AttendanceRepository) =>
        new UseCaseProxy(new DeleteAttendanceUseCase(attendanceRepository))
    }
  ],
  exports: [
    AttendanceUsecasesProxy.GET_ATTENDANCE_USECASE_PROXY,
    AttendanceUsecasesProxy.GET_ATTENDANCES_USECASE_PROXY,
    AttendanceUsecasesProxy.GET_ATTENDANCES_BY_ENROLLMENT_USECASE_PROXY,
    AttendanceUsecasesProxy.GET_ATTENDANCES_BY_DATE_USECASE_PROXY,
    AttendanceUsecasesProxy.GET_ATTENDANCE_STATS_USECASE_PROXY,
    AttendanceUsecasesProxy.POST_ATTENDANCE_USECASE_PROXY,
    AttendanceUsecasesProxy.PUT_ATTENDANCE_USECASE_PROXY,
    AttendanceUsecasesProxy.DELETE_ATTENDANCE_USECASE_PROXY
  ]
})
export class AttendanceUsecasesProxyModule {}
