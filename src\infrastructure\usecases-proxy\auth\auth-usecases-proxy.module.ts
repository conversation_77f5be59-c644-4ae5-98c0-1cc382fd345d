import { <PERSON>du<PERSON> } from '@nestjs/common';
import { LoginUseCase } from '../../../usecases/auth/login.usecase';
import { LogoutUseCase } from '../../../usecases/auth/logout.usecase';
import { RefreshTokenUseCase } from '../../../usecases/auth/refresh-token.usecase';
import { SetPasswordUseCase } from '../../../usecases/auth/set-password.usecase';
import { LoggerService } from '../../logger/logger.service';
import { UserRepository } from '../../repositories/user-repository';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { BcryptService } from '../../services/bcrypt/bcrypt.service';
import { BcryptModule } from '../../services/bcrypt/bcrypt.module';
import { EmailService } from '../../services/email/email.service';
import { EmailModule } from '../../services/email/email.module';
import { JwtTokenService } from '../../services/jwt/jwt.service';
import { JwtModule } from '../../services/jwt/jwt.module';
import { UseCaseProxy } from '../usecases-proxy';
import { AuthUsecasesProxy } from './auth-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule, JwtModule, BcryptModule, EmailModule],
  providers: [
    LoggerService,
    {
      inject: [UserRepository, BcryptService, JwtTokenService, LoggerService],
      provide: AuthUsecasesProxy.LOGIN_USECASE_PROXY,
      useFactory: (
        userRepository: UserRepository,
        bcryptService: BcryptService,
        jwtTokenService: JwtTokenService,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new LoginUseCase(
            userRepository,
            bcryptService,
            jwtTokenService,
            logger
          )
        )
    },
    {
      inject: [LoggerService],
      provide: AuthUsecasesProxy.LOGOUT_USECASE_PROXY,
      useFactory: (logger: LoggerService) =>
        new UseCaseProxy(new LogoutUseCase(logger))
    },
    {
      inject: [UserRepository, JwtTokenService, LoggerService],
      provide: AuthUsecasesProxy.REFRESH_TOKEN_USECASE_PROXY,
      useFactory: (
        userRepository: UserRepository,
        jwtTokenService: JwtTokenService,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new RefreshTokenUseCase(userRepository, jwtTokenService, logger)
        )
    },
    {
      inject: [UserRepository, BcryptService, JwtTokenService, LoggerService],
      provide: AuthUsecasesProxy.SET_PASSWORD_USECASE_PROXY,
      useFactory: (
        userRepository: UserRepository,
        bcryptService: BcryptService,
        jwtTokenService: JwtTokenService,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new SetPasswordUseCase(
            userRepository,
            bcryptService,
            jwtTokenService,
            logger
          )
        )
    }
  ],
  exports: [
    AuthUsecasesProxy.LOGIN_USECASE_PROXY,
    AuthUsecasesProxy.LOGOUT_USECASE_PROXY,
    AuthUsecasesProxy.REFRESH_TOKEN_USECASE_PROXY,
    AuthUsecasesProxy.SET_PASSWORD_USECASE_PROXY
  ]
})
export class AuthUsecasesProxyModule {}
