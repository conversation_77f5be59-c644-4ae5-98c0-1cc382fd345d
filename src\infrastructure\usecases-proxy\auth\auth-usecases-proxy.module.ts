import { Module } from '@nestjs/common';
import { LoginUseCase } from '../../../usecases/auth/login.usecase';
import { LogoutUseCase } from '../../../usecases/auth/logout.usecase';
import { RefreshTokenUseCase } from '../../../usecases/auth/refresh-token.usecase';
import { SetPasswordUseCase } from '../../../usecases/auth/set-password.usecase';
import { LoggerService } from '../../logger/logger.service';
import { InstitutionRepository } from '../../repositories/institution-repository';
import { PlanRepository } from '../../repositories/plan-repository';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { SubscriptionRepository } from '../../repositories/subscription-repository';
import { UserRepository } from '../../repositories/user-repository';
import { BcryptModule } from '../../services/bcrypt/bcrypt.module';
import { BcryptService } from '../../services/bcrypt/bcrypt.service';
import { EmailModule } from '../../services/email/email.module';
import { JwtModule } from '../../services/jwt/jwt.module';
import { JwtTokenService } from '../../services/jwt/jwt.service';
import { UseCaseProxy } from '../usecases-proxy';
import { AuthUsecasesProxy } from './auth-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule, JwtModule, BcryptModule, EmailModule],
  providers: [
    LoggerService,
    {
      inject: [
        UserRepository,
        InstitutionRepository,
        SubscriptionRepository,
        PlanRepository,
        BcryptService,
        JwtTokenService,
        LoggerService
      ],
      provide: AuthUsecasesProxy.LOGIN_USECASE_PROXY,
      useFactory: (
        userRepository: UserRepository,
        institutionRepository: InstitutionRepository,
        subscriptionRepository: SubscriptionRepository,
        planRepository: PlanRepository,
        bcryptService: BcryptService,
        jwtTokenService: JwtTokenService,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new LoginUseCase(
            userRepository,
            institutionRepository,
            subscriptionRepository,
            planRepository,
            bcryptService,
            jwtTokenService,
            logger
          )
        )
    },
    {
      inject: [LoggerService],
      provide: AuthUsecasesProxy.LOGOUT_USECASE_PROXY,
      useFactory: (logger: LoggerService) =>
        new UseCaseProxy(new LogoutUseCase(logger))
    },
    {
      inject: [UserRepository, JwtTokenService, LoggerService],
      provide: AuthUsecasesProxy.REFRESH_TOKEN_USECASE_PROXY,
      useFactory: (
        userRepository: UserRepository,
        jwtTokenService: JwtTokenService,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new RefreshTokenUseCase(userRepository, jwtTokenService, logger)
        )
    },
    {
      inject: [UserRepository, BcryptService, JwtTokenService, LoggerService],
      provide: AuthUsecasesProxy.SET_PASSWORD_USECASE_PROXY,
      useFactory: (
        userRepository: UserRepository,
        bcryptService: BcryptService,
        jwtTokenService: JwtTokenService,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new SetPasswordUseCase(
            userRepository,
            bcryptService,
            jwtTokenService,
            logger
          )
        )
    }
  ],
  exports: [
    AuthUsecasesProxy.LOGIN_USECASE_PROXY,
    AuthUsecasesProxy.LOGOUT_USECASE_PROXY,
    AuthUsecasesProxy.REFRESH_TOKEN_USECASE_PROXY,
    AuthUsecasesProxy.SET_PASSWORD_USECASE_PROXY
  ]
})
export class AuthUsecasesProxyModule {}
