export enum ClassUsecasesProxy {
  GET_CLASS_USECASE_PROXY = 'getClassUsecaseProxy',
  GET_CLASSES_USECASE_PROXY = 'getClassesUsecaseProxy',
  GET_CLASSES_BY_COURSE_USECASE_PROXY = 'getClassesByCourseUsecaseProxy',
  GET_AVAILABLE_CLASSES_USECASE_PROXY = 'getAvailableClassesUsecaseProxy',
  POST_CLASS_USECASE_PROXY = 'postClassUsecaseProxy',
  DELETE_CLASS_USECASE_PROXY = 'deleteClassUsecaseProxy',
  PUT_CLASS_USECASE_PROXY = 'putClassUsecaseProxy'
}
