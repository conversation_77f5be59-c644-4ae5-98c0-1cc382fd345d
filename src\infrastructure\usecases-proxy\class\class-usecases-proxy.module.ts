import { Modu<PERSON> } from '@nestjs/common';
import { ClassRepository } from '../../../domain/abstractions/class-repository.abstraction';
import { CreateClassUseCase } from '../../../usecases/class/create-class.usecase';
import { DeleteClassUseCase } from '../../../usecases/class/delete-class.usecase';
import { GetAvailableClassesUseCase } from '../../../usecases/class/get-available-classes.usecase';
import { GetClassUseCase } from '../../../usecases/class/get-class.usecase';
import { GetClassesByCourseUseCase } from '../../../usecases/class/get-classes-by-course.usecase';
import { GetClassesUseCase } from '../../../usecases/class/get-classes.usecase';
import { UpdateClassUseCase } from '../../../usecases/class/update-class.usecase';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { UseCaseProxy } from '../usecases-proxy';
import { ClassUsecasesProxy } from './class-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule],
  providers: [
    {
      inject: [ClassRepository],
      provide: ClassUsecasesProxy.GET_CLASS_USECASE_PROXY,
      useFactory: (classRepository: ClassRepository) =>
        new UseCaseProxy(new GetClassUseCase(classRepository))
    },
    {
      inject: [ClassRepository],
      provide: ClassUsecasesProxy.GET_CLASSES_USECASE_PROXY,
      useFactory: (classRepository: ClassRepository) =>
        new UseCaseProxy(new GetClassesUseCase(classRepository))
    },
    {
      inject: [ClassRepository],
      provide: ClassUsecasesProxy.GET_CLASSES_BY_COURSE_USECASE_PROXY,
      useFactory: (classRepository: ClassRepository) =>
        new UseCaseProxy(new GetClassesByCourseUseCase(classRepository))
    },
    {
      inject: [ClassRepository],
      provide: ClassUsecasesProxy.GET_AVAILABLE_CLASSES_USECASE_PROXY,
      useFactory: (classRepository: ClassRepository) =>
        new UseCaseProxy(new GetAvailableClassesUseCase(classRepository))
    },
    {
      inject: [ClassRepository],
      provide: ClassUsecasesProxy.POST_CLASS_USECASE_PROXY,
      useFactory: (classRepository: ClassRepository) =>
        new UseCaseProxy(new CreateClassUseCase(classRepository))
    },
    {
      inject: [ClassRepository],
      provide: ClassUsecasesProxy.PUT_CLASS_USECASE_PROXY,
      useFactory: (classRepository: ClassRepository) =>
        new UseCaseProxy(new UpdateClassUseCase(classRepository))
    },
    {
      inject: [ClassRepository],
      provide: ClassUsecasesProxy.DELETE_CLASS_USECASE_PROXY,
      useFactory: (classRepository: ClassRepository) =>
        new UseCaseProxy(new DeleteClassUseCase(classRepository))
    }
  ],
  exports: [
    ClassUsecasesProxy.GET_CLASS_USECASE_PROXY,
    ClassUsecasesProxy.GET_CLASSES_USECASE_PROXY,
    ClassUsecasesProxy.GET_CLASSES_BY_COURSE_USECASE_PROXY,
    ClassUsecasesProxy.GET_AVAILABLE_CLASSES_USECASE_PROXY,
    ClassUsecasesProxy.POST_CLASS_USECASE_PROXY,
    ClassUsecasesProxy.PUT_CLASS_USECASE_PROXY,
    ClassUsecasesProxy.DELETE_CLASS_USECASE_PROXY
  ]
})
export class ClassUsecasesProxyModule {}
