import { DynamicModule, Module } from '@nestjs/common';
import { CourseRepository } from '../../../domain/abstractions/course-repository.abstraction';
import { DatabaseCourseRepository } from '../../repositories/course-repository';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { UseCaseProxy } from '../usecases-proxy';
import { CreateCourseUseCase } from '../../../usecases/course/create-course.usecase';
import { DeleteCourseUseCase } from '../../../usecases/course/delete-course.usecase';
import { GetCourseUseCase } from '../../../usecases/course/get-course.usecase';
import { GetCoursesUseCase } from '../../../usecases/course/get-courses.usecase';
import { GetCoursesByInstitutionUseCase } from '../../../usecases/course/get-courses-by-institution.usecase';
import { UpdateCourseUseCase } from '../../../usecases/course/update-course.usecase';
import { CourseUsecasesProxy } from './course-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule]
})
export class CourseUsecasesProxyModule {
  static GET_COURSE_USECASE_PROXY = CourseUsecasesProxy.GET_COURSE_USECASE_PROXY;
  static GET_COURSES_USECASE_PROXY = CourseUsecasesProxy.GET_COURSES_USECASE_PROXY;
  static GET_COURSES_BY_INSTITUTION_USECASE_PROXY = CourseUsecasesProxy.GET_COURSES_BY_INSTITUTION_USECASE_PROXY;
  static POST_COURSE_USECASE_PROXY = CourseUsecasesProxy.POST_COURSE_USECASE_PROXY;
  static PUT_COURSE_USECASE_PROXY = CourseUsecasesProxy.PUT_COURSE_USECASE_PROXY;
  static DELETE_COURSE_USECASE_PROXY = CourseUsecasesProxy.DELETE_COURSE_USECASE_PROXY;

  static register(): DynamicModule {
    return {
      module: CourseUsecasesProxyModule,
      providers: [
        {
          inject: [CourseRepository],
          provide: CourseUsecasesProxy.GET_COURSE_USECASE_PROXY,
          useFactory: (courseRepository: CourseRepository) =>
            new UseCaseProxy(new GetCourseUseCase(courseRepository))
        },
        {
          inject: [CourseRepository],
          provide: CourseUsecasesProxy.GET_COURSES_USECASE_PROXY,
          useFactory: (courseRepository: CourseRepository) =>
            new UseCaseProxy(new GetCoursesUseCase(courseRepository))
        },
        {
          inject: [CourseRepository],
          provide: CourseUsecasesProxy.GET_COURSES_BY_INSTITUTION_USECASE_PROXY,
          useFactory: (courseRepository: CourseRepository) =>
            new UseCaseProxy(new GetCoursesByInstitutionUseCase(courseRepository))
        },
        {
          inject: [CourseRepository],
          provide: CourseUsecasesProxy.POST_COURSE_USECASE_PROXY,
          useFactory: (courseRepository: CourseRepository) =>
            new UseCaseProxy(new CreateCourseUseCase(courseRepository))
        },
        {
          inject: [CourseRepository],
          provide: CourseUsecasesProxy.PUT_COURSE_USECASE_PROXY,
          useFactory: (courseRepository: CourseRepository) =>
            new UseCaseProxy(new UpdateCourseUseCase(courseRepository))
        },
        {
          inject: [CourseRepository],
          provide: CourseUsecasesProxy.DELETE_COURSE_USECASE_PROXY,
          useFactory: (courseRepository: CourseRepository) =>
            new UseCaseProxy(new DeleteCourseUseCase(courseRepository))
        }
      ],
      exports: [
        CourseUsecasesProxy.GET_COURSE_USECASE_PROXY,
        CourseUsecasesProxy.GET_COURSES_USECASE_PROXY,
        CourseUsecasesProxy.GET_COURSES_BY_INSTITUTION_USECASE_PROXY,
        CourseUsecasesProxy.POST_COURSE_USECASE_PROXY,
        CourseUsecasesProxy.PUT_COURSE_USECASE_PROXY,
        CourseUsecasesProxy.DELETE_COURSE_USECASE_PROXY
      ]
    };
  }
}
