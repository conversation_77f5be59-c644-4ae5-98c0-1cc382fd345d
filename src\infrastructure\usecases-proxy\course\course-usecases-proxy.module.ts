import { Modu<PERSON> } from '@nestjs/common';
import { CourseRepository } from '../../../domain/abstractions/course-repository.abstraction';
import { CreateCourseUseCase } from '../../../usecases/course/create-course.usecase';
import { DeleteCourseUseCase } from '../../../usecases/course/delete-course.usecase';
import { GetCourseUseCase } from '../../../usecases/course/get-course.usecase';
import { GetCoursesByInstitutionUseCase } from '../../../usecases/course/get-courses-by-institution.usecase';
import { GetCoursesUseCase } from '../../../usecases/course/get-courses.usecase';
import { UpdateCourseUseCase } from '../../../usecases/course/update-course.usecase';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { UseCaseProxy } from '../usecases-proxy';
import { CourseUsecasesProxy } from './course-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule],
  providers: [
    {
      inject: [CourseRepository],
      provide: CourseUsecasesProxy.GET_COURSE_USECASE_PROXY,
      useFactory: (courseRepository: CourseRepository) =>
        new UseCaseProxy(new GetCourseUseCase(courseRepository))
    },
    {
      inject: [CourseRepository],
      provide: CourseUsecasesProxy.GET_COURSES_USECASE_PROXY,
      useFactory: (courseRepository: CourseRepository) =>
        new UseCaseProxy(new GetCoursesUseCase(courseRepository))
    },
    {
      inject: [CourseRepository],
      provide: CourseUsecasesProxy.GET_COURSES_BY_INSTITUTION_USECASE_PROXY,
      useFactory: (courseRepository: CourseRepository) =>
        new UseCaseProxy(new GetCoursesByInstitutionUseCase(courseRepository))
    },
    {
      inject: [CourseRepository],
      provide: CourseUsecasesProxy.POST_COURSE_USECASE_PROXY,
      useFactory: (courseRepository: CourseRepository) =>
        new UseCaseProxy(new CreateCourseUseCase(courseRepository))
    },
    {
      inject: [CourseRepository],
      provide: CourseUsecasesProxy.PUT_COURSE_USECASE_PROXY,
      useFactory: (courseRepository: CourseRepository) =>
        new UseCaseProxy(new UpdateCourseUseCase(courseRepository))
    },
    {
      inject: [CourseRepository],
      provide: CourseUsecasesProxy.DELETE_COURSE_USECASE_PROXY,
      useFactory: (courseRepository: CourseRepository) =>
        new UseCaseProxy(new DeleteCourseUseCase(courseRepository))
    }
  ],
  exports: [
    CourseUsecasesProxy.GET_COURSE_USECASE_PROXY,
    CourseUsecasesProxy.GET_COURSES_USECASE_PROXY,
    CourseUsecasesProxy.GET_COURSES_BY_INSTITUTION_USECASE_PROXY,
    CourseUsecasesProxy.POST_COURSE_USECASE_PROXY,
    CourseUsecasesProxy.PUT_COURSE_USECASE_PROXY,
    CourseUsecasesProxy.DELETE_COURSE_USECASE_PROXY
  ]
})
export class CourseUsecasesProxyModule {}
