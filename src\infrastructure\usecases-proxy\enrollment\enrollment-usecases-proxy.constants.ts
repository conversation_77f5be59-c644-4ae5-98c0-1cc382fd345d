export enum EnrollmentUsecasesProxy {
  GET_ENROLLMENT_USECASE_PROXY = 'getEnrollmentUsecaseProxy',
  GET_ENROLLMENTS_USECASE_PROXY = 'getEnrollmentsUsecaseProxy',
  GET_ENROLLMENTS_BY_STUDENT_USECASE_PROXY = 'getEnrollmentsByStudentUsecaseProxy',
  GET_ENROLLMENTS_BY_CLASS_USECASE_PROXY = 'getEnrollmentsByClassUsecaseProxy',
  GET_PENDING_ENROLLMENTS_USECASE_PROXY = 'getPendingEnrollmentsUsecaseProxy',
  POST_ENROLLMENT_USECASE_PROXY = 'postEnrollmentUsecaseProxy',
  DELETE_ENROLLMENT_USECASE_PROXY = 'deleteEnrollmentUsecaseProxy',
  APPROVE_ENROLLMENT_USECASE_PROXY = 'approveEnrollmentUsecaseProxy',
  REJECT_ENROLLMENT_USECASE_PROXY = 'rejectEnrollmentUsecaseProxy',
  CANCEL_ENROLLMENT_USECASE_PROXY = 'cancelEnrollmentUsecaseProxy'
}
