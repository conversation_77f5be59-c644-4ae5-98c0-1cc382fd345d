import { Module } from '@nestjs/common';
import { EnrollmentRepository } from '../../../domain/abstractions/enrollment-repository.abstraction';
import { ClassRepository } from '../../../domain/abstractions/class-repository.abstraction';
import { CreateEnrollmentUseCase } from '../../../usecases/enrollment/create-enrollment.usecase';
import { DeleteEnrollmentUseCase } from '../../../usecases/enrollment/delete-enrollment.usecase';
import { GetEnrollmentUseCase } from '../../../usecases/enrollment/get-enrollment.usecase';
import { GetEnrollmentsByStudentUseCase } from '../../../usecases/enrollment/get-enrollments-by-student.usecase';
import { GetEnrollmentsByClassUseCase } from '../../../usecases/enrollment/get-enrollments-by-class.usecase';
import { GetEnrollmentsUseCase } from '../../../usecases/enrollment/get-enrollments.usecase';
import { GetPendingEnrollmentsUseCase } from '../../../usecases/enrollment/get-pending-enrollments.usecase';
import { ApproveEnrollmentUseCase } from '../../../usecases/enrollment/approve-enrollment.usecase';
import { RejectEnrollmentUseCase } from '../../../usecases/enrollment/reject-enrollment.usecase';
import { CancelEnrollmentUseCase } from '../../../usecases/enrollment/cancel-enrollment.usecase';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { UseCaseProxy } from '../usecases-proxy';
import { EnrollmentUsecasesProxy } from './enrollment-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule],
  providers: [
    {
      inject: [EnrollmentRepository],
      provide: EnrollmentUsecasesProxy.GET_ENROLLMENT_USECASE_PROXY,
      useFactory: (enrollmentRepository: EnrollmentRepository) =>
        new UseCaseProxy(new GetEnrollmentUseCase(enrollmentRepository))
    },
    {
      inject: [EnrollmentRepository],
      provide: EnrollmentUsecasesProxy.GET_ENROLLMENTS_USECASE_PROXY,
      useFactory: (enrollmentRepository: EnrollmentRepository) =>
        new UseCaseProxy(new GetEnrollmentsUseCase(enrollmentRepository))
    },
    {
      inject: [EnrollmentRepository],
      provide: EnrollmentUsecasesProxy.GET_ENROLLMENTS_BY_STUDENT_USECASE_PROXY,
      useFactory: (enrollmentRepository: EnrollmentRepository) =>
        new UseCaseProxy(
          new GetEnrollmentsByStudentUseCase(enrollmentRepository)
        )
    },
    {
      inject: [EnrollmentRepository],
      provide: EnrollmentUsecasesProxy.GET_ENROLLMENTS_BY_CLASS_USECASE_PROXY,
      useFactory: (enrollmentRepository: EnrollmentRepository) =>
        new UseCaseProxy(new GetEnrollmentsByClassUseCase(enrollmentRepository))
    },
    {
      inject: [EnrollmentRepository],
      provide: EnrollmentUsecasesProxy.GET_PENDING_ENROLLMENTS_USECASE_PROXY,
      useFactory: (enrollmentRepository: EnrollmentRepository) =>
        new UseCaseProxy(new GetPendingEnrollmentsUseCase(enrollmentRepository))
    },
    {
      inject: [EnrollmentRepository, ClassRepository],
      provide: EnrollmentUsecasesProxy.POST_ENROLLMENT_USECASE_PROXY,
      useFactory: (
        enrollmentRepository: EnrollmentRepository,
        classRepository: ClassRepository
      ) =>
        new UseCaseProxy(
          new CreateEnrollmentUseCase(enrollmentRepository, classRepository)
        )
    },
    {
      inject: [EnrollmentRepository, ClassRepository],
      provide: EnrollmentUsecasesProxy.DELETE_ENROLLMENT_USECASE_PROXY,
      useFactory: (
        enrollmentRepository: EnrollmentRepository,
        classRepository: ClassRepository
      ) =>
        new UseCaseProxy(
          new DeleteEnrollmentUseCase(enrollmentRepository, classRepository)
        )
    },
    {
      inject: [EnrollmentRepository, ClassRepository],
      provide: EnrollmentUsecasesProxy.APPROVE_ENROLLMENT_USECASE_PROXY,
      useFactory: (
        enrollmentRepository: EnrollmentRepository,
        classRepository: ClassRepository
      ) =>
        new UseCaseProxy(
          new ApproveEnrollmentUseCase(enrollmentRepository, classRepository)
        )
    },
    {
      inject: [EnrollmentRepository],
      provide: EnrollmentUsecasesProxy.REJECT_ENROLLMENT_USECASE_PROXY,
      useFactory: (enrollmentRepository: EnrollmentRepository) =>
        new UseCaseProxy(new RejectEnrollmentUseCase(enrollmentRepository))
    },
    {
      inject: [EnrollmentRepository, ClassRepository],
      provide: EnrollmentUsecasesProxy.CANCEL_ENROLLMENT_USECASE_PROXY,
      useFactory: (
        enrollmentRepository: EnrollmentRepository,
        classRepository: ClassRepository
      ) =>
        new UseCaseProxy(
          new CancelEnrollmentUseCase(enrollmentRepository, classRepository)
        )
    }
  ],
  exports: [
    EnrollmentUsecasesProxy.GET_ENROLLMENT_USECASE_PROXY,
    EnrollmentUsecasesProxy.GET_ENROLLMENTS_USECASE_PROXY,
    EnrollmentUsecasesProxy.GET_ENROLLMENTS_BY_STUDENT_USECASE_PROXY,
    EnrollmentUsecasesProxy.GET_ENROLLMENTS_BY_CLASS_USECASE_PROXY,
    EnrollmentUsecasesProxy.GET_PENDING_ENROLLMENTS_USECASE_PROXY,
    EnrollmentUsecasesProxy.POST_ENROLLMENT_USECASE_PROXY,
    EnrollmentUsecasesProxy.DELETE_ENROLLMENT_USECASE_PROXY,
    EnrollmentUsecasesProxy.APPROVE_ENROLLMENT_USECASE_PROXY,
    EnrollmentUsecasesProxy.REJECT_ENROLLMENT_USECASE_PROXY,
    EnrollmentUsecasesProxy.CANCEL_ENROLLMENT_USECASE_PROXY
  ]
})
export class EnrollmentUsecasesProxyModule {}
