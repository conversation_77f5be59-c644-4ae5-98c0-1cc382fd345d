export enum FinancialUsecasesProxy {
  // Financial Transactions
  CREATE_FINANCIAL_TRANSACTION_USECASE_PROXY = 'createFinancialTransactionUsecaseProxy',
  GET_FINANCIAL_TRANSACTIONS_USECASE_PROXY = 'getFinancialTransactionsUsecaseProxy',
  UPDATE_FINANCIAL_TRANSACTION_USECASE_PROXY = 'updateFinancialTransactionUsecaseProxy',
  GET_FINANCIAL_SUMMARY_USECASE_PROXY = 'getFinancialSummaryUsecaseProxy',

  // Invoices
  CREATE_INVOICE_USECASE_PROXY = 'createInvoiceUsecaseProxy',
  PROCESS_INVOICE_PAYMENT_USECASE_PROXY = 'processInvoicePaymentUsecaseProxy'
}
