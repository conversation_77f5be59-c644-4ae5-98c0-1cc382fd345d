import { Module } from '@nestjs/common';
import {
  FinancialTransactionRepository,
  InvoiceRepository
} from '../../../domain/abstractions/financial-repository.abstraction';
import { CreateFinancialTransactionUseCase } from '../../../usecases/financial/create-financial-transaction.usecase';
import { GetFinancialTransactionsUseCase } from '../../../usecases/financial/get-financial-transactions.usecase';
import { UpdateFinancialTransactionUseCase } from '../../../usecases/financial/update-financial-transaction.usecase';
import { GetFinancialSummaryUseCase } from '../../../usecases/financial/get-financial-summary.usecase';
import { CreateInvoiceUseCase } from '../../../usecases/financial/create-invoice.usecase';
import { ProcessInvoicePaymentUseCase } from '../../../usecases/financial/process-invoice-payment.usecase';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { UseCaseProxy } from '../usecases-proxy';
import { FinancialUsecasesProxy } from './financial-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule],
  providers: [
    {
      inject: [FinancialTransactionRepository],
      provide:
        FinancialUsecasesProxy.CREATE_FINANCIAL_TRANSACTION_USECASE_PROXY,
      useFactory: (
        financialTransactionRepository: FinancialTransactionRepository
      ) =>
        new UseCaseProxy(
          new CreateFinancialTransactionUseCase(financialTransactionRepository)
        )
    },
    {
      inject: [FinancialTransactionRepository],
      provide: FinancialUsecasesProxy.GET_FINANCIAL_TRANSACTIONS_USECASE_PROXY,
      useFactory: (
        financialTransactionRepository: FinancialTransactionRepository
      ) =>
        new UseCaseProxy(
          new GetFinancialTransactionsUseCase(financialTransactionRepository)
        )
    },
    {
      inject: [FinancialTransactionRepository],
      provide:
        FinancialUsecasesProxy.UPDATE_FINANCIAL_TRANSACTION_USECASE_PROXY,
      useFactory: (
        financialTransactionRepository: FinancialTransactionRepository
      ) =>
        new UseCaseProxy(
          new UpdateFinancialTransactionUseCase(financialTransactionRepository)
        )
    },
    {
      inject: [FinancialTransactionRepository],
      provide: FinancialUsecasesProxy.GET_FINANCIAL_SUMMARY_USECASE_PROXY,
      useFactory: (
        financialTransactionRepository: FinancialTransactionRepository
      ) =>
        new UseCaseProxy(
          new GetFinancialSummaryUseCase(financialTransactionRepository)
        )
    },
    {
      inject: [InvoiceRepository],
      provide: FinancialUsecasesProxy.CREATE_INVOICE_USECASE_PROXY,
      useFactory: (invoiceRepository: InvoiceRepository) =>
        new UseCaseProxy(new CreateInvoiceUseCase(invoiceRepository))
    },
    {
      inject: [InvoiceRepository, FinancialTransactionRepository],
      provide: FinancialUsecasesProxy.PROCESS_INVOICE_PAYMENT_USECASE_PROXY,
      useFactory: (
        invoiceRepository: InvoiceRepository,
        financialTransactionRepository: FinancialTransactionRepository
      ) =>
        new UseCaseProxy(
          new ProcessInvoicePaymentUseCase(
            invoiceRepository,
            financialTransactionRepository
          )
        )
    }
  ],
  exports: [
    FinancialUsecasesProxy.CREATE_FINANCIAL_TRANSACTION_USECASE_PROXY,
    FinancialUsecasesProxy.GET_FINANCIAL_TRANSACTIONS_USECASE_PROXY,
    FinancialUsecasesProxy.UPDATE_FINANCIAL_TRANSACTION_USECASE_PROXY,
    FinancialUsecasesProxy.GET_FINANCIAL_SUMMARY_USECASE_PROXY,
    FinancialUsecasesProxy.CREATE_INVOICE_USECASE_PROXY,
    FinancialUsecasesProxy.PROCESS_INVOICE_PAYMENT_USECASE_PROXY
  ]
})
export class FinancialUsecasesProxyModule {}
