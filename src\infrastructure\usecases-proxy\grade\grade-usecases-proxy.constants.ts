export enum GradeUsecasesProxy {
  GET_GRADE_USECASE_PROXY = 'getGradeUsecaseProxy',
  GET_GRADES_USECASE_PROXY = 'getGradesUsecaseProxy',
  GET_GRADES_BY_ENROLLMENT_USECASE_PROXY = 'getGradesByEnrollmentUsecaseProxy',
  GET_GRADES_BY_ASSESSMENT_TYPE_USECASE_PROXY = 'getGradesByAssessmentTypeUsecaseProxy',
  CALCULATE_WEIGHTED_AVERAGE_USECASE_PROXY = 'calculateWeightedAverageUsecaseProxy',
  POST_GRADE_USECASE_PROXY = 'postGradeUsecaseProxy',
  PUT_GRADE_USECASE_PROXY = 'putGradeUsecaseProxy',
  DELETE_GRADE_USECASE_PROXY = 'deleteGradeUsecaseProxy'
}
