import { Module } from '@nestjs/common';
import { GradeRepository } from '../../../domain/abstractions/grade-repository.abstraction';
import { EnrollmentRepository } from '../../../domain/abstractions/enrollment-repository.abstraction';
import { CreateGradeUseCase } from '../../../usecases/grade/create-grade.usecase';
import { DeleteGradeUseCase } from '../../../usecases/grade/delete-grade.usecase';
import { GetGradeUseCase } from '../../../usecases/grade/get-grade.usecase';
import { GetGradesByEnrollmentUseCase } from '../../../usecases/grade/get-grades-by-enrollment.usecase';
import { GetGradesByAssessmentTypeUseCase } from '../../../usecases/grade/get-grades-by-assessment-type.usecase';
import { GetGradesUseCase } from '../../../usecases/grade/get-grades.usecase';
import { CalculateWeightedAverageUseCase } from '../../../usecases/grade/calculate-weighted-average.usecase';
import { UpdateGradeUseCase } from '../../../usecases/grade/update-grade.usecase';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { UseCaseProxy } from '../usecases-proxy';
import { GradeUsecasesProxy } from './grade-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule],
  providers: [
    {
      inject: [GradeRepository],
      provide: GradeUsecasesProxy.GET_GRADE_USECASE_PROXY,
      useFactory: (gradeRepository: GradeRepository) =>
        new UseCaseProxy(new GetGradeUseCase(gradeRepository))
    },
    {
      inject: [GradeRepository],
      provide: GradeUsecasesProxy.GET_GRADES_USECASE_PROXY,
      useFactory: (gradeRepository: GradeRepository) =>
        new UseCaseProxy(new GetGradesUseCase(gradeRepository))
    },
    {
      inject: [GradeRepository],
      provide: GradeUsecasesProxy.GET_GRADES_BY_ENROLLMENT_USECASE_PROXY,
      useFactory: (gradeRepository: GradeRepository) =>
        new UseCaseProxy(new GetGradesByEnrollmentUseCase(gradeRepository))
    },
    {
      inject: [GradeRepository],
      provide: GradeUsecasesProxy.GET_GRADES_BY_ASSESSMENT_TYPE_USECASE_PROXY,
      useFactory: (gradeRepository: GradeRepository) =>
        new UseCaseProxy(new GetGradesByAssessmentTypeUseCase(gradeRepository))
    },
    {
      inject: [GradeRepository],
      provide: GradeUsecasesProxy.CALCULATE_WEIGHTED_AVERAGE_USECASE_PROXY,
      useFactory: (gradeRepository: GradeRepository) =>
        new UseCaseProxy(new CalculateWeightedAverageUseCase(gradeRepository))
    },
    {
      inject: [GradeRepository, EnrollmentRepository],
      provide: GradeUsecasesProxy.POST_GRADE_USECASE_PROXY,
      useFactory: (gradeRepository: GradeRepository, enrollmentRepository: EnrollmentRepository) =>
        new UseCaseProxy(new CreateGradeUseCase(gradeRepository, enrollmentRepository))
    },
    {
      inject: [GradeRepository],
      provide: GradeUsecasesProxy.PUT_GRADE_USECASE_PROXY,
      useFactory: (gradeRepository: GradeRepository) =>
        new UseCaseProxy(new UpdateGradeUseCase(gradeRepository))
    },
    {
      inject: [GradeRepository],
      provide: GradeUsecasesProxy.DELETE_GRADE_USECASE_PROXY,
      useFactory: (gradeRepository: GradeRepository) =>
        new UseCaseProxy(new DeleteGradeUseCase(gradeRepository))
    }
  ],
  exports: [
    GradeUsecasesProxy.GET_GRADE_USECASE_PROXY,
    GradeUsecasesProxy.GET_GRADES_USECASE_PROXY,
    GradeUsecasesProxy.GET_GRADES_BY_ENROLLMENT_USECASE_PROXY,
    GradeUsecasesProxy.GET_GRADES_BY_ASSESSMENT_TYPE_USECASE_PROXY,
    GradeUsecasesProxy.CALCULATE_WEIGHTED_AVERAGE_USECASE_PROXY,
    GradeUsecasesProxy.POST_GRADE_USECASE_PROXY,
    GradeUsecasesProxy.PUT_GRADE_USECASE_PROXY,
    GradeUsecasesProxy.DELETE_GRADE_USECASE_PROXY
  ]
})
export class GradeUsecasesProxyModule {}
