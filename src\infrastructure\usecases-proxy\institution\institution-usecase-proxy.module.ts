import { Module } from '@nestjs/common';
import { CreateInstitutionUseCase } from '../../../usecases/institution/create-institution.usecase';
import { DeleteInstitutionUseCase } from '../../../usecases/institution/delete-institution.usecase';
import { GetInstitutionUseCase } from '../../../usecases/institution/get-institution.usecase';
import { GetInstitutionsUseCase } from '../../../usecases/institution/get-institutions.usecase';
import { UpdateInstitutionUseCase } from '../../../usecases/institution/update-institution.usecase';
import { LoggerService } from '../../logger/logger.service';
import { InstitutionRepository } from '../../repositories/institution-repository';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { TwoFactorAuthenticationService } from '../../services/2fa/two-factor-authentication.service';
import { EmailModule } from '../../services/email/email.module';
import { EmailService } from '../../services/email/email.service';
import { UseCaseProxy } from '../usecases-proxy';
import { InstitutionUsecasesProxy } from './institution-usecase-proxy.constants';

@Module({
  imports: [RepositoriesModule, EmailModule],
  providers: [
    TwoFactorAuthenticationService,
    LoggerService,
    EmailService,
    {
      inject: [InstitutionRepository, LoggerService, EmailService],
      provide: InstitutionUsecasesProxy.POST_INSTITUTION_USECASE_PROXY,
      useFactory: (
        institutionRepository: InstitutionRepository,
        logger: LoggerService,
        emailService: EmailService
      ) =>
        new UseCaseProxy(
          new CreateInstitutionUseCase(
            institutionRepository,
            logger,
            emailService
          )
        )
    },
    {
      inject: [InstitutionRepository, LoggerService],
      provide: InstitutionUsecasesProxy.GET_INSTITUTIONS_USECASE_PROXY,
      useFactory: (
        institutionRepository: InstitutionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new GetInstitutionsUseCase(institutionRepository, logger)
        )
    },
    {
      inject: [InstitutionRepository, LoggerService],
      provide: InstitutionUsecasesProxy.GET_INSTITUTION_USECASE_PROXY,
      useFactory: (
        institutionRepository: InstitutionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new GetInstitutionUseCase(institutionRepository, logger)
        )
    },
    {
      inject: [InstitutionRepository, LoggerService],
      provide: InstitutionUsecasesProxy.DELETE_INSTITUTION_USECASE_PROXY,
      useFactory: (
        institutionRepository: InstitutionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new DeleteInstitutionUseCase(institutionRepository, logger)
        )
    },
    {
      inject: [InstitutionRepository, LoggerService],
      provide: InstitutionUsecasesProxy.UPDATE_INSTITUTION_USECASE_PROXY,
      useFactory: (
        institutionRepository: InstitutionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new UpdateInstitutionUseCase(institutionRepository, logger)
        )
    }
  ],
  exports: [
    InstitutionUsecasesProxy.POST_INSTITUTION_USECASE_PROXY,
    InstitutionUsecasesProxy.GET_INSTITUTION_USECASE_PROXY,
    InstitutionUsecasesProxy.GET_INSTITUTIONS_USECASE_PROXY,
    InstitutionUsecasesProxy.DELETE_INSTITUTION_USECASE_PROXY,
    InstitutionUsecasesProxy.UPDATE_INSTITUTION_USECASE_PROXY
  ]
})
export class InstitutionUsecasesProxyModule {}
