import { Module } from '@nestjs/common';
import { CreatePermissionUseCase } from '../../../usecases/permission/create-permission.usecase';
import { DeletePermissionUseCase } from '../../../usecases/permission/delete-permission.usecase';
import { GetPermissionUseCase } from '../../../usecases/permission/get-permission.usecase';
import { GetPermissionsUseCase } from '../../../usecases/permission/get-permissions.usecase';
import { UpdatePermissionUseCase } from '../../../usecases/permission/update-permission.usecase';
import { LoggerService } from '../../logger/logger.service';
import { PermissionRepository } from '../../repositories/permission-repository';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { TwoFactorAuthenticationService } from '../../services/2fa/two-factor-authentication.service';
import { EmailModule } from '../../services/email/email.module';
import { EmailService } from '../../services/email/email.service';
import { JwtModule } from '../../services/jwt/jwt.module';
import { UseCaseProxy } from '../usecases-proxy';
import { PermissionUsecasesProxy } from './permission-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule, JwtModule, EmailModule],
  providers: [
    TwoFactorAuthenticationService,
    LoggerService,
    EmailService,
    {
      inject: [PermissionRepository, LoggerService],
      provide: PermissionUsecasesProxy.POST_PERMISSION_USECASE_PROXY,
      useFactory: (
        permissionRepository: PermissionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new CreatePermissionUseCase(permissionRepository, logger)
        )
    },
    {
      inject: [PermissionRepository, LoggerService],
      provide: PermissionUsecasesProxy.GET_PERMISSIONS_USECASE_PROXY,
      useFactory: (
        permissionRepository: PermissionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new GetPermissionsUseCase(permissionRepository, logger)
        )
    },
    {
      inject: [PermissionRepository, LoggerService],
      provide: PermissionUsecasesProxy.GET_PERMISSION_USECASE_PROXY,
      useFactory: (
        permissionRepository: PermissionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(new GetPermissionUseCase(permissionRepository, logger))
    },
    {
      inject: [PermissionRepository, LoggerService],
      provide: PermissionUsecasesProxy.DELETE_PERMISSION_USECASE_PROXY,
      useFactory: (
        permissionRepository: PermissionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new DeletePermissionUseCase(permissionRepository, logger)
        )
    },
    {
      inject: [PermissionRepository, LoggerService],
      provide: PermissionUsecasesProxy.UPDATE_PERMISSION_USECASE_PROXY,
      useFactory: (
        permissionRepository: PermissionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new UpdatePermissionUseCase(permissionRepository, logger)
        )
    }
  ],
  exports: [
    PermissionUsecasesProxy.DELETE_PERMISSION_USECASE_PROXY,
    PermissionUsecasesProxy.GET_PERMISSION_USECASE_PROXY,
    PermissionUsecasesProxy.GET_PERMISSIONS_USECASE_PROXY,
    PermissionUsecasesProxy.POST_PERMISSION_USECASE_PROXY,
    PermissionUsecasesProxy.UPDATE_PERMISSION_USECASE_PROXY
  ]
})
export class PermissionUsecasesProxyModule {}
