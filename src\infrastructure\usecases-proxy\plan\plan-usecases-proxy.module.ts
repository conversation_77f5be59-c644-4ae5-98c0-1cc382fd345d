import { <PERSON>du<PERSON> } from '@nestjs/common';
import { CreatePlanUseCase } from '../../../usecases/plan/create-plan.usecase';
import { DeletePlanUseCase } from '../../../usecases/plan/delete-plan.usecase';
import { GetActivePlansUseCase } from '../../../usecases/plan/get-active-plans.usecase';
import { GetPlanUseCase } from '../../../usecases/plan/get-plan.usecase';
import { GetPlansUseCase } from '../../../usecases/plan/get-plans.usecase';
import { UpdatePlanUseCase } from '../../../usecases/plan/update-plan.usecase';
import { LoggerService } from '../../logger/logger.service';
import { PlanRepository } from '../../repositories/plan-repository';
import { SubscriptionRepository } from '../../repositories/subscription-repository';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { TwoFactorAuthenticationService } from '../../services/2fa/two-factor-authentication.service';
import { EmailService } from '../../services/email/email.service';
import { JwtModule } from '../../services/jwt/jwt.module';
import { EmailModule } from '../../services/email/email.module';
import { UseCaseProxy } from '../usecases-proxy';
import { PlanUsecasesProxy } from './plan-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule, JwtModule, EmailModule],
  providers: [
    TwoFactorAuthenticationService,
    LoggerService,
    EmailService,
    {
      inject: [PlanRepository, LoggerService],
      provide: PlanUsecasesProxy.POST_PLAN_USECASE_PROXY,
      useFactory: (planRepository: PlanRepository, logger: LoggerService) =>
        new UseCaseProxy(new CreatePlanUseCase(planRepository, logger))
    },
    {
      inject: [PlanRepository, LoggerService],
      provide: PlanUsecasesProxy.GET_PLANS_USECASE_PROXY,
      useFactory: (planRepository: PlanRepository, logger: LoggerService) =>
        new UseCaseProxy(new GetPlansUseCase(planRepository, logger))
    },
    {
      inject: [PlanRepository, LoggerService],
      provide: PlanUsecasesProxy.GET_ACTIVE_PLANS_USECASE_PROXY,
      useFactory: (planRepository: PlanRepository, logger: LoggerService) =>
        new UseCaseProxy(new GetActivePlansUseCase(planRepository, logger))
    },
    {
      inject: [PlanRepository, LoggerService],
      provide: PlanUsecasesProxy.GET_PLAN_USECASE_PROXY,
      useFactory: (planRepository: PlanRepository, logger: LoggerService) =>
        new UseCaseProxy(new GetPlanUseCase(planRepository, logger))
    },
    {
      inject: [PlanRepository, SubscriptionRepository, LoggerService],
      provide: PlanUsecasesProxy.DELETE_PLAN_USECASE_PROXY,
      useFactory: (
        planRepository: PlanRepository,
        subscriptionRepository: SubscriptionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new DeletePlanUseCase(planRepository, subscriptionRepository, logger)
        )
    },
    {
      inject: [PlanRepository, LoggerService],
      provide: PlanUsecasesProxy.UPDATE_PLAN_USECASE_PROXY,
      useFactory: (planRepository: PlanRepository, logger: LoggerService) =>
        new UseCaseProxy(new UpdatePlanUseCase(planRepository, logger))
    }
  ],
  exports: [
    PlanUsecasesProxy.GET_PLAN_USECASE_PROXY,
    PlanUsecasesProxy.GET_PLANS_USECASE_PROXY,
    PlanUsecasesProxy.GET_ACTIVE_PLANS_USECASE_PROXY,
    PlanUsecasesProxy.POST_PLAN_USECASE_PROXY,
    PlanUsecasesProxy.DELETE_PLAN_USECASE_PROXY,
    PlanUsecasesProxy.UPDATE_PLAN_USECASE_PROXY
  ]
})
export class PlanUsecasesProxyModule {}
