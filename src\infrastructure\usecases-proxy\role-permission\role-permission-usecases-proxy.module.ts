import { <PERSON>du<PERSON> } from '@nestjs/common';
import { CreateRolePermissionUseCase } from '../../../usecases/role_permission/create-role-permission.usecase';
import { DeleteRolePermissionUseCase } from '../../../usecases/role_permission/delete-role-permission.usecase';
import { GetRolePermissionUseCase } from '../../../usecases/role_permission/get-role-permission.usecase';
import { GetRolePermissionsUseCase } from '../../../usecases/role_permission/get-role-permissions.usecase';
import { UpdateRolePermissionUseCase } from '../../../usecases/role_permission/update-role-permission.usecase';
import { LoggerService } from '../../logger/logger.service';
import { RolePermissionRepository } from '../../repositories/role_permission-repository';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { TwoFactorAuthenticationService } from '../../services/2fa/two-factor-authentication.service';
import { EmailService } from '../../services/email/email.service';
import { JwtModule } from '../../services/jwt/jwt.module';
import { EmailModule } from '../../services/email/email.module';
import { UseCaseProxy } from '../usecases-proxy';
import { RolePermissionUsecasesProxy } from './role-permission-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule, JwtModule, EmailModule],
  providers: [
    TwoFactorAuthenticationService,
    LoggerService,
    EmailService,
    {
      inject: [RolePermissionRepository, LoggerService],
      provide: RolePermissionUsecasesProxy.POST_ROLE_PERMISSION_USECASE_PROXY,
      useFactory: (
        rolePermissionRepository: RolePermissionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new CreateRolePermissionUseCase(rolePermissionRepository, logger)
        )
    },
    {
      inject: [RolePermissionRepository, LoggerService],
      provide: RolePermissionUsecasesProxy.GET_ROLE_PERMISSIONS_USECASE_PROXY,
      useFactory: (
        rolePermissionRepository: RolePermissionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new GetRolePermissionsUseCase(rolePermissionRepository, logger)
        )
    },
    {
      inject: [RolePermissionRepository, LoggerService],
      provide: RolePermissionUsecasesProxy.GET_ROLE_PERMISSION_USECASE_PROXY,
      useFactory: (
        rolePermissionRepository: RolePermissionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new GetRolePermissionUseCase(rolePermissionRepository, logger)
        )
    },
    {
      inject: [RolePermissionRepository, LoggerService],
      provide: RolePermissionUsecasesProxy.DELETE_ROLE_PERMISSION_USECASE_PROXY,
      useFactory: (
        rolePermissionRepository: RolePermissionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new DeleteRolePermissionUseCase(rolePermissionRepository, logger)
        )
    },
    {
      inject: [RolePermissionRepository, LoggerService],
      provide: RolePermissionUsecasesProxy.UPDATE_ROLE_PERMISSION_USECASE_PROXY,
      useFactory: (
        rolePermissionRepository: RolePermissionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new UpdateRolePermissionUseCase(rolePermissionRepository, logger)
        )
    }
  ],
  exports: [
    RolePermissionUsecasesProxy.GET_ROLE_PERMISSION_USECASE_PROXY,
    RolePermissionUsecasesProxy.GET_ROLE_PERMISSIONS_USECASE_PROXY,
    RolePermissionUsecasesProxy.POST_ROLE_PERMISSION_USECASE_PROXY,
    RolePermissionUsecasesProxy.DELETE_ROLE_PERMISSION_USECASE_PROXY,
    RolePermissionUsecasesProxy.UPDATE_ROLE_PERMISSION_USECASE_PROXY
  ]
})
export class RolePermissionUsecasesProxyModule {}
