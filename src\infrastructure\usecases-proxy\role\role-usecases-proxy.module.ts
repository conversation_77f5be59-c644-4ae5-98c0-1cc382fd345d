import { Module } from '@nestjs/common';
import { RoleRepository } from '../../../infrastructure/repositories/role-repository';
import { CreateRoleUseCase } from '../../../usecases/role/create-role.usecase';
import { DeleteRoleUseCase } from '../../../usecases/role/delete-role.usecase';
import { GetRoleUseCase } from '../../../usecases/role/get-role.usecase';
import { GetRolesUseCase } from '../../../usecases/role/get-roles.usecase';
import { UpdateRoleUseCase } from '../../../usecases/role/update-role.usecase';
import { LoggerService } from '../../logger/logger.service';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { TwoFactorAuthenticationService } from '../../services/2fa/two-factor-authentication.service';
import { EmailModule } from '../../services/email/email.module';
import { EmailService } from '../../services/email/email.service';
import { JwtModule } from '../../services/jwt/jwt.module';
import { UseCaseProxy } from '../usecases-proxy';
import { RoleUsecasesProxy } from './role-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule, JwtModule, EmailModule],
  providers: [
    TwoFactorAuthenticationService,
    LoggerService,
    EmailService,
    {
      inject: [RoleRepository, LoggerService],
      provide: RoleUsecasesProxy.POST_ROLE_USECASE_PROXY,
      useFactory: (roleRepository: RoleRepository, logger: LoggerService) =>
        new UseCaseProxy(new CreateRoleUseCase(roleRepository, logger))
    },
    {
      inject: [RoleRepository, LoggerService],
      provide: RoleUsecasesProxy.GET_ROLES_USECASE_PROXY,
      useFactory: (roleRepository: RoleRepository, logger: LoggerService) =>
        new UseCaseProxy(new GetRolesUseCase(roleRepository, logger))
    },
    {
      inject: [RoleRepository, LoggerService],
      provide: RoleUsecasesProxy.GET_ROLE_USECASE_PROXY,
      useFactory: (roleRepository: RoleRepository, logger: LoggerService) =>
        new UseCaseProxy(new GetRoleUseCase(roleRepository, logger))
    },
    {
      inject: [RoleRepository, LoggerService],
      provide: RoleUsecasesProxy.DELETE_ROLE_USECASE_PROXY,
      useFactory: (roleRepository: RoleRepository, logger: LoggerService) =>
        new UseCaseProxy(new DeleteRoleUseCase(roleRepository, logger))
    },
    {
      inject: [RoleRepository, LoggerService],
      provide: RoleUsecasesProxy.UPDATE_ROLE_USECASE_PROXY,
      useFactory: (roleRepository: RoleRepository, logger: LoggerService) =>
        new UseCaseProxy(new UpdateRoleUseCase(roleRepository, logger))
    }
  ],
  exports: [
    RoleUsecasesProxy.DELETE_ROLE_USECASE_PROXY,
    RoleUsecasesProxy.GET_ROLE_USECASE_PROXY,
    RoleUsecasesProxy.GET_ROLES_USECASE_PROXY,
    RoleUsecasesProxy.POST_ROLE_USECASE_PROXY,
    RoleUsecasesProxy.UPDATE_ROLE_USECASE_PROXY
  ]
})
export class RoleUsecasesProxyModule {}
