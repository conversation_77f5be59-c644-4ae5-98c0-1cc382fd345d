import { <PERSON>du<PERSON> } from '@nestjs/common';
import { CancelSubscriptionUseCase } from '../../../usecases/subscription/cancel-subscription.usecase';
import { CreateSubscriptionUseCase } from '../../../usecases/subscription/create-subscription.usecase';
import { GetInstitutionSubscriptionsUseCase } from '../../../usecases/subscription/get-institution-subscriptions.usecase';
import { GetSubscriptionUseCase } from '../../../usecases/subscription/get-subscription.usecase';
import { GetSubscriptionsUseCase } from '../../../usecases/subscription/get-subscriptions.usecase';
import { LoggerService } from '../../logger/logger.service';
import { InstitutionRepository } from '../../repositories/institution-repository';
import { PlanRepository } from '../../repositories/plan-repository';
import { SubscriptionRepository } from '../../repositories/subscription-repository';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { TwoFactorAuthenticationService } from '../../services/2fa/two-factor-authentication.service';
import { EmailService } from '../../services/email/email.service';
import { JwtModule } from '../../services/jwt/jwt.module';
import { EmailModule } from '../../services/email/email.module';
import { UseCaseProxy } from '../usecases-proxy';
import { SubscriptionUsecasesProxy } from './subscription-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule, JwtModule, EmailModule],
  providers: [
    TwoFactorAuthenticationService,
    LoggerService,
    EmailService,
    {
      inject: [
        SubscriptionRepository,
        InstitutionRepository,
        PlanRepository,
        LoggerService
      ],
      provide: SubscriptionUsecasesProxy.POST_SUBSCRIPTION_USECASE_PROXY,
      useFactory: (
        subscriptionRepository: SubscriptionRepository,
        institutionRepository: InstitutionRepository,
        planRepository: PlanRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new CreateSubscriptionUseCase(
            subscriptionRepository,
            institutionRepository,
            planRepository,
            logger
          )
        )
    },
    {
      inject: [SubscriptionRepository, LoggerService],
      provide: SubscriptionUsecasesProxy.GET_SUBSCRIPTIONS_USECASE_PROXY,
      useFactory: (
        subscriptionRepository: SubscriptionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new GetSubscriptionsUseCase(subscriptionRepository, logger)
        )
    },
    {
      inject: [SubscriptionRepository, LoggerService],
      provide: SubscriptionUsecasesProxy.GET_SUBSCRIPTION_USECASE_PROXY,
      useFactory: (
        subscriptionRepository: SubscriptionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new GetSubscriptionUseCase(subscriptionRepository, logger)
        )
    },
    {
      inject: [SubscriptionRepository, LoggerService],
      provide: SubscriptionUsecasesProxy.GET_INSTITUTION_SUBSCRIPTIONS_USECASE_PROXY,
      useFactory: (
        subscriptionRepository: SubscriptionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new GetInstitutionSubscriptionsUseCase(subscriptionRepository, logger)
        )
    },
    {
      inject: [SubscriptionRepository, LoggerService],
      provide: SubscriptionUsecasesProxy.CANCEL_SUBSCRIPTION_USECASE_PROXY,
      useFactory: (
        subscriptionRepository: SubscriptionRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new CancelSubscriptionUseCase(subscriptionRepository, logger)
        )
    }
  ],
  exports: [
    SubscriptionUsecasesProxy.GET_SUBSCRIPTION_USECASE_PROXY,
    SubscriptionUsecasesProxy.GET_SUBSCRIPTIONS_USECASE_PROXY,
    SubscriptionUsecasesProxy.GET_INSTITUTION_SUBSCRIPTIONS_USECASE_PROXY,
    SubscriptionUsecasesProxy.POST_SUBSCRIPTION_USECASE_PROXY,
    SubscriptionUsecasesProxy.CANCEL_SUBSCRIPTION_USECASE_PROXY
  ]
})
export class SubscriptionUsecasesProxyModule {}
