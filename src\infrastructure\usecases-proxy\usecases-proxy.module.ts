import { Modu<PERSON> } from '@nestjs/common';
import { AddressUseCasesProxyModule } from './address/address-usecases-proxy.module';
import { InstitutionUsecasesProxyModule } from './institution/institution-usecase-proxy.module';
import { PermissionUsecasesProxyModule } from './permission/permission-usecases-proxy.module';
import { RoleUsecasesProxyModule } from './role/role-usecases-proxy.module';
import { UserUsecasesProxyModule } from './user/user-usecases-proxy.module';

@Module({
  imports: [
    UserUsecasesProxyModule,
    PermissionUsecasesProxyModule,
    InstitutionUsecasesProxyModule,
    AddressUseCasesProxyModule,
    RoleUsecasesProxyModule
  ],
  exports: [
    UserUsecasesProxyModule,
    InstitutionUsecasesProxyModule,
    PermissionUsecasesProxyModule,
    AddressUseCasesProxyModule,
    RoleUsecasesProxyModule
  ]
})
export class UsecasesProxyModule {}
