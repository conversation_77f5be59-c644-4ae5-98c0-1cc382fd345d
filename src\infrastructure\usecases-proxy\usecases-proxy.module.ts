import { Module } from '@nestjs/common';
import { AddressUseCasesProxyModule } from './address/address-usecases-proxy.module';
import { AuthUsecasesProxyModule } from './auth/auth-usecases-proxy.module';
import { InstitutionUsecasesProxyModule } from './institution/institution-usecase-proxy.module';
import { PermissionUsecasesProxyModule } from './permission/permission-usecases-proxy.module';
import { PlanUsecasesProxyModule } from './plan/plan-usecases-proxy.module';
import { RolePermissionUsecasesProxyModule } from './role-permission/role-permission-usecases-proxy.module';
import { RoleUsecasesProxyModule } from './role/role-usecases-proxy.module';
import { SubscriptionUsecasesProxyModule } from './subscription/subscription-usecases-proxy.module';
import { UserRoleUsecasesProxyModule } from './user-role/user-role-usecases-proxy.module';
import { UserUsecasesProxyModule } from './user/user-usecases-proxy.module';

@Module({
  imports: [
    UserUsecasesProxyModule,
    AuthUsecasesProxyModule,
    PermissionUsecasesProxyModule,
    InstitutionUsecasesProxyModule,
    AddressUseCasesProxyModule,
    RoleUsecasesProxyModule,
    RolePermissionUsecasesProxyModule,
    UserRoleUsecasesProxyModule,
    PlanUsecasesProxyModule,
    SubscriptionUsecasesProxyModule
  ],
  exports: [
    UserUsecasesProxyModule,
    AuthUsecasesProxyModule,
    InstitutionUsecasesProxyModule,
    PermissionUsecasesProxyModule,
    AddressUseCasesProxyModule,
    RoleUsecasesProxyModule,
    RolePermissionUsecasesProxyModule,
    UserRoleUsecasesProxyModule,
    PlanUsecasesProxyModule,
    SubscriptionUsecasesProxyModule
  ]
})
export class UsecasesProxyModule {}
