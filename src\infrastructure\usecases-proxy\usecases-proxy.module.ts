import { Module } from '@nestjs/common';
import { AddressUseCasesProxyModule } from './address/address-usecases-proxy.module';
import { AttendanceUsecasesProxyModule } from './attendance/attendance-usecases-proxy.module';
import { AuthUsecasesProxyModule } from './auth/auth-usecases-proxy.module';
import { ClassUsecasesProxyModule } from './class/class-usecases-proxy.module';
import { CourseUsecasesProxyModule } from './course/course-usecases-proxy.module';
import { EnrollmentUsecasesProxyModule } from './enrollment/enrollment-usecases-proxy.module';
import { FinancialUsecasesProxyModule } from './financial/financial-usecases-proxy.module';
import { GradeUsecasesProxyModule } from './grade/grade-usecases-proxy.module';
import { InstitutionUsecasesProxyModule } from './institution/institution-usecase-proxy.module';
import { PermissionUsecasesProxyModule } from './permission/permission-usecases-proxy.module';
import { PlanUsecasesProxyModule } from './plan/plan-usecases-proxy.module';
import { RolePermissionUsecasesProxyModule } from './role-permission/role-permission-usecases-proxy.module';
import { RoleUsecasesProxyModule } from './role/role-usecases-proxy.module';
import { SubscriptionUsecasesProxyModule } from './subscription/subscription-usecases-proxy.module';
import { UserRoleUsecasesProxyModule } from './user-role/user-role-usecases-proxy.module';
import { UserUsecasesProxyModule } from './user/user-usecases-proxy.module';

@Module({
  imports: [
    UserUsecasesProxyModule,
    AuthUsecasesProxyModule,
    ClassUsecasesProxyModule,
    CourseUsecasesProxyModule,
    EnrollmentUsecasesProxyModule,
    AttendanceUsecasesProxyModule,
    GradeUsecasesProxyModule,
    FinancialUsecasesProxyModule,
    PermissionUsecasesProxyModule,
    InstitutionUsecasesProxyModule,
    AddressUseCasesProxyModule,
    RoleUsecasesProxyModule,
    RolePermissionUsecasesProxyModule,
    UserRoleUsecasesProxyModule,
    PlanUsecasesProxyModule,
    SubscriptionUsecasesProxyModule
  ],
  exports: [
    UserUsecasesProxyModule,
    AuthUsecasesProxyModule,
    ClassUsecasesProxyModule,
    CourseUsecasesProxyModule,
    EnrollmentUsecasesProxyModule,
    AttendanceUsecasesProxyModule,
    GradeUsecasesProxyModule,
    FinancialUsecasesProxyModule,
    InstitutionUsecasesProxyModule,
    PermissionUsecasesProxyModule,
    AddressUseCasesProxyModule,
    RoleUsecasesProxyModule,
    RolePermissionUsecasesProxyModule,
    UserRoleUsecasesProxyModule,
    PlanUsecasesProxyModule,
    SubscriptionUsecasesProxyModule
  ]
})
export class UsecasesProxyModule {}
