import { <PERSON>du<PERSON> } from '@nestjs/common';
import { CreateUserRoleUseCase } from '../../../usecases/user_role/create-user-role.usecase';
import { DeleteUserRoleUseCase } from '../../../usecases/user_role/delete-user-role.usecase';
import { GetUserRoleUseCase } from '../../../usecases/user_role/get-user-role.usecase';
import { GetUserRolesUseCase } from '../../../usecases/user_role/get-user-roles.usecase';
import { UpdateUserRoleUseCase } from '../../../usecases/user_role/update-user-role.usecase';
import { LoggerService } from '../../logger/logger.service';
import { UserRoleRepository } from '../../repositories/user_role-repository';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { TwoFactorAuthenticationService } from '../../services/2fa/two-factor-authentication.service';
import { EmailService } from '../../services/email/email.service';
import { JwtModule } from '../../services/jwt/jwt.module';
import { EmailModule } from '../../services/email/email.module';
import { UseCaseProxy } from '../usecases-proxy';
import { UserRoleUsecasesProxy } from './user-role-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule, JwtModule, EmailModule],
  providers: [
    TwoFactorAuthenticationService,
    LoggerService,
    EmailService,
    {
      inject: [UserRoleRepository, LoggerService],
      provide: UserRoleUsecasesProxy.POST_USER_ROLE_USECASE_PROXY,
      useFactory: (
        userRoleRepository: UserRoleRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(new CreateUserRoleUseCase(userRoleRepository, logger))
    },
    {
      inject: [UserRoleRepository, LoggerService],
      provide: UserRoleUsecasesProxy.GET_USER_ROLES_USECASE_PROXY,
      useFactory: (
        userRoleRepository: UserRoleRepository,
        logger: LoggerService
      ) => new UseCaseProxy(new GetUserRolesUseCase(userRoleRepository, logger))
    },
    {
      inject: [UserRoleRepository, LoggerService],
      provide: UserRoleUsecasesProxy.GET_USER_ROLE_USECASE_PROXY,
      useFactory: (
        userRoleRepository: UserRoleRepository,
        logger: LoggerService
      ) => new UseCaseProxy(new GetUserRoleUseCase(userRoleRepository, logger))
    },
    {
      inject: [UserRoleRepository, LoggerService],
      provide: UserRoleUsecasesProxy.DELETE_USER_ROLE_USECASE_PROXY,
      useFactory: (
        userRoleRepository: UserRoleRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(new DeleteUserRoleUseCase(userRoleRepository, logger))
    },
    {
      inject: [UserRoleRepository, LoggerService],
      provide: UserRoleUsecasesProxy.UPDATE_USER_ROLE_USECASE_PROXY,
      useFactory: (
        userRoleRepository: UserRoleRepository,
        logger: LoggerService
      ) =>
        new UseCaseProxy(new UpdateUserRoleUseCase(userRoleRepository, logger))
    }
  ],
  exports: [
    UserRoleUsecasesProxy.GET_USER_ROLE_USECASE_PROXY,
    UserRoleUsecasesProxy.GET_USER_ROLES_USECASE_PROXY,
    UserRoleUsecasesProxy.POST_USER_ROLE_USECASE_PROXY,
    UserRoleUsecasesProxy.DELETE_USER_ROLE_USECASE_PROXY,
    UserRoleUsecasesProxy.UPDATE_USER_ROLE_USECASE_PROXY
  ]
})
export class UserRoleUsecasesProxyModule {}
