import { Module } from '@nestjs/common';
import { CreateUserUseCase } from '../../../usecases/user/create-user.usecase';
import { DeleteUserUseCase } from '../../../usecases/user/delete-user.usecase';
import { GetUserUseCase } from '../../../usecases/user/get-user.usecase';
import { GetUsersUseCase } from '../../../usecases/user/get-users.usecase';
import { UpdateUserUseCase } from '../../../usecases/user/update-user.usecase';
import { LoggerService } from '../../logger/logger.service';
import { RepositoriesModule } from '../../repositories/repositories.module';
import { UserRepository } from '../../repositories/user-repository';
import { TwoFactorAuthenticationService } from '../../services/2fa/two-factor-authentication.service';
import { EmailModule } from '../../services/email/email.module';
import { EmailService } from '../../services/email/email.service';
import { JwtModule } from '../../services/jwt/jwt.module';
import { JwtTokenService } from '../../services/jwt/jwt.service';
import { UseCaseProxy } from '../usecases-proxy';
import { UserUsecasesProxy } from './user-usecases-proxy.constants';

@Module({
  imports: [RepositoriesModule, JwtModule, EmailModule],
  providers: [
    TwoFactorAuthenticationService,
    LoggerService,
    EmailService,
    {
      inject: [
        UserRepository,
        TwoFactorAuthenticationService,
        JwtTokenService,
        LoggerService,
        EmailService
      ],
      provide: UserUsecasesProxy.POST_USER_USECASE_PROXY,
      useFactory: (
        userRepository: UserRepository,
        twoFactorAuthenticationService: TwoFactorAuthenticationService,
        jwtTokenService: JwtTokenService,
        logger: LoggerService,
        emailService: EmailService
      ) =>
        new UseCaseProxy(
          new CreateUserUseCase(
            userRepository,
            twoFactorAuthenticationService,
            jwtTokenService,
            logger,
            emailService
          )
        )
    },
    {
      inject: [UserRepository, TwoFactorAuthenticationService, LoggerService],
      provide: UserUsecasesProxy.DELETE_USER_USECASE_PROXY,
      useFactory: (
        userRepository: UserRepository,
        twoFactorAuthenticationService: TwoFactorAuthenticationService,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new DeleteUserUseCase(
            userRepository,
            logger,
            twoFactorAuthenticationService
          )
        )
    },
    {
      inject: [UserRepository, TwoFactorAuthenticationService, LoggerService],
      provide: UserUsecasesProxy.UPDATE_USER_USECASE_PROXY,
      useFactory: (
        userRepository: UserRepository,
        twoFactorAuthenticationService: TwoFactorAuthenticationService,
        logger: LoggerService
      ) =>
        new UseCaseProxy(
          new UpdateUserUseCase(
            userRepository,
            twoFactorAuthenticationService,
            logger
          )
        )
    },
    {
      inject: [UserRepository, LoggerService],
      provide: UserUsecasesProxy.GET_USER_USECASE_PROXY,
      useFactory: (userRepository: UserRepository, logger: LoggerService) =>
        new UseCaseProxy(new GetUserUseCase(userRepository, logger))
    },
    {
      inject: [UserRepository, LoggerService],
      provide: UserUsecasesProxy.GET_USERS_USECASE_PROXY,
      useFactory: (userRepository: UserRepository, logger: LoggerService) =>
        new UseCaseProxy(new GetUsersUseCase(userRepository, logger))
    }
  ],
  exports: [
    UserUsecasesProxy.POST_USER_USECASE_PROXY,
    UserUsecasesProxy.DELETE_USER_USECASE_PROXY,
    UserUsecasesProxy.UPDATE_USER_USECASE_PROXY,
    UserUsecasesProxy.GET_USER_USECASE_PROXY,
    UserUsecasesProxy.GET_USERS_USECASE_PROXY
  ]
})
export class UserUsecasesProxyModule {}
