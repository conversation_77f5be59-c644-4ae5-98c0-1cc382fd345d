import { Injectable } from '@nestjs/common';
import { AddressEntity } from '../../domain/entities/address.entity';
import { CreateAddressDto } from '../../infrastructure/controllers/addresss/address.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { AddressRepository } from '../../infrastructure/repositories/address-repository';

@Injectable()
export class CreateAddressUseCase {
  constructor(
    private readonly addressRepository: AddressRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'CREATE_ADDRESS_USE_CASE';

  async execute(address: CreateAddressDto): Promise<AddressEntity> {
    this.logger.log(this.logContextName, 'Iniciando criação de Endereço');

    const newAddress = await this.addressRepository.insert(address);
    return newAddress;
  }
}
