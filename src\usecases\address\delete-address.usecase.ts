import { Injectable } from '@nestjs/common';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { AddressRepository } from '../../infrastructure/repositories/address-repository';

@Injectable()
export class DeleteAddressUseCase {
  constructor(
    private readonly addressRepository: AddressRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'DELETE_ADDRESS_USE_CASE';

  async execute(id: string): Promise<void> {
    this.logger.log(this.logContextName, 'Deletando Endereço por Id');
    await this.addressRepository.delete(id);
  }
}
