import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { AddressEntity } from '../../domain/entities/address.entity';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { AddressRepository } from '../../infrastructure/repositories/address-repository';

@Injectable()
export class GetAddressUseCase {
  constructor(
    private readonly addressRepository: AddressRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_ADDRESS_USE_CASE';

  async execute(id: string): Promise<AddressEntity> {
    this.logger.log(this.logContextName, 'Buscando Endereço por Id');
    const address = await this.addressRepository.findById(id);

    if (!address)
      throw new HttpException('Endereço não encontrado', HttpStatus.NOT_FOUND);

    return address;
  }
}
