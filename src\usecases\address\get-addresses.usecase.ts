import { Injectable } from '@nestjs/common';
import { AddressEntity } from '../../domain/entities/address.entity';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { AddressRepository } from '../../infrastructure/repositories/address-repository';

@Injectable()
export class GetAddressesUseCase {
  constructor(
    private readonly addressRepository: AddressRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_ADDRESSES_USE_CASE';

  async execute(): Promise<AddressEntity[]> {
    this.logger.log(this.logContextName, 'Buscando lista de endereços');
    const addresses = await this.addressRepository.findAll();
    return addresses;
  }
}
