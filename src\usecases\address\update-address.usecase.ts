import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { AddressEntity } from '../../domain/entities/address.entity';
import { UpdateAddressDto } from '../../infrastructure/controllers/addresss/address.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { AddressRepository } from '../../infrastructure/repositories/address-repository';

@Injectable()
export class UpdateAddressUseCase {
  constructor(
    private readonly addressRepository: AddressRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'UPDATE_ADDRESS_USE_CASE';

  async execute(
    id: string,
    newAddress: UpdateAddressDto
  ): Promise<AddressEntity> {
    this.logger.log(this.logContextName, 'Iniciando Atualização de Endereço');
    const address = await this.addressRepository.findById(id);

    if (!address) {
      throw new HttpException('Endereço não encontrado', HttpStatus.NOT_FOUND);
    }

    if (id !== newAddress.id)
      throw new HttpException(
        'Não autorizado, dados para update incosistentes.',
        HttpStatus.UNAUTHORIZED
      );

    const updatedAddress = await this.addressRepository.update(id, {
      ...address,
      ...newAddress
    });

    return updatedAddress;
  }
}
