import { AttendanceEntity } from '../../domain/entities/attendance.entity';
import { AttendanceRepository } from '../../domain/abstractions/attendance-repository.abstraction';
import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';

export class CreateAttendanceUseCase {
  constructor(
    private readonly attendanceRepository: AttendanceRepository,
    private readonly enrollmentRepository: EnrollmentRepository
  ) {}

  async execute(attendanceData: {
    enrollment_id: string;
    class_date: Date;
    status?: 'present' | 'absent' | 'late' | 'excused';
    notes?: string;
    recorded_by: string;
    created_by: string;
  }): Promise<AttendanceEntity> {
    // Verificar se a matrícula existe e está ativa
    const enrollment = await this.enrollmentRepository.findById(
      attendanceData.enrollment_id
    );

    if (enrollment.status !== 'approved') {
      throw new Error('Cannot record attendance for non-approved enrollment');
    }

    // Verificar se já existe registro de presença para esta matrícula nesta data
    const existingAttendance =
      await this.attendanceRepository.findByEnrollmentAndDate(
        attendanceData.enrollment_id,
        attendanceData.class_date
      );

    if (existingAttendance) {
      throw new Error(
        'Attendance already recorded for this enrollment on this date'
      );
    }

    // Validar data da aula (não pode ser futura)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const classDate = new Date(attendanceData.class_date);
    classDate.setHours(0, 0, 0, 0);

    if (classDate > today) {
      throw new Error('Cannot record attendance for future dates');
    }

    const attendanceToCreate = {
      enrollment_id: attendanceData.enrollment_id,
      class_date: attendanceData.class_date,
      status: attendanceData.status || 'present',
      notes: attendanceData.notes,
      recorded_by: attendanceData.recorded_by,
      created_by: attendanceData.created_by,
      created_at: new Date()
    };

    return await this.attendanceRepository.create(attendanceToCreate);
  }
}
