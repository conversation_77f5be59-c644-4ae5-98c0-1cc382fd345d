import { AttendanceRepository } from '../../domain/abstractions/attendance-repository.abstraction';

export class DeleteAttendanceUseCase {
  constructor(private readonly attendanceRepository: AttendanceRepository) {}

  async execute(id: string): Promise<void> {
    // Verificar se o registro de presença existe
    await this.attendanceRepository.findById(id);

    // Deletar o registro
    await this.attendanceRepository.delete(id);
  }
}
