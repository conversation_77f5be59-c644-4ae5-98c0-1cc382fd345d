import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UserEntity } from '../../domain/entities/user.entity';
import { LoginDto } from '../../infrastructure/controllers/auth/auth.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRepository } from '../../infrastructure/repositories/user-repository';
import { BcryptService } from '../../infrastructure/services/bcrypt/bcrypt.service';
import { JwtTokenService } from '../../infrastructure/services/jwt/jwt.service';

export interface LoginResponse {
  user: UserEntity;
  accessToken: string;
  refreshToken: string;
}

@Injectable()
export class LoginUseCase {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly bcryptService: BcryptService,
    private readonly jwtTokenService: JwtTokenService,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'LOGIN_USE_CASE';

  async execute(loginData: LoginDto): Promise<LoginResponse> {
    this.logger.log(this.logContextName, 'Iniciando processo de login');

    // Buscar usuário por email
    const user = await this.userRepository.findByEmail(loginData.email);
    if (!user) {
      this.logger.warn(this.logContextName, 'Usuário não encontrado');
      throw new HttpException(
        'Email ou senha inválidos',
        HttpStatus.UNAUTHORIZED
      );
    }

    // Verificar se o usuário está ativo
    if (!user.actived) {
      this.logger.warn(this.logContextName, 'Usuário inativo');
      throw new HttpException(
        'Usuário inativo. Entre em contato com o administrador.',
        HttpStatus.UNAUTHORIZED
      );
    }

    // Verificar se o usuário tem senha definida
    if (!user.password_hash) {
      this.logger.warn(this.logContextName, 'Usuário sem senha definida');
      throw new HttpException(
        'Usuário ainda não definiu uma senha. Use o link de primeiro acesso.',
        HttpStatus.UNAUTHORIZED
      );
    }

    // Verificar senha
    const isPasswordValid = await this.bcryptService.compare(
      loginData.password,
      user.password_hash
    );

    if (!isPasswordValid) {
      this.logger.warn(this.logContextName, 'Senha inválida');
      throw new HttpException(
        'Email ou senha inválidos',
        HttpStatus.UNAUTHORIZED
      );
    }

    // Gerar tokens
    const accessTokenPayload = {
      sub: user.id,
      email: user.email,
      name: user.name,
      institution_id: user.institution_id
    };

    const refreshTokenPayload = {
      sub: user.id,
      email: user.email,
      type: 'refresh' as const
    };

    const accessToken = this.jwtTokenService.createToken(
      accessTokenPayload,
      process.env.JWT_SECRET || 'secret',
      process.env.JWT_EXPIRATION_TIME || '15m'
    );

    const refreshToken = this.jwtTokenService.createToken(
      refreshTokenPayload,
      process.env.JWT_REFRESH_SECRET || 'refresh-secret',
      process.env.JWT_REFRESH_EXPIRATION_TIME || '7d'
    );

    this.logger.log(this.logContextName, 'Login realizado com sucesso');

    return {
      user,
      accessToken,
      refreshToken
    };
  }
}
