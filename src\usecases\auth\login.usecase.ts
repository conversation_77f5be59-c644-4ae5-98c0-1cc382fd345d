import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InstitutionEntity } from '../../domain/entities/institution.entity';
import { PlanEntity } from '../../domain/entities/plan.entity';
import { SubscriptionEntity } from '../../domain/entities/subscription.entity';
import { UserEntity } from '../../domain/entities/user.entity';
import { LoginDto } from '../../infrastructure/controllers/auth/auth.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { InstitutionRepository } from '../../infrastructure/repositories/institution-repository';
import { PlanRepository } from '../../infrastructure/repositories/plan-repository';
import { SubscriptionRepository } from '../../infrastructure/repositories/subscription-repository';
import { UserRepository } from '../../infrastructure/repositories/user-repository';
import { BcryptService } from '../../infrastructure/services/bcrypt/bcrypt.service';
import { JwtTokenService } from '../../infrastructure/services/jwt/jwt.service';

export interface LoginResponse {
  user: UserEntity;
  institution?: InstitutionEntity;
  subscription?: SubscriptionEntity;
  plan?: PlanEntity;
  accessToken: string;
  refreshToken: string;
}

@Injectable()
export class LoginUseCase {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly institutionRepository: InstitutionRepository,
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly planRepository: PlanRepository,
    private readonly bcryptService: BcryptService,
    private readonly jwtTokenService: JwtTokenService,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'LOGIN_USE_CASE';

  async execute(loginData: LoginDto): Promise<LoginResponse> {
    this.logger.log(this.logContextName, 'Iniciando processo de login');

    // Buscar usuário por email
    const user = await this.userRepository.findByEmail(loginData.email);
    if (!user) {
      this.logger.warn(this.logContextName, 'Usuário não encontrado');
      throw new HttpException(
        'Email ou senha inválidos',
        HttpStatus.UNAUTHORIZED
      );
    }

    // Verificar se o usuário está ativo
    if (!user.actived) {
      this.logger.warn(this.logContextName, 'Usuário inativo');
      throw new HttpException(
        'Usuário inativo. Entre em contato com o administrador.',
        HttpStatus.UNAUTHORIZED
      );
    }

    // Verificar se o usuário tem senha definida
    if (!user.password_hash) {
      this.logger.warn(this.logContextName, 'Usuário sem senha definida');
      throw new HttpException(
        'Usuário ainda não definiu uma senha. Use o link de primeiro acesso.',
        HttpStatus.UNAUTHORIZED
      );
    }

    // Verificar senha
    const isPasswordValid = await this.bcryptService.compare(
      loginData.password,
      user.password_hash
    );

    if (!isPasswordValid) {
      this.logger.warn(this.logContextName, 'Senha inválida');
      throw new HttpException(
        'Email ou senha inválidos',
        HttpStatus.UNAUTHORIZED
      );
    }

    // Gerar tokens
    const accessTokenPayload = {
      sub: user.id,
      email: user.email,
      name: user.name,
      institution_id: user.institution_id
    };

    const refreshTokenPayload = {
      sub: user.id,
      email: user.email,
      type: 'refresh' as const
    };

    const accessToken = this.jwtTokenService.createToken(
      accessTokenPayload,
      process.env.JWT_SECRET || 'secret',
      process.env.JWT_EXPIRATION_TIME || '15m'
    );

    const refreshToken = this.jwtTokenService.createToken(
      refreshTokenPayload,
      process.env.JWT_REFRESH_SECRET || 'refresh-secret',
      process.env.JWT_REFRESH_EXPIRATION_TIME || '7d'
    );

    // Buscar dados da instituição se o usuário pertence a uma
    let institution: InstitutionEntity | undefined;
    let subscription: SubscriptionEntity | undefined;
    let plan: PlanEntity | undefined;

    if (user.institution_id) {
      try {
        // Buscar instituição
        const foundInstitution = await this.institutionRepository.findById(
          user.institution_id
        );
        institution = foundInstitution || undefined;

        if (institution) {
          // Buscar subscription ativa da instituição
          const foundSubscription =
            await this.subscriptionRepository.findActiveByInstitutionId(
              institution.id
            );
          subscription = foundSubscription || undefined;

          if (subscription) {
            // Buscar dados do plano
            const foundPlan = await this.planRepository.findById(
              subscription.plan_id
            );
            plan = foundPlan || undefined;
          }
        }
      } catch (error) {
        this.logger.warn(
          this.logContextName,
          `Erro ao buscar dados da instituição/subscription: ${error}`
        );
        // Não bloquear o login por erro na busca de dados complementares
      }
    }

    this.logger.log(this.logContextName, 'Login realizado com sucesso');

    return {
      user,
      institution,
      subscription,
      plan,
      accessToken,
      refreshToken
    };
  }
}
