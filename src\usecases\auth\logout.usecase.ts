import { Injectable } from '@nestjs/common';
import { LoggerService } from '../../infrastructure/logger/logger.service';

@Injectable()
export class LogoutUseCase {
  constructor(private readonly logger: LoggerService) {}
  private readonly logContextName: string = 'LOGOUT_USE_CASE';

  async execute(userId: string): Promise<void> {
    this.logger.log(
      this.logContextName,
      `Iniciando logout do usuário: ${userId}`
    );

    // Aqui podemos implementar lógicas adicionais como:
    // - Invalidar tokens em uma blacklist (Redis)
    // - Registrar logs de auditoria
    // - Limpar sessões ativas

    this.logger.log(this.logContextName, 'Logout realizado com sucesso');
  }
}
