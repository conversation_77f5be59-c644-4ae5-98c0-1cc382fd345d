import { Injectable } from '@nestjs/common';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { TokenBlacklistService } from '../../infrastructure/services/token-blacklist/token-blacklist.service';

export interface LogoutRequest {
  userId: string;
  accessToken?: string;
  refreshToken?: string;
}

@Injectable()
export class LogoutUseCase {
  constructor(
    private readonly logger: LoggerService,
    private readonly tokenBlacklistService: TokenBlacklistService
  ) {}
  private readonly logContextName: string = 'LOGOUT_USE_CASE';

  async execute(logoutData: LogoutRequest): Promise<void> {
    this.logger.log(
      this.logContextName,
      `Iniciando logout do usuário: ${logoutData.userId}`
    );

    try {
      // Invalidar tokens na blacklist
      const tokensToBlacklist: string[] = [];

      if (logoutData.accessToken) {
        tokensToBlacklist.push(logoutData.accessToken);
      }

      if (logoutData.refreshToken) {
        tokensToBlacklist.push(logoutData.refreshToken);
      }

      if (tokensToBlacklist.length > 0) {
        await this.tokenBlacklistService.addMultipleToBlacklist(
          tokensToBlacklist
        );
        this.logger.log(
          this.logContextName,
          `${tokensToBlacklist.length} tokens invalidados na blacklist`
        );
      }

      // Registrar logs de auditoria
      this.logger.log(
        this.logContextName,
        `Logout realizado com sucesso para usuário: ${logoutData.userId}`
      );
    } catch (error) {
      this.logger.error(
        this.logContextName,
        `Erro durante logout: ${error.message}`
      );
      throw error;
    }
  }
}
