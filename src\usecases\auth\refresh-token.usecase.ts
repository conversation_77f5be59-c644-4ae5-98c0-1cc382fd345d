import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UserEntity } from '../../domain/entities/user.entity';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRepository } from '../../infrastructure/repositories/user-repository';
import { JwtTokenService } from '../../infrastructure/services/jwt/jwt.service';
import { TokenBlacklistService } from '../../infrastructure/services/token-blacklist/token-blacklist.service';

export interface RefreshTokenResponse {
  user: UserEntity;
  accessToken: string;
  refreshToken: string;
}

@Injectable()
export class RefreshTokenUseCase {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly jwtTokenService: JwtTokenService,
    private readonly logger: LoggerService,
    private readonly tokenBlacklistService: TokenBlacklistService
  ) {}
  private readonly logContextName: string = 'REFRESH_TOKEN_USE_CASE';

  async execute(refreshToken: string): Promise<RefreshTokenResponse> {
    this.logger.log(this.logContextName, 'Iniciando refresh do token');

    try {
      // Verificar se o refresh token está na blacklist
      const isBlacklisted = await this.tokenBlacklistService.isBlacklisted(refreshToken);
      if (isBlacklisted) {
        this.logger.warn(
          this.logContextName,
          'Tentativa de uso de refresh token na blacklist'
        );
        throw new HttpException(
          'Refresh token has been invalidated',
          HttpStatus.UNAUTHORIZED
        );
      }

      // Verificar e decodificar o refresh token
      const payload = await this.jwtTokenService.checkToken(refreshToken);

      if (payload.type !== 'refresh') {
        throw new HttpException('Token inválido', HttpStatus.UNAUTHORIZED);
      }

      // Buscar usuário
      const user = await this.userRepository.findById(payload.sub);
      if (!user) {
        this.logger.warn(this.logContextName, 'Usuário não encontrado');
        throw new HttpException(
          'Usuário não encontrado',
          HttpStatus.UNAUTHORIZED
        );
      }

      // Verificar se o usuário ainda está ativo
      if (!user.actived) {
        this.logger.warn(this.logContextName, 'Usuário inativo');
        throw new HttpException('Usuário inativo', HttpStatus.UNAUTHORIZED);
      }

      // Gerar novos tokens
      const accessTokenPayload = {
        sub: user.id,
        email: user.email,
        name: user.name,
        institution_id: user.institution_id
      };

      const refreshTokenPayload = {
        sub: user.id,
        email: user.email,
        type: 'refresh' as const
      };

      const newAccessToken = this.jwtTokenService.createToken(
        accessTokenPayload,
        process.env.JWT_SECRET || 'secret',
        process.env.JWT_EXPIRATION_TIME || '15m'
      );

      const newRefreshToken = this.jwtTokenService.createToken(
        refreshTokenPayload,
        process.env.JWT_REFRESH_SECRET || 'refresh-secret',
        process.env.JWT_REFRESH_EXPIRATION_TIME || '7d'
      );

      // Invalidar o refresh token antigo na blacklist
      await this.tokenBlacklistService.addToBlacklist(refreshToken);
      this.logger.log(
        this.logContextName,
        'Refresh token antigo invalidado na blacklist'
      );

      this.logger.log(this.logContextName, 'Token renovado com sucesso');

      return {
        user,
        accessToken: newAccessToken,
        refreshToken: newRefreshToken
      };
    } catch (error) {
      this.logger.error(this.logContextName, 'Erro ao renovar token', error);
      throw new HttpException(
        'Token inválido ou expirado',
        HttpStatus.UNAUTHORIZED
      );
    }
  }
}
