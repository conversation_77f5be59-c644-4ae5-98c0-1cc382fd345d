import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UserEntity } from '../../domain/entities/user.entity';
import { SetPasswordDto } from '../../infrastructure/controllers/auth/auth.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRepository } from '../../infrastructure/repositories/user-repository';
import { BcryptService } from '../../infrastructure/services/bcrypt/bcrypt.service';
import { JwtTokenService } from '../../infrastructure/services/jwt/jwt.service';

@Injectable()
export class SetPasswordUseCase {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly bcryptService: BcryptService,
    private readonly jwtTokenService: JwtTokenService,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'SET_PASSWORD_USE_CASE';

  async execute(setPasswordData: SetPasswordDto): Promise<UserEntity> {
    this.logger.log(this.logContextName, 'Iniciando definição de senha');

    try {
      // Verificar e decodificar o token de primeiro acesso
      const payload = await this.jwtTokenService.checkToken(
        setPasswordData.token
      );

      // Buscar usuário
      const user = await this.userRepository.findById(payload.sub);
      if (!user) {
        this.logger.warn(this.logContextName, 'Usuário não encontrado');
        throw new HttpException(
          'Token inválido ou usuário não encontrado',
          HttpStatus.UNAUTHORIZED
        );
      }

      // Verificar se o usuário ainda não tem senha definida
      if (user.password_hash) {
        this.logger.warn(
          this.logContextName,
          'Usuário já possui senha definida'
        );
        throw new HttpException(
          'Usuário já possui senha definida',
          HttpStatus.BAD_REQUEST
        );
      }

      // Criptografar a nova senha
      const hashedPassword = await this.bcryptService.hash(
        setPasswordData.password
      );

      // Atualizar usuário com a nova senha
      const updatedUser = await this.userRepository.update(user.id, {
        ...user,
        password_hash: hashedPassword,
        is_email_verified: true, // Marcar email como verificado
        updated_by: user.id,
        updated_at: new Date()
      });

      this.logger.log(this.logContextName, 'Senha definida com sucesso');

      return updatedUser;
    } catch (error) {
      this.logger.error(this.logContextName, 'Erro ao definir senha', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Token inválido ou expirado',
        HttpStatus.UNAUTHORIZED
      );
    }
  }
}
