import { ClassRepository } from '../../domain/abstractions/class-repository.abstraction';
import { ClassEntity, ClassSchedule } from '../../domain/entities/class.entity';

export class CreateClassUseCase {
  constructor(private readonly classRepository: ClassRepository) {}

  async execute(classData: {
    course_id: string;
    institution_id: string;
    name: string;
    description?: string;
    max_students: number;
    status?: 'open' | 'closed' | 'in_progress' | 'finished';
    start_date?: Date;
    end_date?: Date;
    schedule?: ClassSchedule[];
    created_by: string;
  }): Promise<ClassEntity> {
    // Validar se max_students é positivo
    if (classData.max_students <= 0) {
      throw new Error('Max students must be greater than 0');
    }

    // Validar datas se fornecidas
    if (classData.start_date && classData.end_date) {
      if (classData.start_date >= classData.end_date) {
        throw new Error('Start date must be before end date');
      }
    }

    // Validar schedule se fornecido
    if (classData.schedule) {
      for (const scheduleItem of classData.schedule) {
        if (scheduleItem.day_of_week < 0 || scheduleItem.day_of_week > 6) {
          throw new Error('Day of week must be between 0 and 6');
        }
        if (scheduleItem.start_time >= scheduleItem.end_time) {
          throw new Error('Start time must be before end time');
        }
      }
    }

    const classToCreate = {
      course_id: classData.course_id,
      institution_id: classData.institution_id,
      name: classData.name,
      description: classData.description,
      max_students: classData.max_students,
      current_students: 0,
      status: classData.status || 'open',
      start_date: classData.start_date,
      end_date: classData.end_date,
      schedule: classData.schedule,
      created_by: classData.created_by,
      created_at: new Date()
    };

    return await this.classRepository.create(classToCreate);
  }
}
