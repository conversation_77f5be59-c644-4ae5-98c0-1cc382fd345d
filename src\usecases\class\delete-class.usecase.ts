import { ClassRepository } from '../../domain/abstractions/class-repository.abstraction';

export class DeleteClassUseCase {
  constructor(private readonly classRepository: ClassRepository) {}

  async execute(id: string): Promise<void> {
    // Verificar se a classe existe
    const existingClass = await this.classRepository.findById(id);

    // Verificar se há estudantes matriculados
    if (existingClass.current_students > 0) {
      throw new Error('Cannot delete class with enrolled students');
    }

    // Deletar a classe
    await this.classRepository.delete(id);
  }
}
