import { ClassEntity, ClassSchedule } from '../../domain/entities/class.entity';
import { ClassRepository } from '../../domain/abstractions/class-repository.abstraction';

export class UpdateClassUseCase {
  constructor(private readonly classRepository: ClassRepository) {}

  async execute(
    id: string,
    classData: {
      name?: string;
      description?: string;
      max_students?: number;
      status?: 'open' | 'closed' | 'in_progress' | 'finished';
      start_date?: Date;
      end_date?: Date;
      schedule?: ClassSchedule[];
      updated_by: string;
    }
  ): Promise<ClassEntity> {
    // Verificar se a classe existe
    const existingClass = await this.classRepository.findById(id);

    // Validar max_students se fornecido
    if (classData.max_students !== undefined) {
      if (classData.max_students <= 0) {
        throw new Error('Max students must be greater than 0');
      }
      // Não permitir reduzir max_students abaixo do current_students
      if (classData.max_students < existingClass.current_students) {
        throw new Error(
          'Cannot reduce max students below current enrolled students'
        );
      }
    }

    // Validar datas se fornecidas
    if (classData.start_date && classData.end_date) {
      if (classData.start_date >= classData.end_date) {
        throw new Error('Start date must be before end date');
      }
    }

    // Validar schedule se fornecido
    if (classData.schedule) {
      for (const scheduleItem of classData.schedule) {
        if (scheduleItem.day_of_week < 0 || scheduleItem.day_of_week > 6) {
          throw new Error('Day of week must be between 0 and 6');
        }
        if (scheduleItem.start_time >= scheduleItem.end_time) {
          throw new Error('Start time must be before end time');
        }
      }
    }

    const updateData = {
      ...classData,
      updated_at: new Date()
    };

    return await this.classRepository.update(id, updateData);
  }
}
