import { CourseEntity } from 'src/domain/entities/course.entity';
import { CourseRepository } from '../../domain/abstractions/course-repository.abstraction';

export class CreateCourseUseCase {
  constructor(private readonly courseRepository: CourseRepository) {}

  async execute(courseData: {
    institution_id: string;
    name: string;
    description?: string;
    duration_months: number;
    price: number;
    max_students: number;
    status?: 'active' | 'inactive' | 'completed' | 'cancelled';
    start_date?: Date;
    end_date?: Date;
    created_by: string;
  }): Promise<CourseEntity> {
    // Verificar se já existe um curso com o mesmo nome na instituição
    try {
      await this.courseRepository.findByName(courseData.name);
      throw new Error('Course name already exists in this institution');
    } catch (error) {
      if (error.message !== 'Course not found') {
        throw error;
      }
    }

    const course = {
      institution_id: courseData.institution_id,
      name: courseData.name,
      description: courseData.description,
      duration_months: courseData.duration_months,
      price: courseData.price,
      max_students: courseData.max_students,
      status: courseData.status || 'active',
      start_date: courseData.start_date,
      end_date: courseData.end_date,
      created_by: courseData.created_by,
      created_at: new Date()
    };

    return await this.courseRepository.create(course);
  }
}
