import { Course } from '../../domain/entities/course.entity';
import { CourseRepository } from '../../domain/abstractions/course-repository.abstraction';

export class CreateCourseUseCase {
  constructor(private readonly courseRepository: CourseRepository) {}

  async execute(courseData: {
    name: string;
    description?: string;
    code: string;
    credits: number;
    duration_hours: number;
    status?: 'active' | 'inactive' | 'archived';
    institution_id: string;
    created_by: string;
  }): Promise<Course> {
    // Verificar se o código do curso já existe
    try {
      await this.courseRepository.findByCode(courseData.code);
      throw new Error('Course code already exists');
    } catch (error) {
      if (error.message !== 'Course not found') {
        throw error;
      }
    }

    const course: Course = {
      id: '', // Será gerado pelo banco
      name: courseData.name,
      description: courseData.description || '',
      code: courseData.code,
      credits: courseData.credits,
      duration_hours: courseData.duration_hours,
      status: courseData.status || 'active',
      institution_id: courseData.institution_id,
      created_by: courseData.created_by,
      created_at: new Date()
    };

    return await this.courseRepository.create(course);
  }
}
