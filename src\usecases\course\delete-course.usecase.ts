import { CourseRepository } from '../../domain/abstractions/course-repository.abstraction';

export class DeleteCourseUseCase {
  constructor(private readonly courseRepository: CourseRepository) {}

  async execute(id: string): Promise<void> {
    // Verificar se o curso existe antes de deletar
    await this.courseRepository.findById(id);
    
    // Deletar o curso
    await this.courseRepository.delete(id);
  }
}
