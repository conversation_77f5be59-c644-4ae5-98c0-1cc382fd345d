import { Course } from '../../domain/entities/course.entity';
import { CourseRepository } from '../../domain/abstractions/course-repository.abstraction';

export class GetCoursesByInstitutionUseCase {
  constructor(private readonly courseRepository: CourseRepository) {}

  async execute(institutionId: string): Promise<Course[]> {
    return await this.courseRepository.findByInstitutionId(institutionId);
  }
}
