import { CourseRepository } from '../../domain/abstractions/course-repository.abstraction';
import { CourseEntity } from '../../domain/entities/course.entity';

export class GetCoursesByInstitutionUseCase {
  constructor(private readonly courseRepository: CourseRepository) {}

  async execute(institutionId: string): Promise<CourseEntity[]> {
    return await this.courseRepository.findByInstitutionId(institutionId);
  }
}
