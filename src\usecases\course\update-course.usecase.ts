import { CourseRepository } from '../../domain/abstractions/course-repository.abstraction';
import { CourseEntity } from '../../domain/entities/course.entity';

export class UpdateCourseUseCase {
  constructor(private readonly courseRepository: CourseRepository) {}

  async execute(
    id: string,
    courseData: {
      name?: string;
      description?: string;
      code?: string;
      credits?: number;
      duration_hours?: number;
      status?: 'active' | 'inactive' | 'archived';
      updated_by: string;
    }
  ): Promise<CourseEntity> {
    // Verificar se o curso existe
    await this.courseRepository.findById(id);

    // Se o código está sendo alterado, verificar se não existe outro curso com o mesmo código
    if (courseData.code) {
      try {
        const existingCourse = await this.courseRepository.findByCode(
          courseData.code
        );
        if (existingCourse.id !== id) {
          throw new Error('Course code already exists');
        }
      } catch (error) {
        if (error.message !== 'Course not found') {
          throw error;
        }
      }
    }

    const updateData = {
      ...courseData,
      updated_at: new Date()
    };

    return await this.courseRepository.update(id, updateData);
  }
}
