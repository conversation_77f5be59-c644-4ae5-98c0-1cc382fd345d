import { CourseRepository } from '../../domain/abstractions/course-repository.abstraction';
import { CourseEntity } from '../../domain/entities/course.entity';

export class UpdateCourseUseCase {
  constructor(private readonly courseRepository: CourseRepository) {}

  async execute(
    id: string,
    courseData: {
      name?: string;
      description?: string;
      duration_months?: number;
      price?: number;
      max_students?: number;
      status?: 'active' | 'inactive' | 'completed' | 'cancelled';
      start_date?: Date;
      end_date?: Date;
      updated_by: string;
    }
  ): Promise<CourseEntity> {
    // Verificar se o curso existe
    await this.courseRepository.findById(id);

    // Se o nome está sendo alterado, verificar se não existe outro curso com o mesmo nome
    if (courseData.name) {
      try {
        const existingCourse = await this.courseRepository.findByName(
          courseData.name
        );
        if (existingCourse.id !== id) {
          throw new Error('Course name already exists');
        }
      } catch (error) {
        if (error.message !== 'Course not found') {
          throw error;
        }
      }
    }

    const updateData = {
      ...courseData,
      updated_at: new Date()
    };

    return await this.courseRepository.update(id, updateData);
  }
}
