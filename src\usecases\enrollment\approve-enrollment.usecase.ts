import { EnrollmentEntity } from '../../domain/entities/enrollment.entity';
import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';
import { ClassRepository } from '../../domain/abstractions/class-repository.abstraction';

export class ApproveEnrollmentUseCase {
  constructor(
    private readonly enrollmentRepository: EnrollmentRepository,
    private readonly classRepository: ClassRepository
  ) {}

  async execute(enrollmentData: {
    id: string;
    approved_by: string;
    notes?: string;
  }): Promise<EnrollmentEntity> {
    // Verificar se a matrícula existe e está pendente
    const enrollment = await this.enrollmentRepository.findById(
      enrollmentData.id
    );

    if (enrollment.status !== 'pending') {
      throw new Error('Only pending enrollments can be approved');
    }

    // Verificar se a classe ainda tem vagas disponíveis
    const classEntity = await this.classRepository.findById(
      enrollment.class_id
    );

    if (classEntity.current_students >= classEntity.max_students) {
      throw new Error('Class is full - cannot approve enrollment');
    }

    // Aprovar a matrícula
    const approvedEnrollment =
      await this.enrollmentRepository.approveEnrollment(
        enrollmentData.id,
        enrollmentData.approved_by,
        enrollmentData.notes
      );

    // Incrementar contador de estudantes na classe
    await this.classRepository.incrementStudentCount(enrollment.class_id);

    return approvedEnrollment;
  }
}
