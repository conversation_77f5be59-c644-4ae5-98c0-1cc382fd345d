import { EnrollmentEntity } from '../../domain/entities/enrollment.entity';
import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';
import { ClassRepository } from '../../domain/abstractions/class-repository.abstraction';

export class CancelEnrollmentUseCase {
  constructor(
    private readonly enrollmentRepository: EnrollmentRepository,
    private readonly classRepository: ClassRepository
  ) {}

  async execute(enrollmentData: {
    id: string;
    updated_by: string;
    notes?: string;
  }): Promise<EnrollmentEntity> {
    // Verificar se a matrícula existe
    const enrollment = await this.enrollmentRepository.findById(
      enrollmentData.id
    );

    if (!['pending', 'approved'].includes(enrollment.status)) {
      throw new Error('Only pending or approved enrollments can be cancelled');
    }

    // Se a matrícula estava aprovada, decrementar contador de estudantes
    if (enrollment.status === 'approved') {
      await this.classRepository.decrementStudentCount(enrollment.class_id);
    }

    // Cancelar a matrícula
    return await this.enrollmentRepository.cancelEnrollment(
      enrollmentData.id,
      enrollmentData.updated_by,
      enrollmentData.notes
    );
  }
}
