import { EnrollmentEntity } from '../../domain/entities/enrollment.entity';
import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';
import { ClassRepository } from '../../domain/abstractions/class-repository.abstraction';

export class CreateEnrollmentUseCase {
  constructor(
    private readonly enrollmentRepository: EnrollmentRepository,
    private readonly classRepository: ClassRepository
  ) {}

  async execute(enrollmentData: {
    student_id: string;
    class_id: string;
    institution_id: string;
    status?: 'pending' | 'approved' | 'rejected' | 'cancelled' | 'completed';
    notes?: string;
    created_by?: string;
  }): Promise<EnrollmentEntity> {
    // Verificar se a classe existe
    const classEntity = await this.classRepository.findById(
      enrollmentData.class_id
    );

    // Verificar se o estudante já está matriculado nesta classe
    const existingEnrollment =
      await this.enrollmentRepository.findStudentEnrollmentInClass(
        enrollmentData.student_id,
        enrollmentData.class_id
      );

    if (
      existingEnrollment &&
      !['rejected', 'cancelled'].includes(existingEnrollment.status)
    ) {
      throw new Error('Student is already enrolled in this class');
    }

    // Verificar se a classe tem vagas disponíveis
    if (classEntity.current_students >= classEntity.max_students) {
      throw new Error('Class is full - no available spots');
    }

    // Verificar se a classe está aberta para matrículas
    if (!['open', 'in_progress'].includes(classEntity.status)) {
      throw new Error('Class is not open for enrollments');
    }

    const enrollmentToCreate = {
      student_id: enrollmentData.student_id,
      class_id: enrollmentData.class_id,
      institution_id: enrollmentData.institution_id,
      status: enrollmentData.status || 'pending',
      enrollment_date: new Date(),
      notes: enrollmentData.notes,
      created_by: enrollmentData.created_by,
      created_at: new Date()
    };

    const enrollment =
      await this.enrollmentRepository.create(enrollmentToCreate);

    // Se a matrícula foi aprovada automaticamente, incrementar contador de estudantes
    if (enrollment.status === 'approved') {
      await this.classRepository.incrementStudentCount(enrollmentData.class_id);
    }

    return enrollment;
  }
}
