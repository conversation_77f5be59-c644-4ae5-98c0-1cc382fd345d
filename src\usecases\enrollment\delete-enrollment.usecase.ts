import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';
import { ClassRepository } from '../../domain/abstractions/class-repository.abstraction';

export class DeleteEnrollmentUseCase {
  constructor(
    private readonly enrollmentRepository: EnrollmentRepository,
    private readonly classRepository: ClassRepository
  ) {}

  async execute(id: string): Promise<void> {
    // Verificar se a matrícula existe
    const enrollment = await this.enrollmentRepository.findById(id);

    // Se a matrícula estava aprovada, decrementar contador de estudantes
    if (enrollment.status === 'approved') {
      await this.classRepository.decrementStudentCount(enrollment.class_id);
    }

    // Deletar a matrícula
    await this.enrollmentRepository.delete(id);
  }
}
