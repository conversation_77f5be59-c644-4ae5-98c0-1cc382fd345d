import { EnrollmentEntity } from '../../domain/entities/enrollment.entity';
import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';

export class GetEnrollmentUseCase {
  constructor(private readonly enrollmentRepository: EnrollmentRepository) {}

  async execute(id: string): Promise<EnrollmentEntity> {
    return await this.enrollmentRepository.findById(id);
  }
}
