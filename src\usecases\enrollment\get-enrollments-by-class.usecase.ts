import { EnrollmentEntity } from '../../domain/entities/enrollment.entity';
import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';

export class GetEnrollmentsByClassUseCase {
  constructor(private readonly enrollmentRepository: EnrollmentRepository) {}

  async execute(classId: string): Promise<EnrollmentEntity[]> {
    return await this.enrollmentRepository.findByClassId(classId);
  }
}
