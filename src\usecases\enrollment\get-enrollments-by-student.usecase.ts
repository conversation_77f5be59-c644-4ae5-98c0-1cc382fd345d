import { EnrollmentEntity } from '../../domain/entities/enrollment.entity';
import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';

export class GetEnrollmentsByStudentUseCase {
  constructor(private readonly enrollmentRepository: EnrollmentRepository) {}

  async execute(studentId: string): Promise<EnrollmentEntity[]> {
    return await this.enrollmentRepository.findByStudentId(studentId);
  }
}
