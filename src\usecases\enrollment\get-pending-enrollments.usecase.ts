import { EnrollmentEntity } from '../../domain/entities/enrollment.entity';
import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';

export class GetPendingEnrollmentsUseCase {
  constructor(private readonly enrollmentRepository: EnrollmentRepository) {}

  async execute(): Promise<EnrollmentEntity[]> {
    return await this.enrollmentRepository.findPendingEnrollments();
  }
}
