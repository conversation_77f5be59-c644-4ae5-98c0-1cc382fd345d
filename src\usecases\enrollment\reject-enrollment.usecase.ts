import { EnrollmentEntity } from '../../domain/entities/enrollment.entity';
import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';

export class RejectEnrollmentUseCase {
  constructor(private readonly enrollmentRepository: EnrollmentRepository) {}

  async execute(enrollmentData: {
    id: string;
    rejection_reason: string;
    updated_by: string;
  }): Promise<EnrollmentEntity> {
    // Verificar se a matrícula existe e está pendente
    const enrollment = await this.enrollmentRepository.findById(
      enrollmentData.id
    );

    if (enrollment.status !== 'pending') {
      throw new Error('Only pending enrollments can be rejected');
    }

    // Rejeitar a matrícula
    return await this.enrollmentRepository.rejectEnrollment(
      enrollmentData.id,
      enrollmentData.rejection_reason,
      enrollmentData.updated_by
    );
  }
}
