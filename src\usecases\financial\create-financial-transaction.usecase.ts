import { Injectable } from '@nestjs/common';
import { FinancialTransactionRepository } from '../../domain/abstractions/financial-repository.abstraction';
import { FinancialTransactionEntity } from '../../domain/entities/payment.entity';

export interface CreateFinancialTransactionRequest {
  institution_id: string;
  type: 'income' | 'expense';
  category: 'enrollment_fee' | 'monthly_fee' | 'material' | 'salary' | 'infrastructure' | 'other';
  amount: number;
  description: string;
  reference_id?: string;
  reference_type?: string;
  payment_method_id?: string;
  transaction_date: Date;
  due_date?: Date;
  currency?: string;
  metadata?: Record<string, any>;
  created_by: string;
}

@Injectable()
export class CreateFinancialTransactionUseCase {
  constructor(
    private readonly financialTransactionRepository: FinancialTransactionRepository
  ) {}

  async execute(request: CreateFinancialTransactionRequest): Promise<FinancialTransactionEntity> {
    const transactionData: Partial<FinancialTransactionEntity> = {
      institution_id: request.institution_id,
      type: request.type,
      category: request.category,
      amount: request.amount,
      description: request.description,
      reference_id: request.reference_id,
      reference_type: request.reference_type,
      payment_method_id: request.payment_method_id,
      status: 'pending',
      transaction_date: request.transaction_date,
      due_date: request.due_date,
      currency: request.currency || 'BRL',
      payment_type: 'one_time',
      metadata: request.metadata,
      created_by: request.created_by,
      created_at: new Date()
    };

    return await this.financialTransactionRepository.create(transactionData);
  }
}
