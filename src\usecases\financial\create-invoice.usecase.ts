import { Injectable } from '@nestjs/common';
import { InvoiceRepository } from '../../domain/abstractions/financial-repository.abstraction';
import { InvoiceEntity } from '../../domain/entities/payment.entity';

export interface CreateInvoiceRequest {
  enrollment_id: string;
  institution_id: string;
  amount: number;
  due_date: Date;
  notes?: string;
  currency?: string;
  metadata?: Record<string, any>;
  created_by: string;
}

@Injectable()
export class CreateInvoiceUseCase {
  constructor(private readonly invoiceRepository: InvoiceRepository) {}

  async execute(request: CreateInvoiceRequest): Promise<InvoiceEntity> {
    // Gerar número da fatura
    const invoiceNumber = await this.invoiceRepository.generateInvoiceNumber(
      request.institution_id
    );

    const invoiceData: Partial<InvoiceEntity> = {
      enrollment_id: request.enrollment_id,
      institution_id: request.institution_id,
      invoice_number: invoiceNumber,
      amount: request.amount,
      due_date: request.due_date,
      status: 'pending',
      notes: request.notes,
      currency: request.currency || 'BRL',
      metadata: request.metadata,
      created_by: request.created_by,
      created_at: new Date()
    };

    return await this.invoiceRepository.create(invoiceData);
  }
}
