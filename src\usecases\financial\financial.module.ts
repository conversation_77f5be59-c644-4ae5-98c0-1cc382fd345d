import { Modu<PERSON> } from '@nestjs/common';
import { RepositoriesModule } from '../../infrastructure/repositories/repositories.module';
import { CreateFinancialTransactionUseCase } from './create-financial-transaction.usecase';
import { GetFinancialTransactionsUseCase } from './get-financial-transactions.usecase';
import { UpdateFinancialTransactionUseCase } from './update-financial-transaction.usecase';
import { GetFinancialSummaryUseCase } from './get-financial-summary.usecase';
import { CreateInvoiceUseCase } from './create-invoice.usecase';
import { ProcessInvoicePaymentUseCase } from './process-invoice-payment.usecase';

@Module({
  imports: [RepositoriesModule],
  providers: [
    CreateFinancialTransactionUseCase,
    GetFinancialTransactionsUseCase,
    UpdateFinancialTransactionUseCase,
    GetFinancialSummaryUseCase,
    CreateInvoiceUseCase,
    ProcessInvoicePaymentUseCase
  ],
  exports: [
    CreateFinancialTransactionUseCase,
    GetFinancialTransactionsUseCase,
    UpdateFinancialTransactionUseCase,
    GetFinancialSummaryUseCase,
    CreateInvoiceUseCase,
    ProcessInvoicePaymentUseCase
  ]
})
export class FinancialUseCasesModule {}
