import { Injectable } from '@nestjs/common';
import { FinancialTransactionRepository } from '../../domain/abstractions/financial-repository.abstraction';

export interface GetFinancialSummaryRequest {
  institution_id: string;
  start_date?: Date;
  end_date?: Date;
}

export interface FinancialSummaryResponse {
  totalIncome: number;
  totalExpense: number;
  balance: number;
  transactionCount: number;
  incomeByCategory: Record<string, number>;
  expenseByCategory: Record<string, number>;
  monthlyTrend: Array<{
    month: string;
    income: number;
    expense: number;
    balance: number;
  }>;
}

@Injectable()
export class GetFinancialSummaryUseCase {
  constructor(
    private readonly financialTransactionRepository: FinancialTransactionRepository
  ) {}

  async execute(
    request: GetFinancialSummaryRequest
  ): Promise<FinancialSummaryResponse> {
    // Obter resumo básico
    const basicSummary =
      await this.financialTransactionRepository.getFinancialSummary(
        request.institution_id,
        request.start_date,
        request.end_date
      );

    // Obter todas as transações para análise detalhada
    let transactions;
    if (request.start_date && request.end_date) {
      transactions =
        await this.financialTransactionRepository.findByDateRangeAndInstitution(
          request.start_date,
          request.end_date,
          request.institution_id
        );
    } else {
      transactions =
        await this.financialTransactionRepository.findAllByInstitution(
          request.institution_id
        );
    }

    // Filtrar apenas transações completadas
    const completedTransactions = transactions.filter(
      t => t.status === 'completed'
    );

    // Calcular receita por categoria
    const incomeByCategory: Record<string, number> = {};
    const expenseByCategory: Record<string, number> = {};

    completedTransactions.forEach(transaction => {
      if (transaction.type === 'income') {
        incomeByCategory[transaction.category] =
          (incomeByCategory[transaction.category] || 0) + transaction.amount;
      } else if (transaction.type === 'expense') {
        expenseByCategory[transaction.category] =
          (expenseByCategory[transaction.category] || 0) + transaction.amount;
      }
    });

    // Calcular tendência mensal
    const monthlyData: Record<string, { income: number; expense: number }> = {};

    completedTransactions.forEach(transaction => {
      const monthKey = transaction.transaction_date
        .toISOString()
        .substring(0, 7); // YYYY-MM

      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { income: 0, expense: 0 };
      }

      if (transaction.type === 'income') {
        monthlyData[monthKey].income += transaction.amount;
      } else if (transaction.type === 'expense') {
        monthlyData[monthKey].expense += transaction.amount;
      }
    });

    const monthlyTrend = Object.entries(monthlyData)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([month, data]) => ({
        month,
        income: data.income,
        expense: data.expense,
        balance: data.income - data.expense
      }));

    return {
      totalIncome: basicSummary.totalIncome,
      totalExpense: basicSummary.totalExpense,
      balance: basicSummary.balance,
      transactionCount: basicSummary.transactionCount,
      incomeByCategory,
      expenseByCategory,
      monthlyTrend
    };
  }
}
