import { Injectable } from '@nestjs/common';
import { FinancialTransactionRepository } from '../../domain/abstractions/financial-repository.abstraction';
import { FinancialTransactionEntity } from '../../domain/entities/payment.entity';

export interface GetFinancialTransactionsRequest {
  institution_id: string;
  type?: 'income' | 'expense';
  category?: string;
  status?: string;
  start_date?: Date;
  end_date?: Date;
  reference_id?: string;
  reference_type?: string;
}

@Injectable()
export class GetFinancialTransactionsUseCase {
  constructor(
    private readonly financialTransactionRepository: FinancialTransactionRepository
  ) {}

  async execute(
    request: GetFinancialTransactionsRequest
  ): Promise<FinancialTransactionEntity[]> {
    // Se há filtros específicos, aplicá-los
    if (request.start_date && request.end_date) {
      return await this.financialTransactionRepository.findByDateRangeAndInstitution(
        request.start_date,
        request.end_date,
        request.institution_id
      );
    }

    if (request.type) {
      return await this.financialTransactionRepository.findByTypeAndInstitution(
        request.type,
        request.institution_id
      );
    }

    if (request.category) {
      return await this.financialTransactionRepository.findByCategoryAndInstitution(
        request.category,
        request.institution_id
      );
    }

    if (request.status) {
      return await this.financialTransactionRepository.findByStatusAndInstitution(
        request.status,
        request.institution_id
      );
    }

    if (request.reference_id && request.reference_type) {
      return await this.financialTransactionRepository.findByReferenceAndInstitution(
        request.reference_id,
        request.reference_type,
        request.institution_id
      );
    }

    // Se não há filtros específicos, retornar todas as transações da instituição
    return await this.financialTransactionRepository.findAllByInstitution(
      request.institution_id
    );
  }
}
