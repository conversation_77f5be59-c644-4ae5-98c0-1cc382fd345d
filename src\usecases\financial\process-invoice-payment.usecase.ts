import { Injectable } from '@nestjs/common';
import { InvoiceRepository, FinancialTransactionRepository } from '../../domain/abstractions/financial-repository.abstraction';
import { InvoiceEntity } from '../../domain/entities/payment.entity';

export interface ProcessInvoicePaymentRequest {
  invoice_id: string;
  institution_id: string;
  payment_method_id?: string;
  payment_date?: Date;
  stripe_payment_intent_id?: string;
  notes?: string;
  processed_by: string;
}

@Injectable()
export class ProcessInvoicePaymentUseCase {
  constructor(
    private readonly invoiceRepository: InvoiceRepository,
    private readonly financialTransactionRepository: FinancialTransactionRepository
  ) {}

  async execute(request: ProcessInvoicePaymentRequest): Promise<InvoiceEntity> {
    // Verificar se a fatura existe e pertence à instituição
    const invoice = await this.invoiceRepository.findByIdAndInstitution(
      request.invoice_id,
      request.institution_id
    );

    if (!invoice) {
      throw new Error('Invoice not found or access denied');
    }

    if (invoice.status === 'paid') {
      throw new Error('Invoice is already paid');
    }

    if (invoice.status === 'cancelled') {
      throw new Error('Cannot process payment for cancelled invoice');
    }

    const paymentDate = request.payment_date || new Date();

    // Marcar fatura como paga
    const updatedInvoice = await this.invoiceRepository.markAsPaid(
      request.invoice_id,
      paymentDate,
      request.payment_method_id
    );

    // Criar transação financeira correspondente
    await this.financialTransactionRepository.create({
      institution_id: request.institution_id,
      type: 'income',
      category: 'monthly_fee', // ou determinar baseado no contexto
      amount: invoice.amount,
      description: `Payment for invoice ${invoice.invoice_number}`,
      reference_id: invoice.id,
      reference_type: 'invoice',
      payment_method_id: request.payment_method_id,
      status: 'completed',
      transaction_date: paymentDate,
      paid_date: paymentDate,
      stripe_payment_intent_id: request.stripe_payment_intent_id,
      currency: invoice.currency,
      payment_type: 'one_time',
      metadata: {
        invoice_id: invoice.id,
        invoice_number: invoice.invoice_number,
        enrollment_id: invoice.enrollment_id,
        notes: request.notes
      },
      created_by: request.processed_by,
      created_at: new Date()
    });

    return updatedInvoice;
  }
}
