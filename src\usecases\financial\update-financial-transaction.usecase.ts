import { Injectable } from '@nestjs/common';
import { FinancialTransactionRepository } from '../../domain/abstractions/financial-repository.abstraction';
import { FinancialTransactionEntity } from '../../domain/entities/payment.entity';

export interface UpdateFinancialTransactionRequest {
  id: string;
  institution_id: string;
  type?: 'income' | 'expense';
  category?:
    | 'enrollment_fee'
    | 'monthly_fee'
    | 'material'
    | 'salary'
    | 'infrastructure'
    | 'other';
  amount?: number;
  description?: string;
  reference_id?: string;
  reference_type?: string;
  payment_method_id?: string;
  status?: 'pending' | 'completed' | 'cancelled';
  transaction_date?: Date;
  due_date?: Date;
  paid_date?: Date;
  currency?: string;
  metadata?: Record<string, any>;
  updated_by: string;
}

@Injectable()
export class UpdateFinancialTransactionUseCase {
  constructor(
    private readonly financialTransactionRepository: FinancialTransactionRepository
  ) {}

  async execute(
    request: UpdateFinancialTransactionRequest
  ): Promise<FinancialTransactionEntity> {
    // Verificar se a transação existe e pertence à instituição
    const existingTransaction =
      await this.financialTransactionRepository.findByIdAndInstitution(
        request.id,
        request.institution_id
      );

    if (!existingTransaction) {
      throw new Error('Financial transaction not found or access denied');
    }

    const updateData: Partial<FinancialTransactionEntity> = {
      updated_by: request.updated_by,
      updated_at: new Date()
    };

    // Adicionar apenas os campos que foram fornecidos
    if (request.type !== undefined) updateData.type = request.type;
    if (request.category !== undefined) updateData.category = request.category;
    if (request.amount !== undefined) updateData.amount = request.amount;
    if (request.description !== undefined)
      updateData.description = request.description;
    if (request.reference_id !== undefined)
      updateData.reference_id = request.reference_id;
    if (request.reference_type !== undefined)
      updateData.reference_type = request.reference_type;
    if (request.payment_method_id !== undefined)
      updateData.payment_method_id = request.payment_method_id;
    if (request.status !== undefined) updateData.status = request.status;
    if (request.transaction_date !== undefined)
      updateData.transaction_date = request.transaction_date;
    if (request.due_date !== undefined) updateData.due_date = request.due_date;
    if (request.paid_date !== undefined)
      updateData.paid_date = request.paid_date;
    if (request.currency !== undefined) updateData.currency = request.currency;
    if (request.metadata !== undefined) updateData.metadata = request.metadata;

    return await this.financialTransactionRepository.update(
      request.id,
      updateData
    );
  }
}
