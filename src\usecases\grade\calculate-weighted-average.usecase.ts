import { GradeRepository } from '../../domain/abstractions/grade-repository.abstraction';

export class CalculateWeightedAverageUseCase {
  constructor(private readonly gradeRepository: GradeRepository) {}

  async execute(enrollmentId: string): Promise<{
    weightedAverage: number;
    totalWeight: number;
    gradeCount: number;
  }> {
    return await this.gradeRepository.calculateWeightedAverage(enrollmentId);
  }
}
