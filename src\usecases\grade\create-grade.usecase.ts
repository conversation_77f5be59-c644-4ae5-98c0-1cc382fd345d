import { GradeEntity } from '../../domain/entities/grade.entity';
import { GradeRepository } from '../../domain/abstractions/grade-repository.abstraction';
import { EnrollmentRepository } from '../../domain/abstractions/enrollment-repository.abstraction';

export class CreateGradeUseCase {
  constructor(
    private readonly gradeRepository: GradeRepository,
    private readonly enrollmentRepository: EnrollmentRepository
  ) {}

  async execute(gradeData: {
    enrollment_id: string;
    assessment_type: string;
    assessment_name: string;
    grade: number;
    max_grade: number;
    weight?: number;
    assessment_date: Date;
    recorded_by: string;
    created_by: string;
  }): Promise<GradeEntity> {
    // Verificar se a matrícula existe e está ativa
    const enrollment = await this.enrollmentRepository.findById(gradeData.enrollment_id);
    
    if (enrollment.status !== 'approved') {
      throw new Error('Cannot record grade for non-approved enrollment');
    }

    // Validar valores da nota
    if (gradeData.grade < 0) {
      throw new Error('Grade cannot be negative');
    }

    if (gradeData.max_grade <= 0) {
      throw new Error('Max grade must be greater than 0');
    }

    if (gradeData.grade > gradeData.max_grade) {
      throw new Error('Grade cannot be greater than max grade');
    }

    // Validar peso
    const weight = gradeData.weight || 1.0;
    if (weight <= 0) {
      throw new Error('Weight must be greater than 0');
    }

    // Validar data da avaliação (não pode ser futura)
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    const assessmentDate = new Date(gradeData.assessment_date);
    
    if (assessmentDate > today) {
      throw new Error('Cannot record grade for future dates');
    }

    const gradeToCreate = {
      enrollment_id: gradeData.enrollment_id,
      assessment_type: gradeData.assessment_type.trim(),
      assessment_name: gradeData.assessment_name.trim(),
      grade: gradeData.grade,
      max_grade: gradeData.max_grade,
      weight: weight,
      assessment_date: gradeData.assessment_date,
      recorded_by: gradeData.recorded_by,
      created_by: gradeData.created_by,
      created_at: new Date()
    };

    return await this.gradeRepository.create(gradeToCreate);
  }
}
