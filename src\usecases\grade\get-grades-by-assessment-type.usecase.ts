import { GradeEntity } from '../../domain/entities/grade.entity';
import { GradeRepository } from '../../domain/abstractions/grade-repository.abstraction';

export class GetGradesByAssessmentTypeUseCase {
  constructor(private readonly gradeRepository: GradeRepository) {}

  async execute(enrollmentId: string): Promise<
    {
      assessmentType: string;
      grades: GradeEntity[];
      average: number;
      totalWeight: number;
    }[]
  > {
    return await this.gradeRepository.getGradesByAssessmentType(enrollmentId);
  }
}
