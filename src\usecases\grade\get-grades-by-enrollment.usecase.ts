import { GradeEntity } from '../../domain/entities/grade.entity';
import { GradeRepository } from '../../domain/abstractions/grade-repository.abstraction';

export class GetGradesByEnrollmentUseCase {
  constructor(private readonly gradeRepository: GradeRepository) {}

  async execute(enrollmentId: string): Promise<GradeEntity[]> {
    return await this.gradeRepository.findByEnrollmentId(enrollmentId);
  }
}
