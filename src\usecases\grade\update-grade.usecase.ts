import { GradeEntity } from '../../domain/entities/grade.entity';
import { GradeRepository } from '../../domain/abstractions/grade-repository.abstraction';

export class UpdateGradeUseCase {
  constructor(private readonly gradeRepository: GradeRepository) {}

  async execute(
    id: string,
    gradeData: {
      assessment_type?: string;
      assessment_name?: string;
      grade?: number;
      max_grade?: number;
      weight?: number;
      assessment_date?: Date;
      updated_by: string;
    }
  ): Promise<GradeEntity> {
    // Verificar se a nota existe
    const existingGrade = await this.gradeRepository.findById(id);

    // Validar valores da nota se fornecidos
    if (gradeData.grade !== undefined) {
      if (gradeData.grade < 0) {
        throw new Error('Grade cannot be negative');
      }

      const maxGrade = gradeData.max_grade || existingGrade.max_grade;
      if (gradeData.grade > maxGrade) {
        throw new Error('Grade cannot be greater than max grade');
      }
    }

    if (gradeData.max_grade !== undefined) {
      if (gradeData.max_grade <= 0) {
        throw new Error('Max grade must be greater than 0');
      }

      const grade = gradeData.grade || existingGrade.grade;
      if (grade > gradeData.max_grade) {
        throw new Error('Grade cannot be greater than max grade');
      }
    }

    // Validar peso se fornecido
    if (gradeData.weight !== undefined && gradeData.weight <= 0) {
      throw new Error('Weight must be greater than 0');
    }

    // Validar data da avaliação se fornecida
    if (gradeData.assessment_date) {
      const today = new Date();
      today.setHours(23, 59, 59, 999);
      const assessmentDate = new Date(gradeData.assessment_date);
      
      if (assessmentDate > today) {
        throw new Error('Cannot record grade for future dates');
      }
    }

    const updateData = {
      ...gradeData,
      assessment_type: gradeData.assessment_type?.trim(),
      assessment_name: gradeData.assessment_name?.trim(),
      updated_at: new Date()
    };

    return await this.gradeRepository.update(id, updateData);
  }
}
