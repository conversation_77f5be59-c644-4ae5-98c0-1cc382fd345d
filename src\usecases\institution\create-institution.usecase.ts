import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InstitutionEntity } from '../../domain/entities/institution.entity';
import { CreateInstitutionDto } from '../../infrastructure/controllers/institution/institution.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { InstitutionRepository } from '../../infrastructure/repositories/institution-repository';
import { EmailService } from '../../infrastructure/services/email/email.service';

@Injectable()
export class CreateInstitutionUseCase {
  constructor(
    private readonly institutionRepository: InstitutionRepository,
    private readonly logger: LoggerService,
    private readonly emailService: EmailService
  ) {}
  private readonly logContextName: string = 'CREATE_INSTITUTION_USE_CASE';

  async execute(institution: CreateInstitutionDto): Promise<InstitutionEntity> {
    this.logger.log(this.logContextName, 'Iniciando criação de instituição');

    const institutionAlreadyExists =
      await this.institutionRepository.findByTaxId(institution.tax_id);

    if (institutionAlreadyExists)
      throw new HttpException(
        'Instituição já cadastrada para o CNPJ: ' + institution.tax_id,
        HttpStatus.CONFLICT
      );

    const newInstitution = await this.institutionRepository.insert(institution);

    this.logger.log(
      this.logContextName,
      'Instituição criada com sucesso: ' + newInstitution.id
    );

    return newInstitution;
  }
}
