import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { InstitutionRepository } from '../../infrastructure/repositories/institution-repository';

@Injectable()
export class DeleteInstitutionUseCase {
  constructor(
    private readonly institutionRepository: InstitutionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'DELETE_INSTITUTION_USE_CASE';

  async execute(id: string): Promise<void> {
    this.logger.log(this.logContextName, 'Deletando Instituição por Id');

    const institution = this.institutionRepository.findById(id);
    if (!institution)
      throw new HttpException(
        'Instituição não encontrada',
        HttpStatus.NOT_FOUND
      );

    await this.institutionRepository.delete(id);
  }
}
