import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InstitutionEntity } from '../../domain/entities/institution.entity';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { InstitutionRepository } from '../../infrastructure/repositories/institution-repository';

@Injectable()
export class GetInstitutionUseCase {
  constructor(
    private readonly institutionRepository: InstitutionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_INSTITUTION_USE_CASE';

  async execute(id: string): Promise<InstitutionEntity> {
    this.logger.log(this.logContextName, 'Buscando Instituição por Id');
    const institution = await this.institutionRepository.findById(id);
    if (!institution)
      throw new HttpException(
        'Instituição não encontrada',
        HttpStatus.NOT_FOUND
      );
    return institution;
  }
}
