import { Injectable } from '@nestjs/common';
import { InstitutionEntity } from '../../domain/entities/institution.entity';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { InstitutionRepository } from '../../infrastructure/repositories/institution-repository';

@Injectable()
export class GetInstitutionsUseCase {
  constructor(
    private readonly institutionRepository: InstitutionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_INSTITUTIONS_USE_CASE';

  async execute(): Promise<InstitutionEntity[]> {
    this.logger.log(this.logContextName, 'Buscando lista de instituições');
    const institution = await this.institutionRepository.findAll();
    return institution;
  }
}
