import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InstitutionEntity } from '../../domain/entities/institution.entity';
import { UpdateInstitutionDto } from '../../infrastructure/controllers/institution/institution.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { InstitutionRepository } from '../../infrastructure/repositories/institution-repository';

@Injectable()
export class UpdateInstitutionUseCase {
  constructor(
    private readonly institutionRepository: InstitutionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'UPDATE_INSTITUTION_USE_CASE';
  async execute(
    id: string,
    newInstitution: UpdateInstitutionDto
  ): Promise<InstitutionEntity> {
    this.logger.log(
      this.logContextName,
      'Iniciando Atualização de Instituição'
    );

    const institution = await this.institutionRepository.findById(id);
    if (!institution)
      throw new HttpException(
        'Instituição não encontrada',
        HttpStatus.NOT_FOUND
      );

    const updatedInstitution = await this.institutionRepository.update(id, {
      ...institution,
      ...newInstitution
    });

    return updatedInstitution;
  }
}
