import { Injectable } from '@nestjs/common';
import Stripe from 'stripe';
import { PaymentRepository } from '../../domain/abstractions/payment-repository.abstraction';
import { StripeConfig } from '../../infrastructure/config/stripe/stripe.config';

export interface CreatePaymentSessionRequest {
  institution_id: string;
  amount: number;
  currency?: string;
  description?: string;
  success_url?: string;
  cancel_url?: string;
  payment_type: 'one_time' | 'subscription';
  plan_id?: string; // Para assinaturas
  trial_days?: number; // Para assinaturas com trial
  metadata?: Record<string, any>;
  created_by: string;
}

export interface CreatePaymentSessionResponse {
  session_id: string;
  session_url: string;
  payment_id: string;
}

@Injectable()
export class CreatePaymentSessionUseCase {
  private stripe: Stripe;

  constructor(
    private readonly stripeConfig: StripeConfig,
    private readonly paymentRepository: PaymentRepository
  ) {
    this.stripe = this.stripeConfig.getStripeInstance();
  }

  async execute(request: CreatePaymentSessionRequest): Promise<CreatePaymentSessionResponse> {
    const config = this.stripeConfig.getConfig();

    try {
      let sessionParams: Stripe.Checkout.SessionCreateParams;

      if (request.payment_type === 'one_time') {
        // Pagamento único
        sessionParams = {
          payment_method_types: ['card', 'boleto'],
          line_items: [
            {
              price_data: {
                currency: request.currency || config.currency,
                product_data: {
                  name: request.description || 'Pagamento',
                },
                unit_amount: Math.round(request.amount * 100), // Stripe usa centavos
              },
              quantity: 1,
            },
          ],
          mode: 'payment',
          success_url: request.success_url || config.successUrl,
          cancel_url: request.cancel_url || config.cancelUrl,
          metadata: {
            institution_id: request.institution_id,
            payment_type: request.payment_type,
            ...request.metadata,
          },
        };
      } else {
        // Assinatura
        if (!request.plan_id) {
          throw new Error('Plan ID is required for subscription payments');
        }

        sessionParams = {
          payment_method_types: ['card'],
          line_items: [
            {
              price: request.plan_id, // ID do preço no Stripe
              quantity: 1,
            },
          ],
          mode: 'subscription',
          success_url: request.success_url || config.successUrl,
          cancel_url: request.cancel_url || config.cancelUrl,
          subscription_data: {
            trial_period_days: request.trial_days,
            metadata: {
              institution_id: request.institution_id,
              ...request.metadata,
            },
          },
          metadata: {
            institution_id: request.institution_id,
            payment_type: request.payment_type,
            ...request.metadata,
          },
        };
      }

      // Criar sessão no Stripe
      const session = await this.stripe.checkout.sessions.create(sessionParams);

      // Salvar pagamento no banco
      const payment = await this.paymentRepository.create({
        institution_id: request.institution_id,
        stripe_session_id: session.id,
        amount: request.amount,
        currency: request.currency || config.currency,
        status: 'pending',
        payment_type: request.payment_type,
        description: request.description,
        metadata: request.metadata,
        created_by: request.created_by,
        created_at: new Date(),
      });

      return {
        session_id: session.id,
        session_url: session.url!,
        payment_id: payment.id,
      };
    } catch (error) {
      throw new Error(`Failed to create payment session: ${error.message}`);
    }
  }
}
