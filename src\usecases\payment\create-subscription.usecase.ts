import { Injectable } from '@nestjs/common';
import Strip<PERSON> from 'stripe';
import { StripeConfig } from '../../infrastructure/config/stripe/stripe.config';
import { SubscriptionRepository } from '../../domain/abstractions/payment-repository.abstraction';
import { SubscriptionEntity } from '../../domain/entities/payment.entity';

export interface CreateSubscriptionRequest {
  institution_id: string;
  plan_id: string;
  customer_email: string;
  customer_name?: string;
  trial_days?: number;
  payment_method_id?: string;
  metadata?: Record<string, any>;
  created_by: string;
}

export interface CreateSubscriptionResponse {
  subscription_id: string;
  customer_id: string;
  status: string;
  current_period_end: Date;
  trial_end?: Date;
  latest_invoice_url?: string;
}

@Injectable()
export class CreateSubscriptionUseCase {
  private stripe: Stripe;

  constructor(
    private readonly stripeConfig: StripeConfig,
    private readonly subscriptionRepository: SubscriptionRepository
  ) {
    this.stripe = this.stripeConfig.getStripeInstance();
  }

  async execute(request: CreateSubscriptionRequest): Promise<CreateSubscriptionResponse> {
    try {
      // Criar ou buscar cliente no Stripe
      let customer: Stripe.Customer;
      
      const existingCustomers = await this.stripe.customers.list({
        email: request.customer_email,
        limit: 1,
      });

      if (existingCustomers.data.length > 0) {
        customer = existingCustomers.data[0];
      } else {
        customer = await this.stripe.customers.create({
          email: request.customer_email,
          name: request.customer_name,
          metadata: {
            institution_id: request.institution_id,
          },
        });
      }

      // Configurar parâmetros da assinatura
      const subscriptionParams: Stripe.SubscriptionCreateParams = {
        customer: customer.id,
        items: [
          {
            price: request.plan_id,
          },
        ],
        metadata: {
          institution_id: request.institution_id,
          ...request.metadata,
        },
      };

      // Adicionar período de trial se especificado
      if (request.trial_days && request.trial_days > 0) {
        subscriptionParams.trial_period_days = request.trial_days;
      }

      // Adicionar método de pagamento se especificado
      if (request.payment_method_id) {
        subscriptionParams.default_payment_method = request.payment_method_id;
      }

      // Criar assinatura no Stripe
      const subscription = await this.stripe.subscriptions.create(subscriptionParams);

      // Salvar assinatura no banco
      const subscriptionEntity = await this.subscriptionRepository.create({
        institution_id: request.institution_id,
        plan_id: request.plan_id,
        stripe_subscription_id: subscription.id,
        stripe_customer_id: customer.id,
        status: subscription.status as any,
        current_period_start: new Date(subscription.current_period_start * 1000),
        current_period_end: new Date(subscription.current_period_end * 1000),
        trial_start: subscription.trial_start ? new Date(subscription.trial_start * 1000) : undefined,
        trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : undefined,
        metadata: request.metadata,
        created_by: request.created_by,
        created_at: new Date(),
      });

      // Buscar URL da última fatura se necessário
      let latestInvoiceUrl: string | undefined;
      if (subscription.latest_invoice) {
        const invoice = await this.stripe.invoices.retrieve(
          subscription.latest_invoice as string
        );
        latestInvoiceUrl = invoice.hosted_invoice_url || undefined;
      }

      return {
        subscription_id: subscription.id,
        customer_id: customer.id,
        status: subscription.status,
        current_period_end: new Date(subscription.current_period_end * 1000),
        trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : undefined,
        latest_invoice_url: latestInvoiceUrl,
      };
    } catch (error) {
      throw new Error(`Failed to create subscription: ${error.message}`);
    }
  }
}
