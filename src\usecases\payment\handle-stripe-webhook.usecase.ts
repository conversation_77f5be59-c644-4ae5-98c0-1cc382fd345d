import { Injectable } from '@nestjs/common';
import Stripe from 'stripe';
import { PaymentRepository, SubscriptionRepository } from '../../domain/abstractions/payment-repository.abstraction';
import { StripeConfig } from '../../infrastructure/config/stripe/stripe.config';

@Injectable()
export class HandleStripeWebhookUseCase {
  private stripe: Stripe;

  constructor(
    private readonly stripeConfig: StripeConfig,
    private readonly paymentRepository: PaymentRepository,
    private readonly subscriptionRepository: SubscriptionRepository
  ) {
    this.stripe = this.stripeConfig.getStripeInstance();
  }

  async execute(body: string, signature: string): Promise<void> {
    const webhookSecret = this.stripeConfig.getWebhookSecret();

    try {
      // Verificar assinatura do webhook
      const event = this.stripe.webhooks.constructEvent(body, signature, webhookSecret);

      // Processar evento baseado no tipo
      switch (event.type) {
        case 'checkout.session.completed':
          await this.handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session);
          break;

        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
          break;

        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
          break;

        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;

        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
          break;

        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription);
          break;

        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;

        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;

        default:
          console.log(`Unhandled event type: ${event.type}`);
      }
    } catch (error) {
      throw new Error(`Webhook error: ${error.message}`);
    }
  }

  private async handleCheckoutSessionCompleted(session: Stripe.Checkout.Session): Promise<void> {
    try {
      const payment = await this.paymentRepository.findByStripeSessionId(session.id);
      if (payment) {
        await this.paymentRepository.update(payment.id, {
          stripe_payment_intent_id: session.payment_intent as string,
          status: 'succeeded',
          updated_at: new Date(),
        });
      }
    } catch (error) {
      console.error('Error handling checkout session completed:', error);
    }
  }

  private async handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    try {
      const payment = await this.paymentRepository.findByStripePaymentIntentId(paymentIntent.id);
      if (payment) {
        await this.paymentRepository.update(payment.id, {
          status: 'succeeded',
          updated_at: new Date(),
        });
      }
    } catch (error) {
      console.error('Error handling payment intent succeeded:', error);
    }
  }

  private async handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    try {
      const payment = await this.paymentRepository.findByStripePaymentIntentId(paymentIntent.id);
      if (payment) {
        await this.paymentRepository.update(payment.id, {
          status: 'failed',
          updated_at: new Date(),
        });
      }
    } catch (error) {
      console.error('Error handling payment intent failed:', error);
    }
  }

  private async handleInvoicePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    try {
      if ((invoice as any).subscription) {
        const subscription = await this.subscriptionRepository.findByStripeSubscriptionId(
          (invoice as any).subscription as string
        );
        if (subscription) {
          await this.subscriptionRepository.update(subscription.id, {
            status: 'active',
            updated_at: new Date(),
          });
        }
      }
    } catch (error) {
      console.error('Error handling invoice payment succeeded:', error);
    }
  }

  private async handleInvoicePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    try {
      if ((invoice as any).subscription) {
        const subscription = await this.subscriptionRepository.findByStripeSubscriptionId(
          (invoice as any).subscription as string
        );
        if (subscription) {
          await this.subscriptionRepository.update(subscription.id, {
            status: 'past_due',
            updated_at: new Date(),
          });
        }
      }
    } catch (error) {
      console.error('Error handling invoice payment failed:', error);
    }
  }

  private async handleSubscriptionCreated(stripeSubscription: Stripe.Subscription): Promise<void> {
    // Já tratado no CreateSubscriptionUseCase
    console.log('Subscription created:', stripeSubscription.id);
  }

  private async handleSubscriptionUpdated(stripeSubscription: Stripe.Subscription): Promise<void> {
    try {
      const subscription = await this.subscriptionRepository.findByStripeSubscriptionId(
        stripeSubscription.id
      );
      if (subscription) {
        await this.subscriptionRepository.update(subscription.id, {
          status: stripeSubscription.status as any,
          current_period_start: new Date((stripeSubscription as any).current_period_start * 1000),
          current_period_end: new Date((stripeSubscription as any).current_period_end * 1000),
          trial_start: (stripeSubscription as any).trial_start ? new Date((stripeSubscription as any).trial_start * 1000) : undefined,
          trial_end: (stripeSubscription as any).trial_end ? new Date((stripeSubscription as any).trial_end * 1000) : undefined,
          canceled_at: (stripeSubscription as any).canceled_at ? new Date((stripeSubscription as any).canceled_at * 1000) : undefined,
          ended_at: (stripeSubscription as any).ended_at ? new Date((stripeSubscription as any).ended_at * 1000) : undefined,
          updated_at: new Date(),
        });
      }
    } catch (error) {
      console.error('Error handling subscription updated:', error);
    }
  }

  private async handleSubscriptionDeleted(stripeSubscription: Stripe.Subscription): Promise<void> {
    try {
      const subscription = await this.subscriptionRepository.findByStripeSubscriptionId(
        stripeSubscription.id
      );
      if (subscription) {
        await this.subscriptionRepository.update(subscription.id, {
          status: 'canceled',
          canceled_at: new Date(),
          ended_at: new Date(),
          updated_at: new Date(),
        });
      }
    } catch (error) {
      console.error('Error handling subscription deleted:', error);
    }
  }
}
