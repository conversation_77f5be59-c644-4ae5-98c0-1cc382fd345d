import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { PermissionEntity } from '../../domain/entities/permission.entity';
import { CreatePermissionDto } from '../../infrastructure/controllers/permission/permission.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { PermissionRepository } from '../../infrastructure/repositories/permission-repository';

@Injectable()
export class CreatePermissionUseCase {
  constructor(
    private readonly permissionRepository: PermissionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'CREATE_PERMISSION_USE_CASE';

  async execute(permission: CreatePermissionDto): Promise<PermissionEntity> {
    this.logger.log(this.logContextName, 'Iniciando criação de permissão');

    const permissionAlreadyExists = await this.permissionRepository.findByKey(
      permission.key
    );

    if (permissionAlreadyExists)
      throw new HttpException(
        'Já existe uma permissão com a key: ' + permission.key,
        HttpStatus.CONFLICT
      );

    const newPermission = await this.permissionRepository.insert(permission);

    this.logger.log(this.logContextName, 'Permissão criada com sucesso');

    return newPermission;
  }
}
