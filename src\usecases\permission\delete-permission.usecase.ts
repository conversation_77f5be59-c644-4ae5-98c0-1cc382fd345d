import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { PermissionRepository } from '../../infrastructure/repositories/permission-repository';

@Injectable()
export class DeletePermissionUseCase {
  constructor(
    private readonly permissionRepository: PermissionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'DELETE_PERMISSION_USE_CASE';

  async execute(id: string): Promise<void> {
    this.logger.log(this.logContextName, 'Iniciando exclusão de permissão');

    const permission = await this.permissionRepository.findById(id);
    if (!permission)
      throw new HttpException('Permisssão não existe', HttpStatus.NOT_FOUND);

    await this.permissionRepository.delete(id);
    this.logger.log(this.logContextName, 'Permissão excluída com sucesso');
  }
}
