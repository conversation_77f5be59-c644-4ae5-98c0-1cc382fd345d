import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { PermissionPresenter } from '../../infrastructure/controllers/permission/permission.presenter';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { PermissionRepository } from '../../infrastructure/repositories/permission-repository';

@Injectable()
export class GetPermissionUseCase {
  constructor(
    private readonly permissionRepository: PermissionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_PERMISSION_USE_CASE';

  async execute(id: string): Promise<PermissionPresenter> {
    this.logger.log(this.logContextName, 'Iniciando busca de permissão');
    const permission = await this.permissionRepository.findById(id);
    if (!permission)
      throw new HttpException('Permission not found', HttpStatus.NOT_FOUND);

    return permission;
  }
}
