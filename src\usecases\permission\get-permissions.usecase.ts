import { Injectable } from '@nestjs/common';
import { PermissionPresenter } from '../../infrastructure/controllers/permission/permission.presenter';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { PermissionRepository } from '../../infrastructure/repositories/permission-repository';

@Injectable()
export class GetPermissionsUseCase {
  constructor(
    private readonly permissionRepository: PermissionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_PERMISSIONS_USE_CASE';

  async execute(): Promise<PermissionPresenter[]> {
    this.logger.log(this.logContextName, 'Iniciando busca de permissões');
    const permission = await this.permissionRepository.findAll();

    return permission;
  }
}
