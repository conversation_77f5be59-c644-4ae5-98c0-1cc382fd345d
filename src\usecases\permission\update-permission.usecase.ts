import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { PermissionEntity } from '../../domain/entities/permission.entity';
import { UpdatePermissionDto } from '../../infrastructure/controllers/permission/permission.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { PermissionRepository } from '../../infrastructure/repositories/permission-repository';

@Injectable()
export class UpdatePermissionUseCase {
  constructor(
    private readonly permissionRepository: PermissionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'UPDATE_PERMISSION_USE_CASE';

  async execute(
    id: string,
    newPermission: UpdatePermissionDto
  ): Promise<PermissionEntity> {
    this.logger.log(this.logContextName, 'Iniciando atualização de permissão');

    const permission = await this.permissionRepository.findById(id);

    if (!permission)
      throw new HttpException(
        'Não existe uma permissão com o id: ' + id,
        HttpStatus.NOT_FOUND
      );

    if (id !== newPermission.id)
      throw new HttpException(
        'Não autorizado, dados para update incosistentes.',
        HttpStatus.UNAUTHORIZED
      );

    const alreadyExistsPermissionKey =
      await this.permissionRepository.findByKey(newPermission.key);

    if (alreadyExistsPermissionKey)
      throw new HttpException(
        'Já existe uma permissão com a key: ' + newPermission.key,
        HttpStatus.CONFLICT
      );

    const updatedPermission = await this.permissionRepository.update(id, {
      ...permission,
      ...newPermission
    });

    this.logger.log(this.logContextName, 'Permissão criada com sucesso');

    return updatedPermission;
  }
}
