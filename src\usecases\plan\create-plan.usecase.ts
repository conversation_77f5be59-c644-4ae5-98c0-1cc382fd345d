import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { PlanEntity } from '../../domain/entities/plan.entity';
import { CreatePlanDto } from '../../infrastructure/controllers/plan/plan.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { PlanRepository } from '../../infrastructure/repositories/plan-repository';

@Injectable()
export class CreatePlanUseCase {
  constructor(
    private readonly planRepository: PlanRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'CREATE_PLAN_USE_CASE';

  async execute(plan: CreatePlanDto): Promise<PlanEntity> {
    this.logger.log(this.logContextName, 'Iniciando criação de plano');

    // Verificar se já existe um plano com o mesmo nome
    const existingPlan = await this.planRepository.findByName(plan.name);
    if (existingPlan) {
      throw new HttpException(
        'Já existe um plano com este nome',
        HttpStatus.CONFLICT
      );
    }

    const newPlan = await this.planRepository.insert(plan);

    this.logger.log(this.logContextName, 'Plano criado com sucesso');

    return newPlan;
  }
}
