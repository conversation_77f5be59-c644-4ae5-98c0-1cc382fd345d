import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { PlanRepository } from '../../infrastructure/repositories/plan-repository';
import { SubscriptionRepository } from '../../infrastructure/repositories/subscription-repository';

@Injectable()
export class DeletePlanUseCase {
  constructor(
    private readonly planRepository: PlanRepository,
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'DELETE_PLAN_USE_CASE';

  async execute(id: string): Promise<void> {
    this.logger.log(this.logContextName, 'Iniciando exclusão de plano');

    const plan = await this.planRepository.findById(id);

    if (!plan)
      throw new HttpException(
        'Não existe um plano com o id: ' + id,
        HttpStatus.NOT_FOUND
      );

    // Verificar se existem assinaturas ativas para este plano
    const activeSubscriptions = await this.subscriptionRepository.findByPlanId(id);
    const hasActiveSubscriptions = activeSubscriptions.some(
      sub => sub.status === 'active' || sub.status === 'trial'
    );

    if (hasActiveSubscriptions) {
      throw new HttpException(
        'Não é possível excluir um plano que possui assinaturas ativas',
        HttpStatus.CONFLICT
      );
    }

    await this.planRepository.delete(id);

    this.logger.log(this.logContextName, 'Plano excluído com sucesso');
  }
}
