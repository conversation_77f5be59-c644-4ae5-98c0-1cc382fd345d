import { Injectable } from '@nestjs/common';
import { PlanEntity } from '../../domain/entities/plan.entity';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { PlanRepository } from '../../infrastructure/repositories/plan-repository';

@Injectable()
export class GetActivePlansUseCase {
  constructor(
    private readonly planRepository: PlanRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_ACTIVE_PLANS_USE_CASE';

  async execute(): Promise<PlanEntity[]> {
    this.logger.log(this.logContextName, 'Iniciando busca de planos ativos');
    const plans = await this.planRepository.findActive();

    return plans;
  }
}
