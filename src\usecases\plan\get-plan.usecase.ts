import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { PlanPresenter } from '../../infrastructure/controllers/plan/plan.presenter';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { PlanRepository } from '../../infrastructure/repositories/plan-repository';

@Injectable()
export class GetPlanUseCase {
  constructor(
    private readonly planRepository: PlanRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_PLAN_USE_CASE';

  async execute(id: string): Promise<PlanPresenter> {
    this.logger.log(this.logContextName, 'Iniciando busca de plano');
    const plan = await this.planRepository.findById(id);
    if (!plan)
      throw new HttpException('Plan not found', HttpStatus.NOT_FOUND);

    return plan;
  }
}
