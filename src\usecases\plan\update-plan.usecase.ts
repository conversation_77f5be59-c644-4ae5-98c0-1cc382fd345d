import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { PlanEntity } from '../../domain/entities/plan.entity';
import { UpdatePlanDto } from '../../infrastructure/controllers/plan/plan.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { PlanRepository } from '../../infrastructure/repositories/plan-repository';

@Injectable()
export class UpdatePlanUseCase {
  constructor(
    private readonly planRepository: PlanRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'UPDATE_PLAN_USE_CASE';

  async execute(id: string, newPlan: UpdatePlanDto): Promise<PlanEntity> {
    this.logger.log(this.logContextName, 'Iniciando atualização de plano');

    const plan = await this.planRepository.findById(id);

    if (!plan)
      throw new HttpException(
        'Não existe um plano com o id: ' + id,
        HttpStatus.NOT_FOUND
      );

    if (id !== newPlan.id)
      throw new HttpException(
        'Não autorizado, dados para update incosistentes.',
        HttpStatus.UNAUTHORIZED
      );

    // Verificar se o novo nome já existe (se foi alterado)
    if (plan.name !== newPlan.name) {
      const existingPlan = await this.planRepository.findByName(newPlan.name);
      if (existingPlan && existingPlan.id !== id) {
        throw new HttpException(
          'Já existe um plano com este nome',
          HttpStatus.CONFLICT
        );
      }
    }

    const updatedPlan = await this.planRepository.update(id, {
      ...plan,
      ...newPlan
    });

    this.logger.log(this.logContextName, 'Plano atualizado com sucesso');

    return updatedPlan;
  }
}
