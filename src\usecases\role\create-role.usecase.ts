import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { RoleEntity } from '../../domain/entities/role.entity';
import { CreateRoleDto } from '../../infrastructure/controllers/role/role.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { RoleRepository } from '../../infrastructure/repositories/role-repository';

@Injectable()
export class CreateRoleUseCase {
  constructor(
    private readonly roleRepository: RoleRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'CREATE_ROLE_USE_CASE';

  async execute(role: CreateRoleDto): Promise<RoleEntity> {
    this.logger.log(this.logContextName, 'Iniciando criação de role');

    const roleAlreadyExists = await this.roleRepository.findByKey(role.key);

    if (roleAlreadyExists)
      throw new HttpException(
        'Já existe uma role com a key: ' + role.key,
        HttpStatus.CONFLICT
      );

    const newRole = await this.roleRepository.insert(role);

    this.logger.log(this.logContextName, 'Permissão criada com sucesso');

    return newRole;
  }
}
