import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { RoleRepository } from '../../infrastructure/repositories/role-repository';

@Injectable()
export class DeleteRoleUseCase {
  constructor(
    private readonly roleRepository: RoleRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'DELETE_ROLE_USE_CASE';

  async execute(id: string): Promise<void> {
    this.logger.log(this.logContextName, 'Iniciando exclusão de role');

    const role = await this.roleRepository.findById(id);
    if (!role) throw new HttpException('Role não existe', HttpStatus.NOT_FOUND);

    await this.roleRepository.delete(id);
    this.logger.log(this.logContextName, 'Role excluída com sucesso');
  }
}
