import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { RolePresenter } from '../../infrastructure/controllers/role/role.presenter';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { RoleRepository } from '../../infrastructure/repositories/role-repository';

@Injectable()
export class GetRoleUseCase {
  constructor(
    private readonly roleRepository: RoleRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_ROLE_USE_CASE';

  async execute(id: string): Promise<RolePresenter> {
    this.logger.log(this.logContextName, 'Iniciando busca de role');
    const role = await this.roleRepository.findById(id);
    if (!role) throw new HttpException('Role not found', HttpStatus.NOT_FOUND);

    return role;
  }
}
