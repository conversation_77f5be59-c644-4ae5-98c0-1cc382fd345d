import { Injectable } from '@nestjs/common';
import { RolePresenter } from '../../infrastructure/controllers/role/role.presenter';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { RoleRepository } from '../../infrastructure/repositories/role-repository';

@Injectable()
export class GetRolesUseCase {
  constructor(
    private readonly roleRepository: RoleRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_ROLES_USE_CASE';

  async execute(): Promise<RolePresenter[]> {
    this.logger.log(this.logContextName, 'Iniciando busca de permissões');
    const role = await this.roleRepository.findAll();

    return role;
  }
}
