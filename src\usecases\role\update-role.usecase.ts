import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { RoleEntity } from '../../domain/entities/role.entity';
import { UpdateRoleDto } from '../../infrastructure/controllers/role/role.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { RoleRepository } from '../../infrastructure/repositories/role-repository';

@Injectable()
export class UpdateRoleUseCase {
  constructor(
    private readonly roleRepository: RoleRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'UPDATE_PERMISSION_USE_CASE';

  async execute(id: string, newRole: UpdateRoleDto): Promise<RoleEntity> {
    this.logger.log(this.logContextName, 'Iniciando atualização de role');

    const role = await this.roleRepository.findById(id);

    if (!role)
      throw new HttpException(
        'Não existe uma role com o id: ' + id,
        HttpStatus.NOT_FOUND
      );

    if (id !== newRole.id)
      throw new HttpException(
        'Não autorizado, dados para update incosistentes.',
        HttpStatus.UNAUTHORIZED
      );

    const alreadyExistsRoleKey = await this.roleRepository.findByKey(
      newRole.key
    );

    if (alreadyExistsRoleKey)
      throw new HttpException(
        'Já existe uma role com a key: ' + newRole.key,
        HttpStatus.CONFLICT
      );

    const updatedRole = await this.roleRepository.update(id, {
      ...role,
      ...newRole
    });

    this.logger.log(this.logContextName, 'Permissão criada com sucesso');

    return updatedRole;
  }
}
