import { Test, TestingModule } from '@nestjs/testing';
import { RolePermissionEntity } from '../../domain/entities/role_permissions.entity';
import { CreateRolePermissionDto } from '../../infrastructure/controllers/role-permission/role-permission.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { RolePermissionRepository } from '../../infrastructure/repositories/role_permission-repository';
import { CreateRolePermissionUseCase } from './create-role-permission.usecase';

const mockRolePermissionRepository = {
  insert: jest.fn()
};

const mockLoggerService = {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn()
};

describe('CreateRolePermissionUseCase', () => {
  let useCase: CreateRolePermissionUseCase;

  beforeEach(async () => {
    jest.clearAllMocks();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateRolePermissionUseCase,
        {
          provide: RolePermissionRepository,
          useValue: mockRolePermissionRepository
        },
        { provide: LoggerService, useValue: mockLoggerService }
      ]
    }).compile();

    useCase = module.get<CreateRolePermissionUseCase>(
      CreateRolePermissionUseCase
    );
  });

  const createRolePermissionDto: CreateRolePermissionDto = {
    role_id: 'role-id-123',
    permission_id: 'permission-id-123'
  };

  const rolePermissionEntity: RolePermissionEntity = {
    id: 'role-permission-id-123',
    ...createRolePermissionDto,
    created_by: 'system',
    updated_by: 'system',
    created_at: new Date(),
    updated_at: new Date()
  };

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should create a role permission successfully', async () => {
      mockRolePermissionRepository.insert.mockResolvedValue(
        rolePermissionEntity
      );

      const result = await useCase.execute(createRolePermissionDto);

      expect(mockLoggerService.log).toHaveBeenCalledWith(
        'CREATE_ROLE_PERMISSION_USE_CASE',
        'Iniciando criação de role permission'
      );
      expect(mockRolePermissionRepository.insert).toHaveBeenCalledWith(
        createRolePermissionDto
      );
      expect(mockLoggerService.log).toHaveBeenCalledWith(
        'CREATE_ROLE_PERMISSION_USE_CASE',
        'Role permission criada com sucesso'
      );
      expect(result).toEqual(rolePermissionEntity);
    });
  });
});
