import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { RolePermissionEntity } from '../../domain/entities/role_permissions.entity';
import { CreateRolePermissionDto } from '../../infrastructure/controllers/role-permission/role-permission.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { RolePermissionRepository } from '../../infrastructure/repositories/role_permission-repository';

@Injectable()
export class CreateRolePermissionUseCase {
  constructor(
    private readonly rolePermissionRepository: RolePermissionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'CREATE_ROLE_PERMISSION_USE_CASE';

  async execute(
    rolePermission: CreateRolePermissionDto
  ): Promise<RolePermissionEntity> {
    this.logger.log(
      this.logContextName,
      'Iniciando criação de role permission'
    );

    const newRolePermission =
      await this.rolePermissionRepository.insert(rolePermission);

    this.logger.log(this.logContextName, 'Role permission criada com sucesso');

    return newRolePermission;
  }
}
