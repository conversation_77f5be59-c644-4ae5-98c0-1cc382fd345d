import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { RolePermissionRepository } from '../../infrastructure/repositories/role_permission-repository';

@Injectable()
export class DeleteRolePermissionUseCase {
  constructor(
    private readonly rolePermissionRepository: RolePermissionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'DELETE_ROLE_PERMISSION_USE_CASE';

  async execute(id: string): Promise<void> {
    this.logger.log(
      this.logContextName,
      'Iniciando exclusão de role permission'
    );

    const rolePermission = await this.rolePermissionRepository.findById(id);

    if (!rolePermission)
      throw new HttpException(
        'Não existe uma role permission com o id: ' + id,
        HttpStatus.NOT_FOUND
      );

    await this.rolePermissionRepository.delete(id);

    this.logger.log(
      this.logContextName,
      'Role permission excluída com sucesso'
    );
  }
}
