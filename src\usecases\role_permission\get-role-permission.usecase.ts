import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { RolePermissionPresenter } from '../../infrastructure/controllers/role-permission/role-permission.presenter';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { RolePermissionRepository } from '../../infrastructure/repositories/role_permission-repository';

@Injectable()
export class GetRolePermissionUseCase {
  constructor(
    private readonly rolePermissionRepository: RolePermissionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_ROLE_PERMISSION_USE_CASE';

  async execute(id: string): Promise<RolePermissionPresenter> {
    this.logger.log(this.logContextName, 'Iniciando busca de role permission');
    const rolePermission = await this.rolePermissionRepository.findById(id);
    if (!rolePermission)
      throw new HttpException(
        'Role Permission not found',
        HttpStatus.NOT_FOUND
      );

    return rolePermission;
  }
}
