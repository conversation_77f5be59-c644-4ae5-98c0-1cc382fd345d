import { Injectable } from '@nestjs/common';
import { RolePermissionPresenter } from '../../infrastructure/controllers/role-permission/role-permission.presenter';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { RolePermissionRepository } from '../../infrastructure/repositories/role_permission-repository';

@Injectable()
export class GetRolePermissionsUseCase {
  constructor(
    private readonly rolePermissionRepository: RolePermissionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_ROLE_PERMISSIONS_USE_CASE';

  async execute(): Promise<RolePermissionPresenter[]> {
    this.logger.log(this.logContextName, 'Iniciando busca de role permissions');
    const rolePermissions = await this.rolePermissionRepository.findAll();

    return rolePermissions;
  }
}
