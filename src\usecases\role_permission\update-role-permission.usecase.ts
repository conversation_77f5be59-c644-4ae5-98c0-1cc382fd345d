import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { RolePermissionEntity } from '../../domain/entities/role_permissions.entity';
import { UpdateRolePermissionDto } from '../../infrastructure/controllers/role-permission/role-permission.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { RolePermissionRepository } from '../../infrastructure/repositories/role_permission-repository';

@Injectable()
export class UpdateRolePermissionUseCase {
  constructor(
    private readonly rolePermissionRepository: RolePermissionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'UPDATE_ROLE_PERMISSION_USE_CASE';

  async execute(
    id: string,
    newRolePermission: UpdateRolePermissionDto
  ): Promise<RolePermissionEntity> {
    this.logger.log(
      this.logContextName,
      'Iniciando atualização de role permission'
    );

    const rolePermission = await this.rolePermissionRepository.findById(id);

    if (!rolePermission)
      throw new HttpException(
        'Não existe uma role permission com o id: ' + id,
        HttpStatus.NOT_FOUND
      );

    if (id !== newRolePermission.id)
      throw new HttpException(
        'Não autorizado, dados para update incosistentes.',
        HttpStatus.UNAUTHORIZED
      );

    const updatedRolePermission = await this.rolePermissionRepository.update(
      id,
      {
        ...rolePermission,
        ...newRolePermission
      }
    );

    this.logger.log(
      this.logContextName,
      'Role permission atualizada com sucesso'
    );

    return updatedRolePermission;
  }
}
