import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { SubscriptionEntity } from '../../domain/entities/subscription.entity';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { SubscriptionRepository } from '../../infrastructure/repositories/subscription-repository';

@Injectable()
export class CancelSubscriptionUseCase {
  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'CANCEL_SUBSCRIPTION_USE_CASE';

  async execute(id: string, canceledBy: string): Promise<SubscriptionEntity> {
    this.logger.log(
      this.logContextName,
      'Iniciando cancelamento de assinatura'
    );

    const subscription = await this.subscriptionRepository.findById(id);

    if (!subscription)
      throw new HttpException(
        'Não existe uma assinatura com o id: ' + id,
        HttpStatus.NOT_FOUND
      );

    if (subscription.status === 'canceled') {
      throw new HttpException(
        'Assinatura já está cancelada',
        HttpStatus.BAD_REQUEST
      );
    }

    if (subscription.status === 'expired') {
      throw new HttpException(
        'Assinatura já está expirada',
        HttpStatus.BAD_REQUEST
      );
    }

    const updatedSubscription = await this.subscriptionRepository.update(id, {
      ...subscription,
      status: 'canceled',
      canceled_at: new Date(),
      updated_by: canceledBy,
      updated_at: new Date()
    });

    this.logger.log(this.logContextName, 'Assinatura cancelada com sucesso');

    return updatedSubscription;
  }
}
