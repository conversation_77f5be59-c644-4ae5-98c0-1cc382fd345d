import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { SubscriptionEntity } from '../../domain/entities/subscription.entity';
import { CreateSubscriptionDto } from '../../infrastructure/controllers/subscription/subscription.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { InstitutionRepository } from '../../infrastructure/repositories/institution-repository';
import { PlanRepository } from '../../infrastructure/repositories/plan-repository';
import { SubscriptionRepository } from '../../infrastructure/repositories/subscription-repository';

@Injectable()
export class CreateSubscriptionUseCase {
  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly institutionRepository: InstitutionRepository,
    private readonly planRepository: PlanRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'CREATE_SUBSCRIPTION_USE_CASE';

  async execute(
    subscription: CreateSubscriptionDto
  ): Promise<SubscriptionEntity> {
    this.logger.log(this.logContextName, 'Iniciando criação de assinatura');

    const institution = await this.institutionRepository.findById(
      subscription.institution_id
    );
    if (!institution) {
      throw new HttpException(
        'Instituição não encontrada',
        HttpStatus.NOT_FOUND
      );
    }

    const plan = await this.planRepository.findById(subscription.plan_id);
    if (!plan) {
      throw new HttpException('Plano não encontrado', HttpStatus.NOT_FOUND);
    }

    if (!plan.is_active) {
      throw new HttpException('Plano não está ativo', HttpStatus.BAD_REQUEST);
    }

    // Verificar se já existe uma assinatura ativa para esta instituição
    const existingActiveSubscription =
      await this.subscriptionRepository.findActiveByInstitutionId(
        subscription.institution_id
      );

    if (existingActiveSubscription) {
      throw new HttpException(
        'Instituição já possui uma assinatura ativa',
        HttpStatus.CONFLICT
      );
    }

    const newSubscription = await this.subscriptionRepository.insert({
      ...subscription,
      status: 'active'
    });

    if (newSubscription) {
      await this.institutionRepository.update(institution.id, {
        ...institution,
        subscription_id: newSubscription.id,
        updated_at: new Date(),
        status: 'active'
      });

      this.logger.log(
        this.logContextName,
        'Assinatura criada com sucesso: ' + newSubscription.id
      );
    }

    this.logger.log(this.logContextName, 'Assinatura criada com sucesso');

    return newSubscription;
  }
}
