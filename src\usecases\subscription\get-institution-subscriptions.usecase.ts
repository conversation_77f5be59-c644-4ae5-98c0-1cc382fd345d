import { Injectable } from '@nestjs/common';
import { SubscriptionPresenter } from '../../infrastructure/controllers/subscription/subscription.presenter';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { SubscriptionRepository } from '../../infrastructure/repositories/subscription-repository';

@Injectable()
export class GetInstitutionSubscriptionsUseCase {
  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_INSTITUTION_SUBSCRIPTIONS_USE_CASE';

  async execute(institutionId: string): Promise<SubscriptionPresenter[]> {
    this.logger.log(this.logContextName, 'Iniciando busca de assinaturas da instituição');
    const subscriptions = await this.subscriptionRepository.findByInstitutionId(institutionId);

    return subscriptions;
  }
}
