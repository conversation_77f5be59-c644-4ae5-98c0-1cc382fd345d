import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { SubscriptionPresenter } from '../../infrastructure/controllers/subscription/subscription.presenter';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { SubscriptionRepository } from '../../infrastructure/repositories/subscription-repository';

@Injectable()
export class GetSubscriptionUseCase {
  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_SUBSCRIPTION_USE_CASE';

  async execute(id: string): Promise<SubscriptionPresenter> {
    this.logger.log(this.logContextName, 'Iniciando busca de assinatura');
    const subscription = await this.subscriptionRepository.findById(id);
    if (!subscription)
      throw new HttpException('Subscription not found', HttpStatus.NOT_FOUND);

    return subscription;
  }
}
