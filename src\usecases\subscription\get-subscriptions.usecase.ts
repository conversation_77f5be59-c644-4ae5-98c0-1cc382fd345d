import { Injectable } from '@nestjs/common';
import { SubscriptionPresenter } from '../../infrastructure/controllers/subscription/subscription.presenter';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { SubscriptionRepository } from '../../infrastructure/repositories/subscription-repository';

@Injectable()
export class GetSubscriptionsUseCase {
  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_SUBSCRIPTIONS_USE_CASE';

  async execute(): Promise<SubscriptionPresenter[]> {
    this.logger.log(this.logContextName, 'Iniciando busca de assinaturas');
    const subscriptions = await this.subscriptionRepository.findAll();

    return subscriptions;
  }
}
