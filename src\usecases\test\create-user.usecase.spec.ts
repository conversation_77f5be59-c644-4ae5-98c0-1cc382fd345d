import { HttpException, HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { UserEntity } from '../../domain/entities/user.entity';
import { CreateUserDto } from '../../infrastructure/controllers/user/user.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRepository } from '../../infrastructure/repositories/user-repository';
import { TwoFactorAuthenticationService } from '../../infrastructure/services/2fa/two-factor-authentication.service';
import { EmailService } from '../../infrastructure/services/email/email.service';
import { JwtTokenService } from '../../infrastructure/services/jwt/jwt.service';
import { CreateUserUseCase } from '../user/create-user.usecase';

const mockUserRepository = {
  findByEmail: jest.fn(),
  findByTaxId: jest.fn(),
  insert: jest.fn()
};

const mockTwoFactorAuthenticationService = {
  generateSecret: jest.fn()
};

const mockJwtTokenService = {
  createFirstAccessToken: jest.fn()
};

const mockLoggerService = {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn()
};

const mockEmailService = {
  sendEmail: jest.fn()
};

describe('CreateUserUseCase', () => {
  let useCase: CreateUserUseCase;

  beforeEach(async () => {
    jest.clearAllMocks();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateUserUseCase,
        { provide: UserRepository, useValue: mockUserRepository },
        {
          provide: TwoFactorAuthenticationService,
          useValue: mockTwoFactorAuthenticationService
        },
        { provide: JwtTokenService, useValue: mockJwtTokenService },
        { provide: LoggerService, useValue: mockLoggerService },
        { provide: EmailService, useValue: mockEmailService }
      ]
    }).compile();

    useCase = module.get<CreateUserUseCase>(CreateUserUseCase);
  });

  const createUserDto: CreateUserDto = {
    name: 'Test User',
    email: '<EMAIL>',
    phone_number: '**********',
    tax_id: '**********0',
    birth_date: new Date('1990-01-01'),
    institution_id: 'inst-123'
  };

  const userEntity: UserEntity = {
    id: 'user-id-123',
    ...createUserDto,
    secret_key_2fa: 'secret',
    is_enabled_2fa_app: false,
    is_email_verified: false,
    is_phone_verified: false,
    actived: true,
    created_by: 'system',
    created_at: new Date(),
    updated_at: new Date(),
    password_hash: '',
    updated_by: ''
  };

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should create a user successfully', async () => {
      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserRepository.findByTaxId.mockResolvedValue(null);
      mockTwoFactorAuthenticationService.generateSecret.mockReturnValue(
        'generated-secret'
      );
      mockUserRepository.insert.mockResolvedValue(userEntity);
      mockJwtTokenService.createFirstAccessToken.mockReturnValue(
        'fake-jwt-token'
      );

      const result = await useCase.execute(createUserDto);

      expect(mockLoggerService.log).toHaveBeenCalledWith(
        'CREATE_USER_USE_CASE',
        'Iniciando criação de usuário'
      );
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(
        createUserDto.email
      );
      expect(mockUserRepository.findByTaxId).toHaveBeenCalledWith(
        createUserDto.tax_id
      );
      expect(
        mockTwoFactorAuthenticationService.generateSecret
      ).toHaveBeenCalled();
      expect(mockUserRepository.insert).toHaveBeenCalledWith({
        name: createUserDto.name,
        email: createUserDto.email,
        phone_number: createUserDto.phone_number,
        tax_id: createUserDto.tax_id,
        birth_date: createUserDto.birth_date,
        institution_id: createUserDto.institution_id,
        secret_key_2fa: 'generated-secret'
      });
      expect(mockJwtTokenService.createFirstAccessToken).toHaveBeenCalledWith({
        email: userEntity.email,
        name: userEntity.name,
        institution_id: userEntity.institution_id,
        sub: userEntity.id
      });
      expect(mockEmailService.sendEmail).toHaveBeenCalledWith(
        userEntity.email,
        'EduSys - Bem vindo a nossa plataforma',
        'first-access',
        {
          name: userEntity.name,
          link: `https://teste.com.br/fake-jwt-token`,
          expires: '30 minutos',
          messageId: '*********'
        }
      );
      expect(result).toEqual(userEntity);
    });

    it('should throw HttpException if email already exists', async () => {
      mockUserRepository.findByEmail.mockResolvedValue(userEntity);

      await expect(useCase.execute(createUserDto)).rejects.toThrow(
        new HttpException(
          'Já existe um usuário registrado para o email informado.',
          HttpStatus.CONFLICT
        )
      );
      expect(mockLoggerService.error).toHaveBeenCalledWith(
        'CREATE_USER_USE_CASE',
        'Usuário já existe'
      );
      expect(mockUserRepository.findByTaxId).not.toHaveBeenCalled();
      expect(mockUserRepository.insert).not.toHaveBeenCalled();
      expect(mockEmailService.sendEmail).not.toHaveBeenCalled();
    });

    it('should throw HttpException if tax ID already exists', async () => {
      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserRepository.findByTaxId.mockResolvedValue(userEntity);

      await expect(useCase.execute(createUserDto)).rejects.toThrow(
        new HttpException(
          'Já existe um usuário registrado para o documento informado.',
          HttpStatus.CONFLICT
        )
      );
      expect(mockLoggerService.error).toHaveBeenCalledWith(
        'CREATE_USER_USE_CASE',
        'Usuário já existe'
      );
      expect(mockUserRepository.insert).not.toHaveBeenCalled();
      expect(mockEmailService.sendEmail).not.toHaveBeenCalled();
    });
  });
});
