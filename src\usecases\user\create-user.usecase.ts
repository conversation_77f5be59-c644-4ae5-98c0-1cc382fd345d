import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UserEntity } from '../../domain/entities/user.entity';
import { CreateUserDto } from '../../infrastructure/controllers/user/user.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRepository } from '../../infrastructure/repositories/user-repository';
import { TwoFactorAuthenticationService } from '../../infrastructure/services/2fa/two-factor-authentication.service';
import { EmailService } from '../../infrastructure/services/email/email.service';
import { JwtTokenService } from '../../infrastructure/services/jwt/jwt.service';

@Injectable()
export class CreateUserUseCase {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly twoFactorAuthenticationService: TwoFactorAuthenticationService,
    private readonly jwtTokenService: JwtTokenService,
    private readonly logger: LoggerService,
    private readonly emailService: EmailService
  ) {}
  private readonly logContextName: string = 'CREATE_USER_USE_CASE';

  async execute(user: CreateUserDto): Promise<UserEntity> {
    this.logger.log(this.logContextName, 'Iniciando criação de usuário');

    let userAlreadyExists: UserEntity | null = null;

    userAlreadyExists = await this.userRepository.findByEmail(user.email);
    if (userAlreadyExists) this.AlreadyExistsException('email');
    userAlreadyExists = await this.userRepository.findByTaxId(user.tax_id);
    if (userAlreadyExists) this.AlreadyExistsException('documento');

    const secret_key_2fa = this.twoFactorAuthenticationService.generateSecret();

    const newUser = {
      name: user.name,
      email: user.email,
      phone_number: user.phone_number,
      tax_id: user.tax_id,
      birth_date: user.birth_date,
      institution_id: user.institution_id,
      secret_key_2fa: secret_key_2fa
    };
    const insertedUser = await this.userRepository.insert(newUser);

    const token = this.jwtTokenService.createFirstAccessToken({
      email: insertedUser.email,
      name: insertedUser.name,
      institution_id: insertedUser.institution_id,
      sub: insertedUser.id
    });

    this.emailService.sendEmail(
      insertedUser.email,
      'EduSys - Bem vindo a nossa plataforma',
      'first-access',
      {
        name: insertedUser.name,
        link: `${process.env.FIRST_LOGIN_URL}/${token}`,
        expires: '30 minutos',
        messageId: '*********'
      }
    );

    return insertedUser;
  }

  private AlreadyExistsException(dataType: string) {
    this.logger.error(this.logContextName, 'Usuário já existe');
    throw new HttpException(
      `Já existe um usuário registrado para o ${dataType} informado.`,
      HttpStatus.CONFLICT
    );
  }
}
