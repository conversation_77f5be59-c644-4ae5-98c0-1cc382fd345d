import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRepository } from '../../infrastructure/repositories/user-repository';
import { TwoFactorAuthenticationService } from '../../infrastructure/services/2fa/two-factor-authentication.service';

@Injectable()
export class DeleteUserUseCase {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly logger: LoggerService,
    private readonly twoFactorAuthenticationService: TwoFactorAuthenticationService
  ) {}
  private readonly logContextName: string = 'DELETE_USER_USE_CASE';

  async execute(id: string, token: string) {
    this.logger.log(this.logContextName, 'Iniciando exclusão de usuário');

    const user = await this.userRepository.findById(id);

    if (!user)
      throw new HttpException(
        'Usuário com o id: ' + id + ' não encontrado.',
        HttpStatus.NOT_FOUND
      );

    const { isValid: twoFactorAuthenticationTokenIsValid } =
      this.twoFactorAuthenticationService.verifyToken(
        process.env.JWT_SECRET || '',
        token
      );

    if (!twoFactorAuthenticationTokenIsValid)
      throw new HttpException(
        'Token de autenticação informado é inválido.',
        HttpStatus.UNAUTHORIZED
      );

    this.userRepository.delete(id);
  }
}
