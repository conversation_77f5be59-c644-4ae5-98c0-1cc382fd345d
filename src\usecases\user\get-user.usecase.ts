import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UserEntity } from '../../domain/entities/user.entity';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRepository } from '../../infrastructure/repositories/user-repository';

@Injectable()
export class GetUserUseCase {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_USER_USE_CASE';

  async execute(id: string): Promise<UserEntity> {
    this.logger.log(this.logContextName, 'Iniciando busca de usuário');
    const user = await this.userRepository.findById(id);
    if (!user) throw new HttpException('User not found', HttpStatus.NOT_FOUND);

    return user;
  }
}
