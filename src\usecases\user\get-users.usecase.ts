import { Injectable } from '@nestjs/common';
import { UserEntity } from '../../domain/entities/user.entity';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRepository } from '../../infrastructure/repositories/user-repository';

@Injectable()
export class GetUsersUseCase {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_USERS_USE_CASE';

  async execute(): Promise<UserEntity[]> {
    this.logger.log(
      this.logContextName,
      'Iniciando busca da lista de usuários'
    );
    const users = await this.userRepository.findAll();
    return users;
  }
}
