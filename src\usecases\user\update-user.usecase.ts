import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UserEntity } from '../../domain/entities/user.entity';
import { UpdateUserDto } from '../../infrastructure/controllers/user/user.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRepository } from '../../infrastructure/repositories/user-repository';
import { TwoFactorAuthenticationService } from '../../infrastructure/services/2fa/two-factor-authentication.service';

@Injectable()
export class UpdateUserUseCase {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly twoFactorAuthenticationService: TwoFactorAuthenticationService,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'UPDATE_USER_USE_CASE';

  async execute(
    id: string,
    newUserData: UpdateUserDto,
    token: string
  ): Promise<UserEntity> {
    this.logger.log(this.logContextName, 'Iniciando update de usuário');

    const { isValid: twoFactorAuthenticationTokenIsValid } =
      this.twoFactorAuthenticationService.verifyToken(
        process.env.JWT_SECRET || '',
        token
      );

    if (!twoFactorAuthenticationTokenIsValid)
      throw new HttpException(
        'Token de autenticação informado é inválido.',
        HttpStatus.UNAUTHORIZED
      );

    if (id !== newUserData.id)
      throw new HttpException(
        'Não autorizado, dados para update incosistentes.',
        HttpStatus.UNAUTHORIZED
      );

    const user = await this.userRepository.findById(id);

    if (!user)
      throw new HttpException(
        'Usuário com o id: ' + id + ' não encontrado.',
        HttpStatus.NOT_FOUND
      );

    if (user?.email !== newUserData.email) {
      const userAlreadyExists = await this.userRepository.findByEmail(
        newUserData.email
      );

      if (userAlreadyExists)
        throw new HttpException(
          'Usuário já cadastrado com este email.',
          HttpStatus.CONFLICT
        );
    }

    const updatedUser = await this.userRepository.update(id, {
      ...user,
      ...newUserData,
      tax_id: user.tax_id
    });

    return updatedUser;
  }
}
