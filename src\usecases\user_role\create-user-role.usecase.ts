import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UserRoleEntity } from '../../domain/entities/user_role.entity';
import { CreateUserRoleDto } from '../../infrastructure/controllers/user-role/user-role.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRoleRepository } from '../../infrastructure/repositories/user_role-repository';

@Injectable()
export class CreateUserRoleUseCase {
  constructor(
    private readonly userRoleRepository: UserRoleRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'CREATE_USER_ROLE_USE_CASE';

  async execute(userRole: CreateUserRoleDto): Promise<UserRoleEntity> {
    this.logger.log(this.logContextName, 'Iniciando criação de user role');

    const newUserRole = await this.userRoleRepository.insert(userRole);

    this.logger.log(this.logContextName, 'User role criada com sucesso');

    return newUserRole;
  }
}
