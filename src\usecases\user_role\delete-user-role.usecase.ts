import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRoleRepository } from '../../infrastructure/repositories/user_role-repository';

@Injectable()
export class DeleteUserRoleUseCase {
  constructor(
    private readonly userRoleRepository: UserRoleRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'DELETE_USER_ROLE_USE_CASE';

  async execute(id: string): Promise<void> {
    this.logger.log(this.logContextName, 'Iniciando exclusão de user role');

    const userRole = await this.userRoleRepository.findById(id);

    if (!userRole)
      throw new HttpException(
        'Não existe uma user role com o id: ' + id,
        HttpStatus.NOT_FOUND
      );

    await this.userRoleRepository.delete(id);

    this.logger.log(this.logContextName, 'User role excluída com sucesso');
  }
}
