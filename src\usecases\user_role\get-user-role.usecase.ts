import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UserRolePresenter } from '../../infrastructure/controllers/user-role/user-role.presenter';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRoleRepository } from '../../infrastructure/repositories/user_role-repository';

@Injectable()
export class GetUserRoleUseCase {
  constructor(
    private readonly userRoleRepository: UserRoleRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_USER_ROLE_USE_CASE';

  async execute(id: string): Promise<UserRolePresenter> {
    this.logger.log(this.logContextName, 'Iniciando busca de user role');
    const userRole = await this.userRoleRepository.findById(id);
    if (!userRole)
      throw new HttpException('User Role not found', HttpStatus.NOT_FOUND);

    return userRole;
  }
}
