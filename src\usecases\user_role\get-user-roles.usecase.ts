import { Injectable } from '@nestjs/common';
import { UserRolePresenter } from '../../infrastructure/controllers/user-role/user-role.presenter';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRoleRepository } from '../../infrastructure/repositories/user_role-repository';

@Injectable()
export class GetUserRolesUseCase {
  constructor(
    private readonly userRoleRepository: UserRoleRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'GET_USER_ROLES_USE_CASE';

  async execute(): Promise<UserRolePresenter[]> {
    this.logger.log(this.logContextName, 'Iniciando busca de user roles');
    const userRoles = await this.userRoleRepository.findAll();

    return userRoles;
  }
}
