import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UserRoleEntity } from '../../domain/entities/user_role.entity';
import { UpdateUserRoleDto } from '../../infrastructure/controllers/user-role/user-role.dto';
import { LoggerService } from '../../infrastructure/logger/logger.service';
import { UserRoleRepository } from '../../infrastructure/repositories/user_role-repository';

@Injectable()
export class UpdateUserRoleUseCase {
  constructor(
    private readonly userRoleRepository: UserRoleRepository,
    private readonly logger: LoggerService
  ) {}
  private readonly logContextName: string = 'UPDATE_USER_ROLE_USE_CASE';

  async execute(
    id: string,
    newUserRole: UpdateUserRoleDto
  ): Promise<UserRoleEntity> {
    this.logger.log(this.logContextName, 'Iniciando atualização de user role');

    const userRole = await this.userRoleRepository.findById(id);

    if (!userRole)
      throw new HttpException(
        'Não existe uma user role com o id: ' + id,
        HttpStatus.NOT_FOUND
      );

    if (id !== newUserRole.id)
      throw new HttpException(
        'Não autorizado, dados para update incosistentes.',
        HttpStatus.UNAUTHORIZED
      );

    const updatedUserRole = await this.userRoleRepository.update(id, {
      ...userRole,
      ...newUserRole
    });

    this.logger.log(this.logContextName, 'User role atualizada com sucesso');

    return updatedUserRole;
  }
}
